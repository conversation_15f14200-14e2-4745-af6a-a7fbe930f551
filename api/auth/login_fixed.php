<?php
require_once '../config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendError('Method not allowed', 405);
    }
    
    $data = getRequestData();
    
    validateRequired($data, ['username', 'password']);
    
    $username = sanitizeInput($data['username']);
    $password = sanitizeInput($data['password']);
    
    // Try to find user by username first (removed is_active check)
    $query = "SELECT * FROM users WHERE username = ? AND password = ?";
    $user = $database->getOne($query, [$username, $password]);
    
    // If not found, try by email (removed is_active check)
    if (!$user) {
        $query = "SELECT * FROM users WHERE email = ? AND password = ?";
        $user = $database->getOne($query, [strtolower($username), $password]);
    }
    
    if (!$user) {
        sendError('Invalid username/email or password', 401);
    }
    
    // Update last login
    $updateQuery = "UPDATE users SET updated_at = NOW() WHERE id = ?";
    $database->executeQuery($updateQuery, [$user['id']]);
    
    // Store user session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['user_name'] = $user['name'];
    
    // Remove password from response
    unset($user['password']);
    
    // Format response to match frontend expectations
    $response = [
        'id' => $user['id'],
        'email' => $user['email'],
        'username' => $user['username'],
        'name' => $user['name'],
        'role' => $user['role'],
        'createdAt' => $user['created_at'],
        'lastLogin' => date('c'), // Current time in ISO format
        'isActive' => true // Default to true since we don't have this column
    ];
    
    sendResponse($response);
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    sendError('Login failed', 500);
}
?>
