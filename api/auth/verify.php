<?php
require_once '../config/database.php';

session_start();

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendError('Method not allowed', 405);
    }
    
    if (!isset($_SESSION['user_id'])) {
        sendError('Not authenticated', 401);
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Get current user data
    $query = "SELECT * FROM users WHERE id = ? AND is_active = 1";
    $user = $database->getOne($query, [$_SESSION['user_id']]);
    
    if (!$user) {
        session_destroy();
        sendError('User not found', 401);
    }
    
    // Remove password from response
    unset($user['password']);
    
    // Format response to match frontend expectations
    $response = [
        'id' => $user['id'],
        'email' => $user['email'],
        'username' => $user['username'],
        'name' => $user['name'],
        'role' => $user['role'],
        'createdAt' => $user['created_at'],
        'lastLogin' => $user['last_login'],
        'isActive' => (bool)$user['is_active']
    ];
    
    sendResponse($response);
    
} catch (Exception $e) {
    error_log("Verify error: " . $e->getMessage());
    sendError('Verification failed', 500);
}
?>
