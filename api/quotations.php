<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            handleGenerateQuote($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Quotations API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGenerateQuote($database) {
    $data = getRequestData();
    
    validateRequired($data, ['booking']);
    
    $booking = $data['booking'];
    
    // Generate HTML for the quote
    $html = generateQuoteHTML($booking, $database);
    
    // For now, return the HTML. In production, you might want to use a library like TCPDF or mPDF
    // to generate actual PDF files
    sendResponse([
        'html' => $html,
        'quoteNumber' => $booking['quoteNumber'],
        'message' => 'Quote generated successfully'
    ]);
}

function generateQuoteHTML($booking, $database) {
    // Get company settings
    $settingsQuery = "SELECT * FROM system_settings WHERE id = ?";
    $settings = $database->getOne($settingsQuery, ['general']);
    
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Quote #' . htmlspecialchars($booking['quoteNumber']) . '</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .company-name { font-size: 24px; font-weight: bold; color: #333; }
            .quote-title { font-size: 20px; margin: 20px 0; }
            .quote-info { margin-bottom: 30px; }
            .customer-info { margin-bottom: 30px; }
            .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
            .info-row { margin-bottom: 5px; }
            .products-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            .products-table th, .products-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .products-table th { background-color: #f2f2f2; font-weight: bold; }
            .totals { float: right; width: 300px; }
            .totals-table { width: 100%; }
            .totals-table td { padding: 5px; }
            .total-row { font-weight: bold; font-size: 16px; border-top: 2px solid #333; }
            .footer { clear: both; margin-top: 50px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="company-name">TeamWork Rental Services</div>
            <div class="quote-title">RENTAL QUOTATION</div>
        </div>
        
        <div class="quote-info">
            <div class="section-title">Quote Information</div>
            <div class="info-row"><strong>Quote Number:</strong> ' . htmlspecialchars($booking['quoteNumber']) . '</div>
            <div class="info-row"><strong>Date:</strong> ' . date('F j, Y', strtotime($booking['date'])) . '</div>
            <div class="info-row"><strong>Rental Period:</strong> ' . $booking['rentalPeriod']['days'] . ' days (' . ucfirst($booking['rentalPeriod']['rentalType']) . ' rate)</div>
        </div>
        
        <div class="customer-info">
            <div class="section-title">Customer Information</div>
            <div class="info-row"><strong>Name:</strong> ' . htmlspecialchars($booking['customer']['name']) . '</div>
            <div class="info-row"><strong>Email:</strong> ' . htmlspecialchars($booking['customer']['email']) . '</div>';
    
    if (!empty($booking['customer']['phone'])) {
        $html .= '<div class="info-row"><strong>Phone:</strong> ' . htmlspecialchars($booking['customer']['phone']) . '</div>';
    }
    
    if (!empty($booking['customer']['address'])) {
        $html .= '<div class="info-row"><strong>Address:</strong> ' . htmlspecialchars($booking['customer']['address']) . '</div>';
    }
    
    $html .= '
        </div>
        
        <div class="section-title">Rental Items</div>
        <table class="products-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Days</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>';
    
    // Get product details for each item
    foreach ($booking['products'] as $product) {
        $productQuery = "SELECT name FROM products WHERE id = ?";
        $productInfo = $database->getOne($productQuery, [$product['id']]);
        $productName = $productInfo ? $productInfo['name'] : 'Unknown Product';
        
        $rate = $booking['rentalPeriod']['rentalType'] === 'weekly' && $product['weeklyRate'] 
                ? $product['weeklyRate'] 
                : $product['dailyRate'];
        
        $html .= '
                <tr>
                    <td>' . htmlspecialchars($productName) . '</td>
                    <td>' . $product['quantity'] . '</td>
                    <td>$' . number_format($rate, 2) . '</td>
                    <td>' . $booking['rentalPeriod']['days'] . '</td>
                    <td>$' . number_format($product['totalPrice'], 2) . '</td>
                </tr>';
    }
    
    $html .= '
            </tbody>
        </table>
        
        <div class="totals">
            <table class="totals-table">
                <tr>
                    <td>Subtotal:</td>
                    <td>$' . number_format($booking['subtotal'], 2) . '</td>
                </tr>';
    
    if ($booking['deliveryFee'] > 0) {
        $html .= '
                <tr>
                    <td>Delivery Fee:</td>
                    <td>$' . number_format($booking['deliveryFee'], 2) . '</td>
                </tr>';
    }
    
    if ($booking['discount'] > 0) {
        $html .= '
                <tr>
                    <td>Discount:</td>
                    <td>-$' . number_format($booking['discount'], 2) . '</td>
                </tr>';
    }
    
    if ($booking['tax'] > 0) {
        $html .= '
                <tr>
                    <td>Tax:</td>
                    <td>$' . number_format($booking['tax'], 2) . '</td>
                </tr>';
    }
    
    $html .= '
                <tr class="total-row">
                    <td>Total:</td>
                    <td>$' . number_format($booking['total'], 2) . '</td>
                </tr>
            </table>
        </div>
        
        <div class="footer">
            <p>Thank you for choosing TeamWork Rental Services!</p>
            <p>This quote is valid for 30 days from the date of issue.</p>
        </div>
    </body>
    </html>';
    
    return $html;
}
?>
