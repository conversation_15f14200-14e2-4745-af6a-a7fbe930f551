<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Clients API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    // Get client statistics from bookings since we don't have a separate clients table
    $query = "
        SELECT
            customer_email as email,
            customer_name as name,
            customer_phone as phone,
            customer_address as address,
            COUNT(*) as totalBookings,
            SUM(total) as totalSpent,
            SUM(CASE WHEN status = 'pending' THEN remaining_amount ELSE 0 END) as pendingPayment,
            MAX(date) as lastBooking,
            MIN(date) as dateAdded
        FROM bookings
        WHERE deleted_at IS NULL
        GROUP BY customer_email, customer_name, customer_phone
        ORDER BY MAX(date) DESC
    ";
    $clients = $database->getMany($query);
    
    // Format clients to match frontend expectations
    $formattedClients = array_map(function($client) {
        return [
            'id' => md5($client['email']), // Generate ID from email
            'name' => $client['name'],
            'email' => $client['email'],
            'phone' => $client['phone'] ?? '',
            'address' => $client['address'] ?? '',
            'dateAdded' => $client['dateAdded'],
            'totalBookings' => (int)$client['totalBookings'],
            'totalSpent' => (float)$client['totalSpent'],
            'pendingPayment' => (float)$client['pendingPayment'],
            'lastBooking' => $client['lastBooking'],
            'notes' => ''
        ];
    }, $clients);
    
    sendResponse($formattedClients);
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['name', 'email']);
    
    $email = strtolower(trim(sanitizeInput($data['email'])));
    
    // Check if client already exists
    $checkQuery = "SELECT id FROM clients WHERE email = ?";
    $existingClient = $database->getOne($checkQuery, [$email]);
    
    if ($existingClient) {
        sendError('A client with this email already exists');
    }
    
    $query = "INSERT INTO clients (id, name, email, phone, address, total_bookings, total_spent, pending_payment, notes)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $email, // Use email as ID for consistency
        sanitizeInput($data['name']),
        $email,
        sanitizeInput($data['phone'] ?? null),
        sanitizeInput($data['address'] ?? null),
        $data['totalBookings'] ?? 0,
        $data['totalSpent'] ?? 0,
        $data['pendingPayment'] ?? 0,
        sanitizeInput($data['notes'] ?? null)
    ];
    
    $database->executeQuery($query, $params);
    
    sendResponse(['id' => $email]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id', 'name']);
    
    $query = "UPDATE clients SET name = ?, phone = ?, address = ?, total_bookings = ?, 
              total_spent = ?, pending_payment = ?, last_booking = ?, notes = ?
              WHERE id = ?";
    
    // Convert ISO date to MySQL format for lastBooking
    $lastBooking = null;
    if (isset($data['lastBooking']) && $data['lastBooking']) {
        $lastBooking = date('Y-m-d H:i:s', strtotime($data['lastBooking']));
    }

    $params = [
        sanitizeInput($data['name']),
        sanitizeInput($data['phone'] ?? null),
        sanitizeInput($data['address'] ?? null),
        $data['totalBookings'] ?? 0,
        $data['totalSpent'] ?? 0,
        $data['pendingPayment'] ?? 0,
        $lastBooking,
        sanitizeInput($data['notes'] ?? null),
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($query, $params);
    
    if ($rowsAffected === 0) {
        sendError('Client not found', 404);
    }
    
    sendSuccess('Client updated successfully');
}

function handleDelete($database) {
    $email = $_GET['email'] ?? null;
    
    if (!$email) {
        sendError('Client email is required');
    }
    
    $email = strtolower(trim(sanitizeInput($email)));
    
    $query = "DELETE FROM clients WHERE email = ?";
    $rowsAffected = $database->delete($query, [$email]);
    
    if ($rowsAffected === 0) {
        sendError('Client not found', 404);
    }
    
    sendSuccess('Client deleted successfully');
}
?>
