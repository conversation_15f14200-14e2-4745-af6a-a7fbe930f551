<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Equipment API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT * FROM products ORDER BY name";
    $products = $database->getMany($query);

    // Format products to match frontend expectations with our simplified schema
    $formattedProducts = array_map(function($product) {
        return [
            'id' => $product['id'],
            'name' => $product['name'],
            'category' => $product['category'],
            'sku' => $product['sku'],
            'barcode' => '', // Not in our schema
            'dailyRate' => (float)$product['daily_rate'],
            'weeklyRate' => (float)$product['weekly_rate'],
            'image' => $product['image'],
            'description' => $product['description'],
            'available' => (bool)$product['available'],
            'quantity' => 1, // Default quantity
            'stock' => (int)$product['stock'],
            'featured' => false, // Not in our schema
            'customDays' => null, // Not in our schema
            'customPrice' => null, // Not in our schema
            'temporaryDailyRate' => null, // Not in our schema
            'temporaryWeeklyRate' => null, // Not in our schema
            'isExternalVendorItem' => false, // Not in our schema
            'vendorId' => null, // Not in our schema
            'vendorSku' => null, // Not in our schema
            'vendorCost' => null, // Not in our schema
            'profitMargin' => null // Not in our schema
        ];
    }, $products);

    sendResponse($formattedProducts);
}

function handlePost($database) {
    $data = getRequestData();

    validateRequired($data, ['name', 'category', 'sku', 'dailyRate']);

    // Generate a simple ID
    $id = uniqid('prod_', true);

    $query = "INSERT INTO products (
        id, name, category, sku, daily_rate, weekly_rate, image, description,
        available, stock
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $params = [
        $id,
        sanitizeInput($data['name']),
        sanitizeInput($data['category']),
        sanitizeInput($data['sku']),
        $data['dailyRate'],
        $data['weeklyRate'] ?? 0,
        sanitizeInput($data['image'] ?? ''),
        sanitizeInput($data['description'] ?? ''),
        ($data['available'] ?? true) ? 1 : 0,
        $data['stock'] ?? 1
    ];
    
    $database->executeQuery($query, $params);
    
    sendResponse(['id' => $id]);
}

function handlePut($database) {
    $data = getRequestData();

    validateRequired($data, ['id', 'name', 'category', 'sku', 'dailyRate']);

    $query = "UPDATE products SET
        name = ?, category = ?, sku = ?, daily_rate = ?, weekly_rate = ?,
        image = ?, description = ?, available = ?, stock = ?
    WHERE id = ?";

    $params = [
        sanitizeInput($data['name']),
        sanitizeInput($data['category']),
        sanitizeInput($data['sku']),
        $data['dailyRate'],
        $data['weeklyRate'] ?? 0,
        sanitizeInput($data['image'] ?? ''),
        sanitizeInput($data['description'] ?? ''),
        ($data['available'] ?? true) ? 1 : 0,
        $data['stock'] ?? 1,
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($query, $params);
    
    if ($rowsAffected === 0) {
        sendError('Product not found', 404);
    }
    
    sendSuccess('Product updated successfully');
}

function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('Product ID is required');
    }
    
    $query = "DELETE FROM products WHERE id = ?";
    $rowsAffected = $database->delete($query, [sanitizeInput($id)]);
    
    if ($rowsAffected === 0) {
        sendError('Product not found', 404);
    }
    
    sendSuccess('Product deleted successfully');
}
?>
