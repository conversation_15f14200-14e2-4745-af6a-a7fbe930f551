<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Coupons API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT * FROM coupons ORDER BY code";
    $coupons = $database->getMany($query);
    
    // Format coupons to match frontend expectations
    $formattedCoupons = array_map(function($coupon) {
        return [
            'id' => $coupon['id'],
            'code' => $coupon['code'],
            'discountType' => $coupon['discount_type'],
            'discountValue' => (float)$coupon['discount_value'],
            'expiryDate' => $coupon['expiry_date'] ? date('Y-m-d', strtotime($coupon['expiry_date'])) : '',
            'active' => (bool)$coupon['active']
        ];
    }, $coupons);
    
    sendResponse($formattedCoupons);
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['code', 'discountType', 'discountValue']);
    
    $id = $database->generateId();
    
    // Check if coupon code already exists
    $checkQuery = "SELECT id FROM coupons WHERE code = ?";
    $existingCoupon = $database->getOne($checkQuery, [sanitizeInput($data['code'])]);
    
    if ($existingCoupon) {
        sendError('A coupon with this code already exists');
    }
    
    $query = "INSERT INTO coupons (id, code, discount_type, discount_value, expiry_date, active)
              VALUES (?, ?, ?, ?, ?, ?)";
    
    $params = [
        $id,
        sanitizeInput($data['code']),
        sanitizeInput($data['discountType']),
        $data['discountValue'],
        $data['expiryDate'] ?? null,
        $data['active'] ?? true
    ];
    
    $database->executeQuery($query, $params);
    
    sendResponse(['id' => $id]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id', 'code', 'discountType', 'discountValue']);
    
    $query = "UPDATE coupons SET code = ?, discount_type = ?, discount_value = ?, expiry_date = ?, active = ?
              WHERE id = ?";
    
    $params = [
        sanitizeInput($data['code']),
        sanitizeInput($data['discountType']),
        $data['discountValue'],
        $data['expiryDate'] ?? null,
        $data['active'] ?? true,
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($query, $params);
    
    if ($rowsAffected === 0) {
        sendError('Coupon not found', 404);
    }
    
    sendSuccess('Coupon updated successfully');
}

function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('Coupon ID is required');
    }
    
    $query = "DELETE FROM coupons WHERE id = ?";
    $rowsAffected = $database->delete($query, [sanitizeInput($id)]);
    
    if ($rowsAffected === 0) {
        sendError('Coupon not found', 404);
    }
    
    sendSuccess('Coupon deleted successfully');
}
?>
