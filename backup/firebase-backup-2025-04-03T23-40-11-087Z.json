{"products": [{"id": "8Ru4wcWUBPZpS8X2KBda", "weeklyRate": 1100, "sku": "AAM2", "featured": true, "image": "https://prggear.com/wp-content/uploads/2016/09/ARRI-ALEXA-Mini.jpg", "available": true, "name": "<PERSON><PERSON>", "createdAt": {"seconds": 1742950518, "nanoseconds": *********}, "dailyRate": 220, "category": "camera", "barcode": "2113", "quantity": 1, "description": "Arri <PERSON> is a digital motion picture camera system", "stock": 1}, {"id": "IfHu0FhJaOv90K12gMRA", "vendorId": "msZ7uO9MukHDSMwmPB9u", "description": "", "vendorSku": "VanEX-1", "isExternalVendorItem": true, "available": true, "weeklyRate": 175, "profitMargin": 14.29, "category": "transport", "stock": 1, "sku": "NVAN8", "quantity": 1, "createdAt": {"seconds": 1743283778, "nanoseconds": 468000000}, "featured": false, "vendorCost": 30, "name": "Nissan Urvan Minivan", "dailyRate": 35, "barcode": "", "image": "https://s3.wheelsage.org/format/picture/picture-thumb-medium/nissan/urvan/autowp.ru_nissan_urvan_high_roof_bus_1.jpg"}, {"id": "MQ1NYmkxE0fAphf4akzd", "createdAt": {"seconds": 1742953518, "nanoseconds": *********}, "description": "Aputure Nova P300c RGB LED Light Panel", "quantity": 1, "dailyRate": 40, "stock": 6, "inventory": {"external": [{"vendorId": "msZ7uO9MukHDSMwmPB9u", "notes": "", "quantity": 5, "location": "", "vendorSku": "VAN01", "vendorCost": 20}], "internal": {"notes": "", "quantity": 1, "location": ""}}, "category": "lighting", "weeklyRate": 200, "sku": "ANP300C", "name": "Aputure Nova P300c", "available": true, "featured": true, "barcode": "", "isExternalVendorItem": false, "image": "https://static.bhphoto.com/images/multiple_images/images500x500/1600267590_IMG_1418674.jpg"}, {"id": "SRnvfYlsw2e4h3maq6nC", "available": true, "barcode": "", "description": "", "stock": 1, "quantity": 1, "featured": false, "sku": "TVL17", "image": "https://www.bhphotovideo.com/cdn-cgi/image/fit=scale-down,width=500,quality=95/https://www.bhphotovideo.com/images/images500x500/tvlogic_lvm_180a_18_5_wide_viewing_lcd_1620139071_1616054.jpg", "createdAt": {"seconds": 1743283002, "nanoseconds": 515000000}, "name": "TVLogic 17inch (LVM-173W)", "category": "camera", "dailyRate": 25, "weeklyRate": 125}, {"id": "gEtmd7uHYIkBvMPbO3Vy", "barcode": "", "stock": 1, "category": "camera", "quantity": 1, "available": true, "dailyRate": 40, "featured": true, "sku": "TB4KTRX", "image": "https://teradek.com/cdn/shop/files/10-2260_Teradek_Bolt-6_LT_Back_750_No-Mount_dc82f691-0f17-405d-ae3d-95ed0e51ac89.png?v=1741895736&width=1100", "description": "4K Zero-delay wireless Video Transmission.", "createdAt": {"seconds": 1742953363, "nanoseconds": 310000000}, "name": "Teradek Bolt 4k 750 (KIT)", "weeklyRate": 225}, {"id": "gUjBh0mZiXegljE192e8", "image": "https://cdn.shopify.com/s/files/1/1343/1935/files/Storm1200x-07.png?v=1725595937&width=1400&crop=center", "name": "Aputure 1200X Kit", "category": "lighting", "createdAt": {"seconds": 1743282585, "nanoseconds": 228000000}, "dailyRate": 60, "featured": true, "description": "", "weeklyRate": 300, "quantity": 1, "available": true, "stock": 3, "barcode": "", "sku": "AP1200X"}, {"id": "iE0q0eykJgqHXaehOz0u", "description": "", "available": true, "featured": false, "dailyRate": 25, "image": "https://www.bhphotovideo.com/images/fb/atomos_neon_17_4k_hdr_1483640.jpg", "name": "Atomos Neon 17 Monitor", "weeklyRate": 125, "sku": "AN174K", "barcode": "", "createdAt": {"seconds": 1743282762, "nanoseconds": 945000000}, "quantity": 1, "stock": 1, "category": "camera"}, {"id": "kzZe8vNlINdz5fn39N7E", "image": "https://www.bhphotovideo.com/cdn-cgi/image/fit=scale-down,width=500,quality=95/https://www.bhphotovideo.com/images/images500x500/dana_dolly_ddok1_dana_dolly_original_kit_1589217726_1047331.jpg", "description": "Local made Dolly slider", "category": "grip", "stock": 1, "featured": false, "sku": "SLIDER", "name": "<PERSON><PERSON><PERSON> (Lite)", "quantity": 1, "createdAt": {"seconds": 1742953796, "nanoseconds": 370000000}, "dailyRate": 20, "available": true, "weeklyRate": 125, "barcode": ""}, {"id": "ntTZX83Sj556WBWoq0jh", "dailyRate": 90, "description": "", "image": "https://static.bhphoto.com/images/multiple_images/images2000x2000/1700653772_IMG_2130748.jpg", "featured": true, "weeklyRate": 450, "stock": 1, "createdAt": {"seconds": 1743656368, "nanoseconds": 849000000}, "barcode": "", "name": "NiSi Athena PL (5 Lens)", "category": "lens", "available": true, "sku": "NISI5", "quantity": 1}, {"id": "tYWOzkyL7d4mWrY5rnp6", "sku": "MMDOLLY", "category": "grip", "image": "https://www.bhphotovideo.com/cdn-cgi/image/fit=scale-down,width=500,quality=95/https://www.bhphotovideo.com/images/images500x500/movmax_83_0054_grip_dolly_1702395335_1785351.jpg", "name": "MOVMAX Grip Dolly Pro Kit", "dailyRate": 40, "featured": false, "available": true, "barcode": "", "createdAt": {"seconds": 1742953705, "nanoseconds": 408000000}, "quantity": 1, "weeklyRate": 200, "stock": 1, "description": "Heavy-Duty Dolly System."}, {"id": "yN7qpcRjlFp8AbBmkbCB", "category": "lighting", "description": "", "weeklyRate": 200, "createdAt": {"seconds": 1743282847, "nanoseconds": *********}, "barcode": "", "image": "https://static.bhphoto.com/images/images500x500/1432655173_1139001.jpg", "available": true, "sku": "ASPS60", "name": "<PERSON>rri <PERSON> S60c", "stock": 2, "featured": false, "dailyRate": 40, "quantity": 1}], "bookings": [{"id": "Q-001001", "discount": 0, "totalAmount": 365, "deliveryFee": 0, "subtotal": 360, "quoteNumber": "Q-001001", "customer": {"name": "patick po", "email": "<EMAIL>", "phone": "+*********", "address": ""}, "tax": 0, "total": 365, "status": "pending", "coupon": null, "delivery": {"fee": 0, "option": {"description": "Pick-up from Manama Office", "fee": 0, "name": "Office pickup", "id": "1"}}, "date": {"seconds": 1743692728, "nanoseconds": *********}, "rentalPeriod": {"startDate": "2025-04-02T16:00:00.000Z", "rentalType": "daily", "days": 1, "dates": ["2025-04-09T04:00:00.000Z"], "endDate": "2025-04-04T16:00:00.000Z"}, "products": [{"createdAt": {"seconds": 1742953518, "nanoseconds": *********}, "stock": 4, "dailyRate": 40, "available": true, "barcode": "", "id": "MQ1NYmkxE0fAphf4akzd", "featured": true, "weeklyRate": 200, "name": "Aputure Nova P300c", "category": "lighting", "description": "Aputure Nova P300c RGB LED Light Panel", "image": "https://static.bhphoto.com/images/multiple_images/images500x500/1600267590_IMG_1418674.jpg", "quantity": 3, "sku": "ANP300C"}]}, {"id": "Q-001002", "deliveryFee": 0, "rentalPeriod": {"startDate": "2025-04-15T16:00:00.000Z", "endDate": "2025-04-23T16:00:00.000Z", "days": 9, "rentalType": "daily", "dates": []}, "delivery": {"option": {"description": "Pick-up from Manama Office", "name": "Office pickup", "fee": 0, "id": "1"}, "fee": 0}, "total": 1980, "totalAmount": 1980, "customer": {"name": "<PERSON>", "address": "", "email": "<EMAIL>", "phone": "+97338451051"}, "date": {"seconds": 1743692728, "nanoseconds": *********}, "subtotal": 1980, "tax": 0, "discount": 0, "quoteNumber": "Q-001002", "coupon": null, "status": "completed", "products": [{"id": "8Ru4wcWUBPZpS8X2KBda", "weeklyRate": 1100, "featured": true, "category": "camera", "quantity": 1, "createdAt": {"seconds": 1742950518, "nanoseconds": *********}, "image": "https://prggear.com/wp-content/uploads/2016/09/ARRI-ALEXA-Mini.jpg", "barcode": "2113", "available": true, "sku": "AAM2", "stock": 1, "description": "Arri <PERSON> is a digital motion picture camera system", "name": "<PERSON><PERSON>", "dailyRate": 220}]}, {"id": "Q-001003", "totalAmount": 160, "products": [{"quantity": 2, "isExternalVendorItem": false, "createdAt": {"seconds": 1742953518, "nanoseconds": *********}, "inventory": {"external": [{"vendorCost": 20, "quantity": 5, "location": "", "vendorSku": "VAN01", "vendorId": "msZ7uO9MukHDSMwmPB9u", "notes": ""}], "internal": {"quantity": 1, "notes": "", "location": ""}}, "barcode": "", "stock": 6, "dailyRate": 40, "sku": "ANP300C", "featured": true, "image": "https://static.bhphoto.com/images/multiple_images/images500x500/1600267590_IMG_1418674.jpg", "available": true, "name": "Aputure Nova P300c", "weeklyRate": 200, "category": "lighting", "description": "Aputure Nova P300c RGB LED Light Panel", "id": "MQ1NYmkxE0fAphf4akzd"}], "discount": 0, "quoteNumber": "Q-001003", "date": {"seconds": 1743692728, "nanoseconds": *********}, "customer": {"phone": "+97338451051", "name": "<PERSON>", "email": "<EMAIL>", "address": ""}, "deliveryFee": 0, "delivery": {"fee": 0, "option": {"id": "1", "fee": 0, "name": "Office pickup", "description": "Pick-up from Manama Office"}}, "rentalPeriod": {"startDate": "2025-04-08T16:00:00.000Z", "dates": [], "endDate": "2025-04-09T16:00:00.000Z", "days": 2, "rentalType": "daily"}, "coupon": null, "subtotal": 160, "status": "pending", "tax": 0, "total": 160}, {"id": "Q-001005", "total": 660, "status": "pending", "delivery": {"option": {"name": "Office pickup", "description": "Pick-up from Manama Office", "id": "1", "fee": 0}, "fee": 0}, "subtotal": 660, "customer": {"phone": "+97338451051", "name": "<PERSON>", "email": "<EMAIL>", "address": ""}, "totalAmount": 660, "products": [{"sku": "AAM2", "image": "https://prggear.com/wp-content/uploads/2016/09/ARRI-ALEXA-Mini.jpg", "createdAt": {"seconds": 1742950518, "nanoseconds": *********}, "id": "8Ru4wcWUBPZpS8X2KBda", "barcode": "2113", "description": "Arri <PERSON> is a digital motion picture camera system", "dailyRate": 220, "name": "<PERSON><PERSON>", "quantity": 1, "available": true, "featured": true, "weeklyRate": 1100, "stock": 1, "category": "camera"}], "date": {"seconds": 1743692728, "nanoseconds": *********}, "rentalPeriod": {"dates": ["2025-04-10T04:00:00.000Z", "2025-04-12T04:00:00.000Z", "2025-04-16T04:00:00.000Z"], "rentalType": "daily", "days": 3}, "deliveryFee": 0, "coupon": null, "quoteNumber": "Q-001005", "tax": 0, "discount": 0}, {"id": "Q-001006", "totalAmount": 1284, "date": {"seconds": 1743692727, "nanoseconds": *********}, "tax": 0, "customer": {"address": "", "email": "<EMAIL>", "name": "<PERSON>", "phone": "+97338451051"}, "coupon": {"code": "MANUAL", "id": "manual-discount", "discountValue": 36, "active": true, "discountType": "fixed", "expiryDate": "2025-04-03"}, "payments": [{"status": "completed", "transactionId": "TXN-1743646114323", "id": "1743646114323", "date": "2025-04-03T02:08:34.323Z", "bookingId": "Q-001006", "method": "cash", "amount": 10}], "subtotal": 1320, "products": [{"createdAt": {"seconds": 1742950518, "nanoseconds": *********}, "sku": "AAM2", "available": true, "featured": true, "category": "camera", "name": "<PERSON><PERSON>", "dailyRate": 220, "weeklyRate": 1100, "description": "Arri <PERSON> is a digital motion picture camera system", "image": "https://prggear.com/wp-content/uploads/2016/09/ARRI-ALEXA-Mini.jpg", "barcode": "2113", "stock": 1, "id": "8Ru4wcWUBPZpS8X2KBda", "quantity": 1}], "discount": 36, "total": 1284, "delivery": {"fee": 0, "option": {"description": "Pick-up from Manama Office", "name": "Office pickup", "fee": 0, "id": "1"}}, "quoteNumber": "Q-001006", "status": "pending", "deliveryFee": 0, "rentalPeriod": {"dates": ["2025-04-30T04:00:00.000Z", "2025-05-02T04:00:00.000Z"], "days": 2, "rentalType": "daily"}}, {"id": "Q-001021", "quoteNumber": "Q-001021", "products": [{"featured": true, "stock": 4, "quantity": 1, "description": "Aputure Nova P300c RGB LED Light Panel", "sku": "ANP300C", "available": true, "category": "lighting", "createdAt": {"seconds": 1742953518, "nanoseconds": *********}, "id": "MQ1NYmkxE0fAphf4akzd", "barcode": "", "dailyRate": 40, "weeklyRate": 200, "name": "Aputure Nova P300c", "image": "https://static.bhphoto.com/images/multiple_images/images500x500/1600267590_IMG_1418674.jpg"}, {"name": "<PERSON><PERSON><PERSON> (Lite)", "dailyRate": 20, "category": "grip", "image": "https://www.bhphotovideo.com/cdn-cgi/image/fit=scale-down,width=500,quality=95/https://www.bhphotovideo.com/images/images500x500/dana_dolly_ddok1_dana_dolly_original_kit_1589217726_1047331.jpg", "id": "kzZe8vNlINdz5fn39N7E", "description": "Local made Dolly slider", "sku": "SLIDER", "stock": 1, "weeklyRate": 125, "available": true, "featured": false, "quantity": 1, "barcode": ""}, {"available": true, "barcode": "", "weeklyRate": 225, "name": "Teradek Bolt 4k 750 (KIT)", "stock": 1, "description": "4K Zero-delay wireless Video Transmission.", "image": "https://teradek.com/cdn/shop/files/10-2260_Teradek_Bolt-6_LT_Back_750_No-Mount_dc82f691-0f17-405d-ae3d-95ed0e51ac89.png?v=1741895736&width=1100", "featured": true, "id": "gEtmd7uHYIkBvMPbO3Vy", "sku": "TB4KTRX", "quantity": 1, "dailyRate": 40, "category": "camera"}], "delivery": {"option": {"id": "1", "description": "Pick-up from Manama Office", "name": "Office pickup", "fee": 0}, "fee": 0}, "customer": {"name": "<PERSON>", "email": "<EMAIL>", "address": "", "phone": "+97338451051"}, "subtotal": 100, "coupon": {"discountValue": 45, "code": "MANUAL", "active": true, "expiryDate": "2025-04-03", "id": "manual-discount", "discountType": "fixed"}, "date": {"seconds": 1743692727, "nanoseconds": *********}, "status": "completed", "tax": 0, "rentalPeriod": {"rentalType": "daily", "days": 1, "startDate": "2025-03-30T16:00:00.000Z", "dates": ["2025-04-09T16:00:00.000Z"], "endDate": "2025-03-30T16:00:00.000Z"}, "total": 55, "deliveryFee": 0, "payments": [], "discount": 45, "totalAmount": 55}], "coupons": [{"id": 1742951877279, "discountType": "percentage", "active": true, "expiryDate": "2025-04-26", "code": "SUMMER", "discountValue": 10}, {"id": 1742952268352, "discountType": "percentage", "code": "WERER", "discountValue": 20, "expiryDate": "2025-04-26", "active": true}, {"id": 1742952309102, "code": "TWE", "active": true, "discountType": "percentage", "discountValue": 12, "expiryDate": "2025-04-26"}], "clients": [{"id": "IZu1MfvsWkeZAxo5rJ5u", "dateAdded": "2025-04-02T21:28:54.458Z", "totalSpent": 10, "totalBookings": 5, "phone": "+97338451051", "name": "<PERSON>", "lastBooking": "2025-04-03T05:17:35.322Z", "email": "<EMAIL>", "address": "", "pendingPayment": 4259}, {"id": "huAQvGt5SMpcNjTvIfmh", "totalSpent": 0, "address": "", "lastBooking": "2025-04-03T05:17:35.512Z", "email": "<EMAIL>", "totalBookings": 1, "phone": "+*********", "pendingPayment": 365, "name": "patick po", "dateAdded": "2025-04-01T03:51:06.707Z"}], "clientActivities": [{"id": "0fKeDaYQElvMiAE9XcwN", "quoteNumber": "Q-001002", "type": "booking", "clientId": "VgayBuV4xh7RFRm2S0aj", "description": "Booked Excavator - <PERSON>, <PERSON><PERSON><PERSON> Saw - 14\"", "amount": 717.6, "date": {"seconds": 1742897719, "nanoseconds": 951000000}, "bookingId": "z1ZIPkbQ7uBCB1yTOxox"}, {"id": "0glwGTKNEgWe5NosBNtR", "bookingId": "Q-001004", "type": "booking", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "description": "Booked <PERSON><PERSON>", "quoteNumber": "Q-001004", "date": {"seconds": 1743043933, "nanoseconds": 847000000}, "amount": 226.6}, {"id": "2EgpKJQ6lQBFIM4CrDJx", "description": "Booked Excavator - Mini", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "bookingId": "RGnaSY5HeSaM3NCmpGJH", "quoteNumber": "Q-001002", "amount": 7650, "type": "booking", "date": {"seconds": 1743042449, "nanoseconds": 403000000}}, {"id": "2ymqet7QTWvgzwzXRh9M", "date": {"seconds": 1742895041, "nanoseconds": 976000000}, "amount": 1104.15, "type": "merge", "description": "Merged client park sdf (sdf@c.c) into sdfsdf (sdfsdf@c.c)", "clientId": "undefined"}, {"id": "3BmhcRqOMpwEXg8h6vkm", "quoteNumber": "Q-001003", "description": "Booked <PERSON><PERSON>", "bookingId": "Q-001003", "date": {"seconds": 1743043454, "nanoseconds": 197000000}, "clientId": "76efOMX6SUjVoJfZmCZC", "type": "booking", "amount": 1812.8}, {"id": "3ulJdr1V7SvqbwlOHAWj", "description": "Booked Excavator - Mini", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "date": {"seconds": 1743049995, "nanoseconds": 821000000}, "bookingId": "Q-001005", "type": "booking", "quoteNumber": "Q-001005", "amount": 250}, {"id": 12, "clientId": 4, "quoteNumber": "Q-789012", "bookingId": 7, "date": {"seconds": 1740920400, "nanoseconds": 0}, "amount": 450, "description": "Rented Concrete Saw - 14\"", "type": "booking"}, {"id": "62sV5unFgkRdHoAASVVY", "bookingId": "ctZMYptGTFQ5dqlqKWS1", "amount": 224.4, "date": {"seconds": 1743033777, "nanoseconds": 525000000}, "description": "Booked <PERSON><PERSON>", "type": "booking", "quoteNumber": "Q-001008", "clientId": "76efOMX6SUjVoJfZmCZC"}, {"id": "9cT7WkdAHkKIHI7s6hoc", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "bookingId": "Q-001003", "date": {"seconds": 1743628653, "nanoseconds": 697000000}, "amount": 160, "quoteNumber": "Q-001003", "description": "Booked Aputure Nova P300c", "type": "booking"}, {"id": "C0Azqb8Kte52gs7g02sW", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "bookingId": "Q-001019", "date": {"seconds": 1743232577, "nanoseconds": 40000000}, "amount": 400, "quoteNumber": "Q-001019", "type": "booking", "description": "Booked Skid Steer Loader"}, {"id": 11, "bookingId": 6, "description": "Rented Generator - 7500W and Air Compressor - 185 CFM", "amount": 650, "clientId": 3, "quoteNumber": "Q-678901", "type": "booking", "date": {"seconds": 1737373500, "nanoseconds": 0}}, {"id": 15, "description": "Rented Trencher - 36\"", "clientId": 5, "bookingId": 10, "quoteNumber": "Q-012345", "amount": 540, "type": "booking", "date": {"seconds": 1740060900, "nanoseconds": 0}}, {"id": 5, "description": "Full payment for booking #3", "date": {"seconds": 1739178300, "nanoseconds": 0}, "amount": 900, "clientId": 1, "type": "payment"}, {"id": 17, "bookingId": 12, "description": "Rented Concrete Mixer - 9 cu ft", "date": {"seconds": 1743846300, "nanoseconds": 0}, "quoteNumber": "Q-234501", "type": "booking", "amount": 350, "clientId": 6}, {"id": "Eu7I3dG2eeyWWfy97GtK", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "bookingId": "Q-001006", "amount": 1320, "date": {"seconds": 1743641720, "nanoseconds": 881000000}, "type": "booking", "description": "Booked <PERSON><PERSON>", "quoteNumber": "Q-001006"}, {"id": "FlEqU45dKtJxuX4rcVww", "amount": 750, "quoteNumber": "Q-001017", "type": "booking", "description": "Booked Excavator - Mini", "date": {"seconds": 1743230017, "nanoseconds": 151000000}, "clientId": "IZu1MfvsWkeZAxo5rJ5u", "bookingId": "Q-001017"}, {"id": "GKkfVfCfmPDSvnvG6LYD", "amount": 396, "bookingId": "jHtRSIeYfNxuaM0TtEvh", "quoteNumber": "Q-001001", "description": "Booked <PERSON><PERSON>", "type": "booking", "date": {"seconds": 1742954617, "nanoseconds": 227000000}, "clientId": "IZu1MfvsWkeZAxo5rJ5u"}, {"id": 14, "clientId": 5, "date": {"seconds": 1738403100, "nanoseconds": 0}, "quoteNumber": "Q-901234", "amount": 650, "type": "booking", "bookingId": 9, "description": "Rented Backhoe Loader"}, {"id": "HYXVj0iwoOKOXjJBgIVg", "amount": 2225.6, "bookingId": "Jp08JX5JYq4JbafXaD7Q", "date": {"seconds": 1742938226, "nanoseconds": 863000000}, "description": "Booked Excavator - <PERSON>, <PERSON><PERSON><PERSON> Saw - 14\"", "type": "booking", "quoteNumber": "Q-001005", "clientId": "zN3CDu0XHpeYpndIHbJo"}, {"id": 16, "quoteNumber": "Q-123450", "type": "booking", "date": {"seconds": 1742034600, "nanoseconds": 0}, "bookingId": 11, "amount": 610, "description": "Rented Forklift - 5000 lb", "clientId": 5}, {"id": "I0uzgk9vi5mTVkQHpkTs", "date": {"seconds": 1743028813, "nanoseconds": 228000000}, "description": "Booked Excavator - Mini", "amount": 1020, "bookingId": "fjRzT7EswHZh1enF7iPs", "clientId": "4icti6rdxDrNkIP0FFzD", "type": "booking", "quoteNumber": "Q-001003"}, {"id": "I8FJxNWCe11A0ONpYt4Y", "date": {"seconds": 1743043518, "nanoseconds": 154000000}, "quoteNumber": "Q-001003", "description": "Booked <PERSON><PERSON>", "clientId": "76efOMX6SUjVoJfZmCZC", "bookingId": "Q-001003", "type": "booking", "amount": 1812.8}, {"id": "KzU7WdwlHVCH1GSQYtSu", "date": {"seconds": 1742894755, "nanoseconds": 608000000}, "description": "Merged client <PERSON> (<EMAIL>) into <PERSON> (<EMAIL>)", "clientId": 5, "amount": 1850, "type": "merge"}, {"id": 7, "amount": 300, "type": "booking", "quoteNumber": "Q-567890", "date": {"seconds": 1742046300, "nanoseconds": 0}, "bookingId": 5, "description": "Rented Concrete Mixer - 9 cu ft", "clientId": 1}, {"id": "MbyXJcO8fQO97oCF1NAv", "amount": 75, "type": "booking", "quoteNumber": "Q-001012", "bookingId": "Q-001012", "date": {"seconds": 1743224221, "nanoseconds": 142000000}, "description": "Booked Combo Stand", "clientId": "C7jOBXuTuMuApDnKyBk4"}, {"id": "Nftz8UL6gv2SKBFN5OSM", "bookingId": "QcAkFrp1pDlTT5fZVXck", "description": "Booked Excavator - Mini", "amount": 520, "date": {"seconds": 1742935696, "nanoseconds": 379000000}, "clientId": "P4ShAqW2kqwA8BM6Wv9F", "quoteNumber": "Q-001004", "type": "booking"}, {"id": "PEQnnDjulkLeQZcZIzzi", "clientId": "4icti6rdxDrNkIP0FFzD", "quoteNumber": "Q-001018", "description": "Booked <PERSON><PERSON>", "bookingId": "Q-001018", "amount": 440, "type": "booking", "date": {"seconds": 1743230271, "nanoseconds": 690000000}}, {"id": "QBwIGR5qPxgWf9gcwsXg", "date": {"seconds": 1742938477, "nanoseconds": 190000000}, "clientId": "undefined", "type": "merge", "description": "Merged client kola po (<EMAIL>) into lolly pop (<EMAIL>)", "amount": 520}, {"id": 8, "quoteNumber": "Q-234567", "amount": 750, "date": {"seconds": 1739524500, "nanoseconds": 0}, "description": "Rented Scissor Lift - 19ft", "bookingId": 2, "type": "booking", "clientId": 2}, {"id": "QtPETqU6AI6z2NG9WxBy", "amount": 660, "type": "booking", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "quoteNumber": "Q-001005", "bookingId": "Q-001005", "date": {"seconds": 1743641506, "nanoseconds": 335000000}, "description": "Booked <PERSON><PERSON>"}, {"id": "RUnXrbHYlAs3gXJBM7En", "date": {"seconds": 1743623847, "nanoseconds": 903000000}, "amount": 0, "type": "merge", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "description": "Merged client <PERSON> (<EMAIL>) into <PERSON> (<EMAIL>)"}, {"id": "RXVjbVS0hNW5Z81pnY7g", "clientId": "undefined", "type": "merge", "description": "Merged client power plus (<EMAIL>) into peeter pan (pete@com.c)", "amount": 1294.8, "date": {"seconds": 1742929150, "nanoseconds": 710000000}}, {"id": 2, "description": "Initial payment for booking #1", "amount": 325, "type": "payment", "clientId": 1, "date": {"seconds": 1736937300, "nanoseconds": 0}}, {"id": "SH7E0lMoxpUGKkG0MZbx", "bookingId": "0MetR8hrNksl24AxL4fl", "type": "booking", "quoteNumber": "Q-001003", "clientId": "76efOMX6SUjVoJfZmCZC", "date": {"seconds": 1742988108, "nanoseconds": 565000000}, "description": "Booked Aputure Nova P300c, Teradek Bolt 4k 750 (KIT)", "amount": 340}, {"id": "SUbR3RFyBx35rjjorbB1", "type": "booking", "quoteNumber": "Q-001014", "clientId": "C7jOBXuTuMuApDnKyBk4", "bookingId": "Q-001014", "description": "Booked <PERSON><PERSON>", "date": {"seconds": 1743224663, "nanoseconds": 372000000}, "amount": 495}, {"id": 10, "description": "Rented Boom Lift - 45ft", "clientId": 3, "amount": 550, "date": {"seconds": 1736071200, "nanoseconds": 0}, "quoteNumber": "Q-456789", "bookingId": 4, "type": "booking"}, {"id": "TBAECRDEJdoVmAGm9aFs", "clientId": "C7jOBXuTuMuApDnKyBk4", "description": "Paid", "type": "note", "date": {"seconds": 1743268682, "nanoseconds": 272000000}}, {"id": "UbP81xTeXprEQ4A06szT", "date": {"seconds": 1742896988, "nanoseconds": 238000000}, "bookingId": "8xP0o94gKNYaaqJK6AJe", "description": "Booked Excavator - <PERSON>, <PERSON><PERSON><PERSON> Saw - 14\"", "quoteNumber": "Q-001001", "type": "booking", "amount": 774.8, "clientId": "YTeArK68hhgt4O0FnCH6"}, {"id": "Uw8cz7bacyCcDcWSi841", "clientId": "uE5QnUHdcP7iB6Z0rC0O", "bookingId": "hDXQFJEmAguPzGmqdKfL", "description": "Booked Excavator - <PERSON>, <PERSON><PERSON>", "amount": 1410, "quoteNumber": "Q-001002", "type": "booking", "date": {"seconds": 1742954828, "nanoseconds": 105000000}}, {"id": "VFLhbvLEvXIhDfiL1kHt", "description": "Booked test", "clientId": "FmOfVWcbIpG2K6OVrpZr", "bookingId": "eTsllhUAYhrwUWCgYm0P", "type": "booking", "date": {"seconds": 1742888678, "nanoseconds": 447000000}, "amount": 292.275, "quoteNumber": "Q-001003"}, {"id": "WDjDPk2IdgPz0hy0OVDe", "type": "booking", "quoteNumber": "Q-001002", "bookingId": "Q-001002", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "description": "Booked <PERSON><PERSON>", "date": {"seconds": 1743623784, "nanoseconds": 275000000}, "amount": 1980}, {"id": "aC24Ycfs352SIXTyjcNR", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "amount": 660, "quoteNumber": "Q-001005", "type": "booking", "date": {"seconds": 1743013681, "nanoseconds": 386000000}, "bookingId": "fSd1utKhTvzBllAPpbr2", "description": "Booked <PERSON><PERSON>"}, {"id": "aFeL3lZGADnxKxTSUla8", "type": "booking", "quoteNumber": "Q-001001", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "description": "Booked Excavator - Mini", "date": {"seconds": 1743028374, "nanoseconds": 614000000}, "bookingId": "dWJs21uXqEWr5APZyUbt", "amount": 510}, {"id": "b9k4EJ2mI2uekDqAiZ3J", "description": "Merged client okay boss (<EMAIL>) into power plus (<EMAIL>)", "date": {"seconds": 1742929039, "nanoseconds": 73000000}, "type": "merge", "amount": 520, "clientId": "undefined"}, {"id": "bFkrNg6cvoExJB7kk83U", "date": {"seconds": 1743230304, "nanoseconds": 706000000}, "description": "Booked <PERSON><PERSON>", "bookingId": "Q-001018", "amount": 440, "type": "booking", "quoteNumber": "Q-001018", "clientId": "4icti6rdxDrNkIP0FFzD"}, {"id": 3, "date": {"seconds": 1738765800, "nanoseconds": 0}, "type": "payment", "clientId": 1, "description": "Final payment for booking #1", "amount": 325}, {"id": "cKQqddi7NYKVPap4M50v", "bookingId": "dywPpBOBRYltcwL3tN14", "type": "booking", "date": {"seconds": 1742988751, "nanoseconds": 65000000}, "clientId": "4icti6rdxDrNkIP0FFzD", "quoteNumber": "Q-001004", "amount": 1040, "description": "Booked <PERSON><PERSON>, Aputure Nova P300c"}, {"id": "eg2jYjLE4iVwjGPQtsC0", "bookingId": "Q-001010", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "type": "booking", "date": {"seconds": 1743223900, "nanoseconds": 688000000}, "quoteNumber": "Q-001010", "amount": 440, "description": "Booked <PERSON><PERSON>"}, {"id": "eiOyyWCMZo85iACanMHW", "clientId": "hxQCvAtG2BfxboU3RGfj", "amount": 649.5, "type": "booking", "date": {"seconds": 1742890212, "nanoseconds": 60000000}, "bookingId": "wiYh9c7c9eQnQe9yCW8B", "quoteNumber": "Q-001006", "description": "Booked Skid Steer Loader"}, {"id": "fQHWYaTLEf7jk6hGH8vY", "clientId": 5, "type": "merge", "description": "Merged client test bhai (<EMAIL>) into <PERSON> (<EMAIL>)", "date": {"seconds": 1742894463, "nanoseconds": 159000000}, "amount": 730.6875}, {"id": 13, "quoteNumber": "Q-890123", "bookingId": 8, "clientId": 5, "description": "Rented Excavator - Mini", "amount": 500, "type": "booking", "date": {"seconds": 1736497800, "nanoseconds": 0}}, {"id": "hc28BibWKgMhYY42M9UL", "type": "booking", "description": "Booked Aputure Nova P300c", "clientId": "4icti6rdxDrNkIP0FFzD", "amount": 408, "bookingId": "H1Mlul3iVj50KrUGNBr9", "quoteNumber": "Q-001002", "date": {"seconds": 1743028709, "nanoseconds": 852000000}}, {"id": "iDQHJgR6TPdQg6KXMh18", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "type": "booking", "quoteNumber": "Q-001001", "description": "Booked <PERSON><PERSON>", "bookingId": "1qQXY4K3jqyD3Vi1f62P", "date": {"seconds": 1743041707, "nanoseconds": 587000000}, "amount": 220}, {"id": "iEETSDwq6fZo1g0s0sF7", "type": "booking", "clientId": "76efOMX6SUjVoJfZmCZC", "quoteNumber": "Q-001020", "date": {"seconds": 1743243753, "nanoseconds": 186000000}, "bookingId": "Q-001020", "description": "Booked <PERSON><PERSON>, <PERSON><PERSON><PERSON> Bolt 4k 750 (KIT)", "amount": 1325}, {"id": "ixuXBlYtoPfwwvxWvj2Z", "date": {"seconds": 1743223234, "nanoseconds": 122000000}, "type": "booking", "quoteNumber": "Q-001008", "description": "Booked Kupo C-Stand", "bookingId": "Q-001008", "clientId": "M7uhjxqWlQsLn37Qt5Vg", "amount": 17.6}, {"id": "jMs1kx4PBBPKXQqQgOhh", "type": "booking", "amount": 2940, "date": {"seconds": 1743127736, "nanoseconds": 4000000}, "description": "Booked <PERSON><PERSON>, Skid St<PERSON>", "quoteNumber": "Q-001007", "bookingId": "Q-001007", "clientId": "76efOMX6SUjVoJfZmCZC"}, {"id": "juibfvDPFwlJX26gNp58", "type": "booking", "date": {"seconds": 1743051644, "nanoseconds": 420000000}, "description": "Booked <PERSON><PERSON>", "clientId": "76efOMX6SUjVoJfZmCZC", "quoteNumber": "Q-001006", "bookingId": "Q-001006", "amount": 220}, {"id": "lBASUy9QOzyhGnmmrhZd", "bookingId": "Q-001013", "description": "Booked Aputure Nova P300c", "quoteNumber": "Q-001013", "clientId": "C7jOBXuTuMuApDnKyBk4", "type": "booking", "date": {"seconds": 1743224268, "nanoseconds": 449000000}, "amount": 80}, {"id": "lUuKcMd99wdH5nuxhlKZ", "clientId": "ps1TlYEefNbTjmI4XptP", "quoteNumber": "Q-001001", "bookingId": "6dl3Gk9w0Is4HMLOYXU9", "amount": 730.6875, "description": "Booked Forklift - 5000 lb", "type": "booking", "date": {"seconds": 1742885351, "nanoseconds": 496000000}}, {"id": 6, "clientId": 1, "date": {"seconds": 1740827700, "nanoseconds": 0}, "description": "Client requested information about long-term rentals", "type": "note"}, {"id": 9, "date": {"seconds": 1739524800, "nanoseconds": 0}, "clientId": 2, "amount": 750, "type": "payment", "description": "Full payment for booking #2"}, {"id": "osc76t4jVz72rt5vwVg9", "quoteNumber": "Q-001005", "date": {"seconds": 1743028891, "nanoseconds": 304000000}, "type": "booking", "bookingId": "EtBTzwAyb8DZMZYHcw7p", "amount": 81.6, "description": "Booked Aputure Nova P300c", "clientId": "uE5QnUHdcP7iB6Z0rC0O"}, {"id": "ozrNXyLyEOXvjvUi2tSV", "amount": 365, "date": {"seconds": **********, "nanoseconds": 271000000}, "type": "booking", "clientId": "huAQvGt5SMpcNjTvIfmh", "description": "Booked Aputure Nova P300c", "bookingId": "Q-001001", "quoteNumber": "Q-001001"}, {"id": 4, "quoteNumber": "Q-345678", "bookingId": 3, "description": "Rented Bulldozer - D6", "clientId": 1, "amount": 900, "type": "booking", "date": {"seconds": **********, "nanoseconds": 0}}, {"id": "rjbKf0iuIEaXtxuWQlTK", "bookingId": "jp3gFMq0YFN1zlZNhSMN", "description": "Booked Excavator - Mini, test", "type": "booking", "date": {"seconds": **********, "nanoseconds": 538000000}, "amount": 1104.15, "quoteNumber": "Q-001002", "clientId": "hxQCvAtG2BfxboU3RGfj"}, {"id": "rwF6sjHl2LfR1V7y5kNS", "clientId": "76efOMX6SUjVoJfZmCZC", "quoteNumber": "Q-001006", "bookingId": "UFcOpe4unZgsg26Uu9DT", "date": {"seconds": **********, "nanoseconds": 714000000}, "type": "booking", "amount": 459, "description": "Booked Bulldozer - D6"}, {"id": "rxPN8RmQsgvj3ywAuCVp", "type": "booking", "bookingId": "x0KWm4kjQkhzlWaOUXqX", "amount": 1795.2, "quoteNumber": "Q-001004", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "date": {"seconds": **********, "nanoseconds": 687000000}, "description": "Booked <PERSON><PERSON>"}, {"id": "sRaWR2q04NZT0SsWc6fR", "description": "Booked <PERSON><PERSON>, Excavator - Mini", "clientId": "M7uhjxqWlQsLn37Qt5Vg", "amount": 940, "date": {"seconds": **********, "nanoseconds": 227000000}, "quoteNumber": "Q-001011", "type": "booking", "bookingId": "Q-001011"}, {"id": "sSJlFPI7dJqtNg7bFtiY", "bookingId": "sTYhJOpYSoKfQbJFLAiP", "type": "booking", "quoteNumber": "Q-001005", "date": {"seconds": 1743013274, "nanoseconds": 543000000}, "description": "Booked <PERSON><PERSON>", "clientId": "IZu1MfvsWkeZAxo5rJ5u", "amount": 660}, {"id": 1, "clientId": 1, "type": "booking", "date": {"seconds": 1736937000, "nanoseconds": 0}, "bookingId": 1, "description": "Rented Excavator - Mini and Skid Steer Loader", "amount": 650, "quoteNumber": "Q-123456"}, {"id": "tnUMvzjVbjHpBi9TTEyM", "date": {"seconds": 1743283987, "nanoseconds": 547000000}, "clientId": "IZu1MfvsWkeZAxo5rJ5u", "type": "booking", "quoteNumber": "Q-001021", "amount": 110, "description": "Booked Aputure Nova P300c, Teradek Bolt 4k 750 (KIT), <PERSON><PERSON><PERSON> (Lite)", "bookingId": "Q-001021"}, {"id": "trjhYhzsfQGPziYaiE9c", "clientId": "zsspeR6g7Gk5muhcPkZb", "date": {"seconds": 1742939209, "nanoseconds": 615000000}, "amount": 520, "bookingId": "7hofzIZNITwTtFBUMMmk", "type": "booking", "quoteNumber": "Q-001006", "description": "Booked Excavator - Mini"}, {"id": "vick3TYF8BmHVUk4WaAK", "clientId": "undefined", "type": "merge", "description": "Merged client Cosy Place (<EMAIL>) into lolly pop (<EMAIL>)", "amount": 520, "date": {"seconds": 1742939421, "nanoseconds": *********}}, {"id": "yLRBkkzEdIGyrNpSpOvq", "clientId": "ZYTokb6TXpZZqgG0ehx9", "description": "Booked Excavator - Mini", "type": "booking", "amount": 520, "date": {"seconds": 1742898025, "nanoseconds": *********}, "quoteNumber": "Q-001003", "bookingId": "rwgjX76e5nQKV01v71zH"}], "systemSettings": [{"id": "general", "enableTax": true, "taxRate": 0, "deliveryOptions": [{"name": "Office pickup", "fee": 0, "description": "Pick-up from Manama Office", "id": "1"}, {"id": "2", "fee": 30, "name": "Express Delivery", "description": "Minivan on-location delivery (35 km)"}], "lastQuoteNumber": 1006}, {"id": "settings", "taxRate": 0, "deliveryOptions": [{"id": "1", "name": "Office pickup", "fee": 0, "description": "Pick-up from Manama Office"}, {"name": "Express Delivery", "fee": 30, "id": "2", "description": "Minivan on-location delivery (35 km)"}], "enableTax": true, "lastQuoteNumber": 1006}], "users": [{"id": "AgGCxRu7Pcv1z6XD4j6X", "lastLogin": "2025-04-02T21:32:08.915Z", "name": "Admin", "password": "password", "isActive": true, "createdAt": "2025-03-29T07:10:30.401Z", "username": "admin", "email": "<EMAIL>", "role": "admin"}, {"id": "gAF0x0OxDRSwUGIXx1Ki", "name": "Booking", "username": "booking", "password": "booking1", "isActive": true, "lastLogin": "2025-03-29T07:15:24.044Z", "createdAt": "2025-03-29T07:10:30.397Z", "email": "<EMAIL>", "role": "manager"}], "vendors": [{"id": "msZ7uO9MukHDSMwmPB9u", "phone": "", "notes": "", "name": "<PERSON><PERSON>", "paymentTerms": "Net 30", "email": "", "createdAt": {"seconds": 1743286583, "nanoseconds": *********}, "address": "", "active": true, "contactName": ""}], "vendorTransactions": [{"id": "qTNsJ2zVM01WLG5PrQ8X", "description": "ap", "amount": 15, "type": "invoice", "reference": "", "vendorId": "msZ7uO9MukHDSMwmPB9u", "date": {"seconds": 1743206400, "nanoseconds": 0}, "status": "completed"}]}