import React, { useState, useRef } from 'react';
import { FileText, Check, X, ChevronDown, ChevronUp, Calendar, User, Download, Plus, Trash2, DollarSign, CreditCard, AlertCircle, Truck, Edit, Building, Camera } from 'lucide-react';
import { Booking, Payment, Product, SystemSettings, DeliveryOption } from '../../../types';
import { generatePDF } from '../../../services/pdfGenerator';
import { updateBooking } from '../../../services/firebase';
import { formatCurrency } from '../../../utils';
import BookingItems from './BookingItems';
import * as htmlToImage from 'html-to-image';

interface BookingListProps {
  bookings: Booking[];
  products: Product[];
  systemSettings: SystemSettings;
  onBookingsUpdate: (bookings: Booking[]) => void;
  expandedBookingId: string | null;
  setExpandedBookingId: (id: string | null) => void;
  onDeleteBooking: (id: string) => void;
  onAddPayment: (bookingId: string) => void;
  onEditPayment: (bookingId: string, paymentId: string) => void;
  onDeletePayment: (bookingId: string, paymentId: string) => void;
  formatDate: (date: string) => string;
  getPaymentStatus: (booking: Booking) => string;
  getPaymentStatusBadgeClass: (status: string) => string;
  calculatePaymentProgress: (booking: Booking) => number;
  onStatusChange: (bookingId: string, status: 'pending' | 'confirmed' | 'completed' | 'cancelled') => void;
}

const BookingList: React.FC<BookingListProps> = ({
  bookings,
  products,
  systemSettings,
  onBookingsUpdate,
  expandedBookingId,
  setExpandedBookingId,
  onDeleteBooking,
  onAddPayment,
  onEditPayment,
  onDeletePayment,
  formatDate,
  getPaymentStatus,
  getPaymentStatusBadgeClass,
  calculatePaymentProgress,
  onStatusChange
}) => {
  // Ensure bookings is always an array of valid objects
  const safeBookings = Array.isArray(bookings) ? bookings.filter(Boolean) : [];

  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  
  // Payment related state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentBookingId, setPaymentBookingId] = useState<string | null>(null);
  const [editingPaymentId, setEditingPaymentId] = useState<string | null>(null);
  const [newPayment, setNewPayment] = useState<{
    amount: string;
    method: 'cash' | 'card' | 'bank_transfer';
    reference: string;
    notes: string;
  }>({
    amount: '',
    method: 'cash',
    reference: '',
    notes: ''
  });

  // Calculate item total based on rental type and custom days
  const calculateItemTotal = (product: Product, booking: Booking) => {
    const productDays = product.customDays || booking.rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate || product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;
    
    if (booking.rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      const weeks = Math.ceil(productDays / 7);
      return weeklyRate * weeks * product.quantity;
    } else {
      return dailyRate * productDays * product.quantity;
    }
  };
  
  // Calculate total amount for booking
  const calculateBookingTotal = (booking: Booking) => {
    // Calculate subtotal from products
    const subtotal = booking.products.reduce((sum, product) => 
      sum + calculateItemTotal(product, booking), 0);
    
    // Add delivery fee
    const deliveryFee = booking.delivery?.fee || 0;
    const subtotalWithDelivery = subtotal + deliveryFee;
    
    // Calculate discount
    const discount = booking.coupon ? 
      (booking.coupon.discountType === 'percentage' ? 
        (subtotalWithDelivery * booking.coupon.discountValue / 100) : 
        booking.coupon.discountValue) : 0;
    
    // Calculate tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);
    
    // Return final total
    return booking.status === 'cancelled' ? 0 : (taxableAmount + tax);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Handle PDF generation
  const handleGeneratePDF = async (booking: Booking) => {
    try {
      await generatePDF({
        ...booking,
        settings: systemSettings
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  // Handle editing booking items
  const handleEditItems = (booking: Booking) => {
    setEditingBooking({ ...booking });
  };
  
  // Save booking changes
  const handleSaveBookingChanges = () => {
    if (editingBooking) {
      // Update the booking in the database
      updateBooking(editingBooking).then(() => {
        // Update the local state
        const updatedBookings = safeBookings.map(b => 
          b.id === editingBooking.id ? editingBooking : b
        );
        onBookingsUpdate(updatedBookings);
        setEditingBooking(null);
      }).catch((error: Error) => {
        console.error('Error updating booking:', error);
        // Show error message to user
        alert(`Failed to update booking: ${error.message}`);
        // Revert the local state if the update fails
        setEditingBooking(null);
      });
    }
  };

  // Handle adding a payment
  const handleLocalAddPayment = (bookingId: string) => {
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: '',
      method: 'cash',
      reference: '',
      notes: ''
    });
    setEditingPaymentId(null);
    setShowPaymentModal(true);
  };

  // Handle editing a payment
  const handleLocalEditPayment = (bookingId: string, paymentId: string) => {
    const booking = safeBookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;
    
    const payment = booking.payments.find(p => p.id === paymentId);
    if (!payment) return;
    
    // Populate the payment form with existing data
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: payment.amount.toString(),
      method: payment.method as 'cash' | 'card' | 'bank_transfer',
      reference: payment.reference || '',
      notes: payment.notes || ''
    });
    
    // Set editing state with payment ID
    setEditingPaymentId(paymentId);
    setShowPaymentModal(true);
  };

  // Handle submitting payment
  const handleSubmitPayment = () => {
    if (!paymentBookingId) return;
    
    const booking = safeBookings.find(b => b.id === paymentBookingId);
    if (!booking) return;
    
    // Validate amount
    const amount = parseFloat(newPayment.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid payment amount');
      return;
    }

    let updatedBooking;
    
    if (editingPaymentId) {
      // Editing existing payment
      updatedBooking = {
        ...booking,
        payments: (booking.payments || []).map(payment => {
          if (payment.id === editingPaymentId) {
            // Create a new payment object without undefined values
            const updatedPayment = {
              ...payment,
              amount: amount,
              method: newPayment.method
            };
            
            // Only add non-empty properties
            if (newPayment.reference && newPayment.reference.trim() !== '') {
              updatedPayment.reference = newPayment.reference;
            }
            if (newPayment.notes && newPayment.notes.trim() !== '') {
              updatedPayment.notes = newPayment.notes;
            }
            
            return updatedPayment;
          }
          return payment;
        })
      };
    } else {
      // Adding new payment
      const paymentData: Payment = {
        id: Date.now().toString(),
        bookingId: paymentBookingId,
        amount: amount,
        transactionId: `TXN-${Date.now()}`,
        date: new Date().toISOString(),
        method: newPayment.method,
        status: 'completed'
      };
      
      // Only add non-empty properties
      if (newPayment.reference && newPayment.reference.trim() !== '') {
        paymentData.reference = newPayment.reference;
      }
      if (newPayment.notes && newPayment.notes.trim() !== '') {
        paymentData.notes = newPayment.notes;
      }

      updatedBooking = {
        ...booking,
        payments: [...(booking.payments || []), paymentData]
      };
    }

    // Update the booking in the database
    updateBooking(updatedBooking)
      .then(() => {
        // Update the local state
        const updatedBookings = safeBookings.map(b => 
          b.id === paymentBookingId ? updatedBooking : b
        );
        onBookingsUpdate(updatedBookings);
        setShowPaymentModal(false);
        setPaymentBookingId(null);
        setEditingPaymentId(null);
      })
      .catch((error) => {
        console.error('Error updating payment:', error);
        alert(`Failed to update payment: ${error.message}`);
      });
  };

  // Handle generating a snapshot image of the booking card
  const handleGenerateSnapshot = async (booking: Booking, bookingCardRef: React.RefObject<HTMLDivElement>) => {
    if (!bookingCardRef.current) return;
    
    try {
      // Create a separate snapshot container
      const snapshotContainer = document.createElement('div');
      snapshotContainer.className = 'bg-white p-6 rounded-lg shadow-lg';
      snapshotContainer.style.width = '350px';
      snapshotContainer.style.position = 'fixed';
      snapshotContainer.style.top = '0';
      snapshotContainer.style.left = '0';
      snapshotContainer.style.zIndex = '-1000';
      document.body.appendChild(snapshotContainer);
      
      // Add header with company branding
      const header = document.createElement('div');
      header.className = 'mb-4 pb-3 border-b border-gray-200';
      header.innerHTML = `
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-blue-600">Equipment Rental</h2>
          <div class="text-right">
            <p class="text-sm font-medium">Booking Confirmation</p>
            <p class="text-xs text-gray-500">Generated on ${new Date().toLocaleDateString()}</p>
          </div>
        </div>
      `;
      snapshotContainer.appendChild(header);
      
      // Quote number and date section
      const quoteSection = document.createElement('div');
      quoteSection.className = 'mb-4';
      quoteSection.innerHTML = `
        <div class="flex justify-between items-center">
          <div>
            <span class="text-sm text-gray-500">Quote #:</span>
            <span class="text-sm font-medium ml-1">${booking.quoteNumber}</span>
          </div>
          <div>
            <span class="text-sm text-gray-500">Date:</span>
            <span class="text-sm font-medium ml-1">${formatDate(booking.date)}</span>
          </div>
        </div>
      `;
      snapshotContainer.appendChild(quoteSection);
      
      // Customer information
      const customerSection = document.createElement('div');
      customerSection.className = 'mb-4 p-3 bg-gray-50 rounded';
      customerSection.innerHTML = `
        <h3 class="text-sm font-semibold mb-2">Customer Information</h3>
        <p class="text-sm"><span class="font-medium">Name:</span> ${booking.customer.name}</p>
        <p class="text-sm"><span class="font-medium">Email:</span> ${booking.customer.email}</p>
        <p class="text-sm"><span class="font-medium">Phone:</span> ${booking.customer.phone || 'N/A'}</p>
      `;
      snapshotContainer.appendChild(customerSection);
      
      // Rental period
      const rentalSection = document.createElement('div');
      rentalSection.className = 'mb-4 p-3 bg-blue-50 rounded';
      rentalSection.innerHTML = `
        <h3 class="text-sm font-semibold mb-2">Rental Period</h3>
        <div class="grid grid-cols-2 gap-2">
          <div>
            <p class="text-xs text-gray-500">Start Date</p>
            <p class="text-sm font-medium">${formatDate(booking.rentalPeriod.startDate)}</p>
          </div>
          <div>
            <p class="text-xs text-gray-500">End Date</p>
            <p class="text-sm font-medium">${formatDate(booking.rentalPeriod.endDate)}</p>
          </div>
        </div>
        <div class="mt-2">
          <span class="px-2 py-1 text-xs font-medium bg-indigo-100 text-indigo-800">
            ${booking.rentalPeriod.days} ${booking.rentalPeriod.days === 1 ? 'day' : 'days'} Rental
          </span>
        </div>
      `;
      snapshotContainer.appendChild(rentalSection);
      
      // Items section
      const itemsSection = document.createElement('div');
      itemsSection.className = 'mb-4';
      
      // Calculate total items and group by category
      const totalItems = booking.products.reduce((sum, product) => sum + product.quantity, 0);
      const categoryCountMap: {[category: string]: number} = {};
      booking.products.forEach(product => {
        const category = product.category || 'Uncategorized';
        categoryCountMap[category] = (categoryCountMap[category] || 0) + product.quantity;
      });
      
      // Create header for items section
      const itemsHeader = document.createElement('div');
      itemsHeader.className = 'flex flex-wrap gap-2 mb-3';
      itemsHeader.innerHTML = `
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
          Total Items: ${totalItems}
        </span>
        ${Object.entries(categoryCountMap).map(([category, count]) => `
          <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
            ${category}: ${count}
          </span>
        `).join('')}
      `;
      itemsSection.appendChild(itemsHeader);
      
      // Add the list of items
      const itemsList = document.createElement('div');
      itemsList.className = 'space-y-2';
      
      booking.products.forEach(product => {
        const productItem = document.createElement('div');
        productItem.className = 'p-2 border border-gray-200 rounded';
        
        const dailyRate = product.temporaryDailyRate || product.dailyRate;
        const days = product.customDays || booking.rentalPeriod.days;
        const itemTotal = dailyRate * product.quantity * days;
        
        productItem.innerHTML = `
          <div class="flex justify-between items-center">
            <div>
              <span class="font-medium mr-1">${product.quantity}x</span>
              <span>${product.name}</span>
            </div>
            <span class="font-medium">${formatCurrency(itemTotal)}</span>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            <span>${formatCurrency(dailyRate)}/day</span>
            <span class="mx-1">•</span>
            <span>${days} ${days === 1 ? 'day' : 'days'}</span>
          </div>
        `;
        
        itemsList.appendChild(productItem);
      });
      
      itemsSection.appendChild(itemsList);
      snapshotContainer.appendChild(itemsSection);
      
      // Cost summary
      const costSummary = document.createElement('div');
      costSummary.className = 'mt-4 pt-3 border-t border-gray-200';
      
      const calculatedTotal = calculateBookingTotal(booking);
      const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
      const remainingAmount = calculatedTotal - paidAmount;
      const paymentStatus = getPaymentStatus(booking);
      
      costSummary.innerHTML = `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Subtotal:</p>
          <p class="text-sm font-medium">${formatCurrency(booking.subtotal)}</p>
        </div>
        ${booking.deliveryFee > 0 ? `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Delivery:</p>
          <p class="text-sm">${formatCurrency(booking.deliveryFee)}</p>
        </div>
        ` : ''}
        ${booking.discount > 0 ? `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Discount:</p>
          <p class="text-sm text-green-600">-${formatCurrency(booking.discount)}</p>
        </div>
        ` : ''}
        ${booking.tax > 0 ? `
        <div class="flex justify-between mb-1">
          <p class="text-sm">Tax:</p>
          <p class="text-sm">${formatCurrency(booking.tax)}</p>
        </div>
        ` : ''}
        <div class="flex justify-between mt-2 pt-2 border-t border-gray-200">
          <p class="text-sm font-bold">Total:</p>
          <p class="text-sm font-bold">${formatCurrency(calculatedTotal)}</p>
        </div>
        ${paidAmount > 0 ? `
        <div class="flex justify-between mt-1">
          <p class="text-sm">Paid:</p>
          <p class="text-sm text-green-600">${formatCurrency(paidAmount)}</p>
        </div>
        ` : ''}
        ${remainingAmount > 0 ? `
        <div class="flex justify-between mt-1">
          <p class="text-sm">Balance:</p>
          <p class="text-sm text-red-600">${formatCurrency(remainingAmount)}</p>
        </div>
        ` : ''}
        <div class="mt-3 flex justify-center">
          <span class="px-3 py-1 text-xs font-semibold rounded-full ${getPaymentStatusBadgeClass(paymentStatus)} capitalize">
            ${paymentStatus}
          </span>
        </div>
      `;
      
      snapshotContainer.appendChild(costSummary);
      
      // Add footer
      const footer = document.createElement('div');
      footer.className = 'mt-4 pt-3 border-t border-gray-200 text-center';
      footer.innerHTML = `
        <p class="text-xs text-gray-500">Thank you for your business!</p>
        <p class="text-xs text-gray-500">For questions, contact <NAME_EMAIL></p>
      `;
      snapshotContainer.appendChild(footer);
      
      // Wait for the DOM to update
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Add Tailwind styles directly to ensure proper rendering
      const tailwindStyles = document.createElement('style');
      tailwindStyles.textContent = `
        .bg-white { background-color: white; }
        .p-6 { padding: 1.5rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
        .mb-4 { margin-bottom: 1rem; }
        .pb-3 { padding-bottom: 0.75rem; }
        .border-b { border-bottom-width: 1px; }
        .border-gray-200 { border-color: #e5e7eb; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-between { justify-content: space-between; }
        .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
        .font-bold { font-weight: 700; }
        .text-blue-600 { color: #2563eb; }
        .text-right { text-align: right; }
        .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
        .font-medium { font-weight: 500; }
        .text-xs { font-size: 0.75rem; line-height: 1rem; }
        .text-gray-500 { color: #6b7280; }
        .ml-1 { margin-left: 0.25rem; }
        .p-3 { padding: 0.75rem; }
        .bg-gray-50 { background-color: #f9fafb; }
        .rounded { border-radius: 0.25rem; }
        .font-semibold { font-weight: 600; }
        .mb-2 { margin-bottom: 0.5rem; }
        .bg-blue-50 { background-color: #eff6ff; }
        .grid { display: grid; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .gap-2 { gap: 0.5rem; }
        .mt-2 { margin-top: 0.5rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .bg-indigo-100 { background-color: #e0e7ff; }
        .text-indigo-800 { color: #3730a3; }
        .flex-wrap { flex-wrap: wrap; }
        .gap-2 { gap: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .rounded-full { border-radius: 9999px; }
        .bg-blue-100 { background-color: #dbeafe; }
        .text-blue-800 { color: #1e40af; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .text-gray-800 { color: #1f2937; }
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .p-2 { padding: 0.5rem; }
        .border { border-width: 1px; }
        .border-gray-200 { border-color: #e5e7eb; }
        .mr-1 { margin-right: 0.25rem; }
        .mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
        .mt-4 { margin-top: 1rem; }
        .pt-3 { padding-top: 0.75rem; }
        .mb-1 { margin-bottom: 0.25rem; }
        .text-green-600 { color: #059669; }
        .mt-3 { margin-top: 0.75rem; }
        .justify-center { justify-content: center; }
        .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
        .text-center { text-align: center; }
        .text-red-600 { color: #dc2626; }
      `;
      snapshotContainer.appendChild(tailwindStyles);
      
      // Generate the image with a small delay to ensure styles are applied
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Generate the image
      const dataUrl = await htmlToImage.toPng(snapshotContainer, {
        quality: 1.0,
        pixelRatio: 2,
        width: snapshotContainer.offsetWidth,
        height: snapshotContainer.offsetHeight,
        canvasWidth: snapshotContainer.offsetWidth * 2,
        canvasHeight: snapshotContainer.offsetHeight * 2,
        backgroundColor: '#ffffff'
      });
      
      // Remove the temporary container
      document.body.removeChild(snapshotContainer);
      
      // Create a link to download the image
      const link = document.createElement('a');
      link.download = `Booking_${booking.quoteNumber}.png`;
      link.href = dataUrl;
      link.click();
    } catch (error) {
      console.error('Error generating snapshot:', error);
      alert('Failed to generate snapshot. Please try again.');
    }
  };

  // Render a booking row for desktop view
  const renderDesktopBookingRow = (booking: Booking) => {
    const paymentStatus = getPaymentStatus(booking);
    const calculatedTotal = calculateBookingTotal(booking);
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = calculatedTotal - paidAmount;
    const progressPercentage = calculatePaymentProgress(booking);
    const bookingCardRef = useRef<HTMLDivElement>(null);
    
    return (
      <tr key={booking.id} className={expandedBookingId === booking.id ? 'bg-blue-50' : ''}>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center" ref={bookingCardRef}>
            <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
              <FileText size={20} />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900">{booking.quoteNumber}</div>
              <div className="text-sm text-gray-500 flex items-center gap-2">
                {formatDate(booking.date)}
                <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  getStatusBadgeClass(booking.status)
                } capitalize`}>{booking.status}</span>
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {booking.customer.name} • {booking.customer.email}
              </div>
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            <div className="flex items-center">
              <Calendar size={16} className="mr-2 text-gray-500" />
              {formatDate(booking.rentalPeriod.startDate)}
            </div>
            <div className="flex items-center mt-1">
              <Calendar size={16} className="mr-2 text-gray-500" />
              {formatDate(booking.rentalPeriod.endDate)}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {booking.rentalPeriod.days} {booking.rentalPeriod.days === 1 ? 'day' : 'days'}
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex flex-col items-center">
            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(paymentStatus)} capitalize`}>
              {paymentStatus}
            </span>
            {paymentStatus !== 'unpaid' && (
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div 
                  className={`h-2.5 rounded-full ${
                    progressPercentage === 100 ? 'bg-green-600' : 'bg-yellow-400'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            )}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right">
          <div className="text-sm font-medium text-gray-900">{formatCurrency(calculatedTotal)}</div>
          {paymentStatus !== 'unpaid' && (
            <div className="text-sm text-gray-500">
              Paid: {formatCurrency(paidAmount)}
            </div>
          )}
          {paymentStatus === 'partial' && (
            <div className="text-sm text-red-600">
              Remaining: {formatCurrency(remainingAmount)}
            </div>
          )}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center space-x-3 min-w-[120px]">
            <button
              onClick={() => setExpandedBookingId(expandedBookingId === booking.id ? null : booking.id)}
              className="p-2 rounded-full hover:bg-gray-100 text-gray-600"
              title={expandedBookingId === booking.id ? "Collapse" : "Expand"}
            >
              {expandedBookingId === booking.id ? (
                <ChevronUp size={18} />
              ) : (
                <ChevronDown size={18} />
              )}
            </button>
            <button
              onClick={() => handleGeneratePDF(booking)}
              className="p-2 rounded-full hover:bg-gray-100 text-gray-600"
              title="Download PDF"
            >
              <Download size={18} />
            </button>
            <button
              onClick={() => handleGenerateSnapshot(booking, bookingCardRef)}
              className="p-2 rounded-full hover:bg-gray-100 text-purple-600"
              title="Generate Snapshot"
            >
              <Camera size={18} />
            </button>
          </div>
        </td>
      </tr>
    );
  };

  // Render a mobile booking card for mobile view
  const renderMobileBookingCard = (booking: Booking) => {
    const bookingCardRef = useRef<HTMLDivElement>(null);
    const paymentStatus = getPaymentStatus(booking);
    const calculatedTotal = calculateBookingTotal(booking);
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = calculatedTotal - paidAmount;
    const progressPercentage = calculatePaymentProgress(booking);
    
    // Calculate total number of items booked
    const totalItems = booking.products.reduce((sum, product) => sum + product.quantity, 0);
    
    // Group items by category and count them
    const categoryCountMap: {[category: string]: number} = {};
    booking.products.forEach(product => {
      const category = product.category || 'Uncategorized';
      categoryCountMap[category] = (categoryCountMap[category] || 0) + product.quantity;
    });
    
    return (
      <div 
        key={booking.id} 
        className={`rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-4 ${
          expandedBookingId === booking.id ? 'bg-blue-50 border-blue-200' : 'bg-white'
        }`}
      >
        <div className="p-4" ref={bookingCardRef}>
          {/* Header with quote number and actions */}
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
                <FileText size={20} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{booking.quoteNumber}</h3>
                <p className="text-sm text-gray-500">{formatDate(booking.date)}</p>
              </div>
            </div>
            
            <div className="flex space-x-2 booking-actions">
              <button
                onClick={() => handleGenerateSnapshot(booking, bookingCardRef)}
                className="p-2 rounded-full bg-purple-100 text-purple-600"
                title="Generate Snapshot"
              >
                <Camera size={18} />
              </button>
              <button
                onClick={() => handleLocalAddPayment(booking.id)}
                className="p-2 rounded-full bg-green-100 text-green-600"
                title="Add Payment"
              >
                <DollarSign size={18} />
              </button>
              <button
                onClick={() => handleGeneratePDF(booking)}
                className="p-2 rounded-full bg-gray-100 text-gray-600"
                title="Download PDF"
              >
                <Download size={18} />
              </button>
              <button
                onClick={() => setExpandedBookingId(expandedBookingId === booking.id ? null : booking.id)}
                className="p-2 rounded-full bg-gray-100 text-gray-600"
                title={expandedBookingId === booking.id ? "Collapse" : "Expand"}
              >
                {expandedBookingId === booking.id ? (
                  <ChevronUp size={18} />
                ) : (
                  <ChevronDown size={18} />
                )}
              </button>
            </div>
          </div>
          
          {/* Status and customer info */}
          <div className="flex flex-wrap items-center gap-2 mb-3">
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
              getStatusBadgeClass(booking.status)
            } capitalize`}>
              {booking.status}
            </span>
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
              getPaymentStatusBadgeClass(paymentStatus)
            } capitalize`}>
              {paymentStatus}
            </span>
            <span className="px-2 py-1 text-xs font-semibold bg-indigo-100 text-indigo-800">
              {booking.rentalPeriod.days} {booking.rentalPeriod.days === 1 ? 'day' : 'days'} Rental
            </span>
          </div>
          
          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700">
              {booking.customer.name}
            </p>
            <p className="text-xs text-gray-500">{booking.customer.email}</p>
          </div>
          
          {/* Item count and categories */}
          <div className="flex flex-wrap gap-2 mb-3">
            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              Total Items: {totalItems}
            </span>
            {Object.entries(categoryCountMap).map(([category, count]) => (
              <span 
                key={category} 
                className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"
              >
                {category}: {count}
              </span>
            ))}
          </div>
          
          {/* Rental period */}
          <div className="grid grid-cols-2 gap-2 mb-3">
            <div>
              <p className="text-xs text-gray-500">Start Date</p>
              <p className="text-sm">{formatDate(booking.rentalPeriod.startDate)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">End Date</p>
              <p className="text-sm">{formatDate(booking.rentalPeriod.endDate)}</p>
            </div>
          </div>
          
          {/* Amount */}
          <div className="border-t border-gray-200 pt-3">
            <div className="flex justify-between mb-1">
              <p className="text-sm font-medium">Total:</p>
              <p className="text-sm font-medium">{formatCurrency(calculatedTotal)}</p>
            </div>
            {paymentStatus !== 'unpaid' && (
              <div className="flex justify-between mb-1">
                <p className="text-sm text-gray-600">Paid:</p>
                <p className="text-sm text-green-600">{formatCurrency(paidAmount)}</p>
              </div>
            )}
            {paymentStatus === 'partial' && (
              <div className="flex justify-between">
                <p className="text-sm text-gray-600">Remaining:</p>
                <p className="text-sm text-red-600">{formatCurrency(remainingAmount)}</p>
              </div>
            )}
            {paymentStatus !== 'unpaid' && (
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div 
                  className={`h-2.5 rounded-full ${
                    progressPercentage === 100 ? 'bg-green-600' : 'bg-yellow-400'
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            )}
          </div>
        </div>
        
        {/* Include expanded content directly in the card for mobile */}
        {expandedBookingId === booking.id && (
          <div className="bg-gray-50 border-t border-gray-200 p-4">
            {/* Mobile expanded view */}
            <div className="border-t border-gray-200 pt-4 mt-2">
              {/* Status Change */}
              <div className="mb-4">
                <h3 className="font-medium mb-2">Booking Status</h3>
                <div className="flex flex-wrap gap-2">
                  {['pending', 'confirmed', 'completed', 'cancelled'].map(status => (
                    <button
                      key={status}
                      onClick={() => onStatusChange(booking.id, status as any)}
                      className={`px-3 py-1 rounded-md text-xs font-medium ${
                        booking.status === status
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                      } capitalize`}
                    >
                      {status}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Cost Breakdown Section */}
              <div className="mb-4">
                <h3 className="font-medium mb-2">Cost Breakdown</h3>
                <div className="bg-gray-50 p-3 rounded border border-gray-200">
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="text-right">{formatCurrency(booking.subtotal || 0)}</span>
                    
                    {/* Delivery Options Dropdown in mobile view */}
                    <span className="text-gray-600">Delivery:</span>
                    <div className="text-right">
                      <select
                        value={booking.delivery?.option?.id || 'none'}
                        onChange={(e) => {
                          const optionId = e.target.value;
                          let selectedOption = null;
                          let fee = 0;
                          
                          if (optionId !== 'none') {
                            selectedOption = systemSettings.deliveryOptions.find(opt => opt.id === optionId) || null;
                            fee = selectedOption?.fee || 0;
                          }
                          
                          // Calculate new total with updated delivery fee
                          const subtotal = booking.subtotal || 0;
                          const discount = booking.discount || 0;
                          const subtotalAfterDiscount = subtotal - discount;
                          const tax = Math.round((subtotalAfterDiscount + fee) * (systemSettings.taxRate || 0) / 100 * 1000) / 1000;
                          
                          const updatedBooking = {
                            ...booking,
                            delivery: {
                              option: selectedOption,
                              fee
                            },
                            deliveryFee: fee,
                            tax: tax
                          };
                          
                          // Update booking in database
                          updateBooking(updatedBooking).then(() => {
                            // Update local state
                            const updatedBookings = safeBookings.map(b => 
                              b.id === booking.id ? updatedBooking : b
                            );
                            onBookingsUpdate(updatedBookings);
                          }).catch((error) => {
                            console.error('Error updating delivery option:', error);
                          });
                        }}
                        className="border border-gray-300 rounded py-1 px-2 text-sm w-full"
                      >
                        <option value="none">Self Pickup (Free)</option>
                        {systemSettings.deliveryOptions.map(opt => (
                          <option key={opt.id} value={opt.id}>
                            {opt.name} ({formatCurrency(opt.fee)})
                          </option>
                        ))}
                      </select>
                      <span className="block mt-1">{formatCurrency(booking.deliveryFee || 0)}</span>
                    </div>
                    
                    <span className="text-gray-600">Discount:</span>
                    <span className="text-right">-{formatCurrency(booking.discount || 0)}</span>
                    
                    <span className="text-gray-600">VAT ({systemSettings.taxRate || 0}%):</span>
                    <span className="text-right">{formatCurrency(booking.tax || 0)}</span>
                    
                    <span className="text-gray-800 font-medium pt-2 border-t border-gray-200 mt-2">Total:</span>
                    <span className="text-right font-medium pt-2 border-t border-gray-200 mt-2">{formatCurrency(calculateBookingTotal(booking))}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full">
      {/* Desktop view */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Booking Details
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dates
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Payment Status
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {safeBookings.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  No bookings found
                </td>
              </tr>
            ) : (
              safeBookings.map(booking => {
                try {
                  return (
                    <React.Fragment key={booking.id}>
                      {renderDesktopBookingRow(booking)}
                      {expandedBookingId === booking.id && (
                        <tr>
                          <td colSpan={5} className="bg-blue-50 p-0">
                            <div className="p-4">
                              {/* Expanded booking view */}
                              <div className="grid md:grid-cols-2 gap-4 mb-4">
                                {/* Customer Info */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                  <h3 className="text-lg font-medium mb-3">Customer Information</h3>
                                  <div className="grid grid-cols-2 gap-2">
                                    <span className="text-gray-600">Name:</span>
                                    <span>{booking.customer?.name || 'N/A'}</span>
                                    
                                    <span className="text-gray-600">Email:</span>
                                    <span>{booking.customer?.email || 'N/A'}</span>
                                    
                                    <span className="text-gray-600">Phone:</span>
                                    <span>{booking.customer?.phone || 'N/A'}</span>
                                    
                                    <span className="text-gray-600">Address:</span>
                                    <span>{booking.customer?.address || 'N/A'}</span>
                                  </div>
                                </div>
                        
                                {/* Booking Summary */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                  <h3 className="text-lg font-medium mb-3">Booking Summary</h3>
                                  <div className="grid grid-cols-2 gap-2">
                                    <span className="text-gray-600">Status:</span>
                                    <div>
                                      <select
                                        value={booking.status}
                                        onChange={(e) => {
                                          const newStatus = e.target.value as 'pending' | 'confirmed' | 'completed' | 'cancelled';
                                          onStatusChange(booking.id, newStatus);
                                        }}
                                        className="border border-gray-300 rounded py-1 px-2 text-sm"
                                      >
                                        <option value="pending">Pending</option>
                                        <option value="confirmed">Confirmed</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                      </select>
                                    </div>
                                    
                                    <span className="text-gray-600">Quote #:</span>
                                    <span>{booking.quoteNumber}</span>
                                    
                                    <span className="text-gray-600">Date:</span>
                                    <span>{formatDate(booking.date)}</span>
                                    
                                    <span className="text-gray-600">Duration:</span>
                                    <span>{booking.rentalPeriod?.days || 0} {booking.rentalPeriod?.days === 1 ? 'day' : 'days'}</span>
                                    
                                    <span className="text-gray-600">Rental Type:</span>
                                    <span className="capitalize">{booking.rentalPeriod?.rentalType || 'daily'}</span>
                                  </div>
                                </div>
                              </div>
                              
                              {/* Payment Section */}
                              <div className="bg-white p-4 rounded-lg shadow border border-gray-200 mb-4">
                                <div className="flex justify-between items-center mb-3">
                                  <h3 className="text-lg font-medium">Payment Information</h3>
                                  <button
                                    onClick={() => onAddPayment(booking.id)}
                                    className="px-3 py-1 bg-green-600 text-white rounded-md text-sm flex items-center"
                                  >
                                    <Plus size={14} className="mr-1" /> Add Payment
                                  </button>
                                </div>
                                
                                {booking.payments && booking.payments.length > 0 ? (
                                  <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                      <thead className="bg-gray-50">
                                        <tr>
                                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                          <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                                          <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                          <th scope="col" className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                      </thead>
                                      <tbody className="bg-white divide-y divide-gray-200">
                                        {booking.payments.map(payment => (
                                          <tr key={payment.id}>
                                            <td className="px-4 py-2 whitespace-nowrap">{formatDate(payment.date)}</td>
                                            <td className="px-4 py-2 whitespace-nowrap capitalize">{payment.method}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{payment.reference || '-'}</td>
                                            <td className="px-4 py-2 whitespace-nowrap text-right">{formatCurrency(payment.amount)}</td>
                                            <td className="px-4 py-2 whitespace-nowrap text-center">
                                              <div className="flex justify-center space-x-2">
                                                <button
                                                  onClick={() => onEditPayment(booking.id, payment.id)}
                                                  className="text-blue-600 hover:text-blue-900"
                                                >
                                                  <Edit size={16} />
                                                </button>
                                                <button
                                                  onClick={() => onDeletePayment(booking.id, payment.id)}
                                                  className="text-red-600 hover:text-red-900"
                                                >
                                                  <Trash2 size={16} />
                                                </button>
                                              </div>
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : (
                                  <div className="text-center py-4 text-gray-500">
                                    No payments recorded
                                  </div>
                                )}
                                
                                {/* Payment Summary */}
                                <div className="mt-4 border-t pt-3">
                                  <div className="grid grid-cols-2 gap-2 max-w-xs ml-auto text-right">
                                    <span className="text-gray-600">Subtotal:</span>
                                    <span>{formatCurrency(booking.subtotal || 0)}</span>
                                    
                                    <span className="text-gray-600">Delivery:</span>
                                    <div>
                                      <select
                                        value={booking.delivery?.option?.id || 'none'}
                                        onChange={(e) => {
                                          const optionId = e.target.value;
                                          let selectedOption = null;
                                          let fee = 0;
                                          
                                          if (optionId !== 'none') {
                                            selectedOption = systemSettings.deliveryOptions.find(opt => opt.id === optionId) || null;
                                            fee = selectedOption?.fee || 0;
                                          }
                                          
                                          // Calculate new total with updated delivery fee
                                          const subtotal = booking.subtotal || 0;
                                          const discount = booking.discount || 0;
                                          const subtotalAfterDiscount = subtotal - discount;
                                          const tax = Math.round((subtotalAfterDiscount + fee) * (systemSettings.taxRate || 0) / 100 * 1000) / 1000;
                                          
                                          const updatedBooking = {
                                            ...booking,
                                            delivery: {
                                              option: selectedOption,
                                              fee
                                            },
                                            deliveryFee: fee,
                                            tax: tax
                                          };
                                          
                                          // Update booking in database
                                          updateBooking(updatedBooking).then(() => {
                                            // Update local state
                                            const updatedBookings = safeBookings.map(b => 
                                              b.id === booking.id ? updatedBooking : b
                                            );
                                            onBookingsUpdate(updatedBookings);
                                          }).catch((error) => {
                                            console.error('Error updating delivery option:', error);
                                          });
                                        }}
                                        className="border border-gray-300 rounded py-1 px-2 text-sm w-full"
                                      >
                                        <option value="none">Self Pickup (Free)</option>
                                        {systemSettings.deliveryOptions.map(opt => (
                                          <option key={opt.id} value={opt.id}>
                                            {opt.name} ({formatCurrency(opt.fee)})
                                          </option>
                                        ))}
                                      </select>
                                      <span className="block mt-1">{formatCurrency(booking.deliveryFee || 0)}</span>
                                    </div>
                                    
                                    <span className="text-gray-600">Discount:</span>
                                    <span className="text-right">-{formatCurrency(booking.discount || 0)}</span>
                                    
                                    <span className="text-gray-600">VAT ({systemSettings.taxRate || 0}%):</span>
                                    <span className="text-right">{formatCurrency(booking.tax || 0)}</span>
                                    
                                    <span className="text-gray-800 font-medium pt-2 border-t border-gray-200 mt-2">Total:</span>
                                    <span className="text-right font-medium pt-2 border-t border-gray-200 mt-2">{formatCurrency(calculateBookingTotal(booking))}</span>
                                  </div>
                                </div>
                              </div>
                  
                  {/* Add/Edit Discount Button */}
                  <div className="mb-4">
                    <button
                      onClick={() => {
                        setEditingBooking({ ...booking });
                        setExpandedBookingId(null);
                      }}
                      className="mt-2 px-3 py-1 bg-purple-600 text-white rounded-md text-sm flex items-center"
                    >
                      <Edit size={14} className="mr-1" /> Add/Edit Discount & Dates
                    </button>
                  </div>
                              
                  {/* Booking Items Section */}
                  <div>
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-lg font-medium">Booking Items</h3>
                      <button
                        onClick={() => handleEditItems(booking)}
                        className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
                      >
                        <Edit size={16} className="mr-1" /> Edit Items
                      </button>
                    </div>
                              
                    <BookingItems 
                      booking={booking} 
                      products={products}
                      systemSettings={systemSettings}
                      formatCurrency={formatCurrency}
                      onUpdate={(updatedBooking: Booking) => {
                        // Update the booking in the database
                        updateBooking(updatedBooking).then(() => {
                          // Update the local state
                          const updatedBookings = safeBookings.map(b => 
                            b.id === updatedBooking.id ? updatedBooking : b
                          );
                          onBookingsUpdate(updatedBookings);
                        }).catch((error: Error) => {
                          console.error('Error updating booking items:', error);
                          alert(`Failed to update booking items: ${error.message}`);
                        });
                      }}
                    />
                  </div>
                </td>
              </tr>
            )}
          </React.Fragment>
        </tbody>
      </table>
    </div>

    {/* Mobile view */}
    <div className="md:hidden">
      {safeBookings.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500">No bookings found</p>
        </div>
      ) : (
        safeBookings.map(booking => {
          try {
            return renderMobileBookingCard(booking);
          } catch (error) {
            console.error('Error rendering mobile booking card:', error, booking);
            return null; // Skip rendering this booking if there's an error
          }
        })
      )}
    </div>

    {/* Expanded booking details (shown for desktop only, mobile has inline expansion) */}
    <div className="hidden md:block">
      {bookings.map(booking => (
        expandedBookingId === booking.id && (
          <div key={`detail-${booking.id}`} className="bg-gray-50 border-t border-gray-200 p-4">
            {/* Status Change */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Booking Status</h3>
              <div className="flex flex-wrap gap-2">
                {['pending', 'confirmed', 'completed', 'cancelled'].map(status => (
                  <button
                    key={status}
                    onClick={() => onStatusChange(booking.id, status as any)}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      booking.status === status
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                    } capitalize`}
                  >
                    {status}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Payment Section */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Payment History</h3>
                <button
                  onClick={() => onAddPayment(booking.id)}
                  className="px-3 py-1 bg-green-600 text-white rounded-md text-sm flex items-center"
                >
                  <Plus size={14} className="mr-1" /> Add Payment
                </button>
              </div>
              
              {(!booking.payments || booking.payments.length === 0) ? (
                <p className="text-gray-500 text-sm">No payments recorded yet.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Method
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {booking.payments.map(payment => (
                        <tr key={payment.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatDate(payment.date)}</div>
                            <div className="text-xs text-gray-500">{payment.transactionId}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {payment.method === 'cash' ? (
                                <DollarSign size={16} className="mr-2 text-green-500" />
                              ) : (
                                <CreditCard size={16} className="mr-2 text-blue-500" />
                              )}
                              <span className="text-sm capitalize">{payment.method}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <div className="text-sm font-medium text-gray-900">{formatCurrency(payment.amount)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center space-x-2">
                              <button
                                onClick={() => onEditPayment(booking.id, payment.id)}
                                className="p-1 rounded-full hover:bg-gray-100 text-gray-600"
                              >
                                <Edit size={16} />
                              </button>
                              <button
                                onClick={() => onDeletePayment(booking.id, payment.id)}
                                className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Cost Breakdown Section */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Cost Breakdown</h3>
              <div className="bg-white p-4 rounded shadow-sm border border-gray-200 max-w-md">
                <div className="grid grid-cols-2 gap-y-2">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="text-right">{formatCurrency(booking.subtotal || 0)}</span>
                  
                  {/* Delivery Options Dropdown in mobile view */}
                  <span className="text-gray-600">Delivery:</span>
                  <div className="text-right">
                    <select
                      value={booking.delivery?.option?.id || 'none'}
                      onChange={(e) => {
                        const optionId = e.target.value;
                        let selectedOption = null;
                        let fee = 0;
                        
                        if (optionId !== 'none') {
                          selectedOption = systemSettings.deliveryOptions.find(opt => opt.id === optionId) || null;
                          fee = selectedOption?.fee || 0;
                        }
                        
                        // Calculate new total with updated delivery fee
                        const subtotal = booking.subtotal || 0;
                        const discount = booking.discount || 0;
                        const subtotalAfterDiscount = subtotal - discount;
                        const tax = Math.round((subtotalAfterDiscount + fee) * (systemSettings.taxRate || 0) / 100 * 1000) / 1000;
                        
                        const updatedBooking = {
                          ...booking,
                          delivery: {
                            option: selectedOption,
                            fee
                          },
                          deliveryFee: fee,
                          tax: tax
                        };
                        
                        // Update booking in database
                        updateBooking(updatedBooking).then(() => {
                          // Update local state
                          const updatedBookings = safeBookings.map(b => 
                            b.id === booking.id ? updatedBooking : b
                          );
                          onBookingsUpdate(updatedBookings);
                        }).catch((error) => {
                          console.error('Error updating delivery option:', error);
                        });
                      }}
                      className="border border-gray-300 rounded py-1 px-2 text-sm w-full"
                    >
                      <option value="none">Self Pickup (Free)</option>
                      {systemSettings.deliveryOptions.map(opt => (
                        <option key={opt.id} value={opt.id}>
                          {opt.name} ({formatCurrency(opt.fee)})
                        </option>
                      ))}
                    </select>
                    <span className="block mt-1">{formatCurrency(booking.deliveryFee || 0)}</span>
                  </div>
                  
                  <span className="text-gray-600">Discount:</span>
                  <span className="text-right">-{formatCurrency(booking.discount || 0)}</span>
                  
                  <span className="text-gray-600">VAT ({systemSettings.taxRate || 0}%):</span>
                  <span className="text-right">{formatCurrency(booking.tax || 0)}</span>
                  
                  <span className="text-gray-800 font-medium pt-2 border-t border-gray-200 mt-2">Total:</span>
                  <span className="text-right font-medium pt-2 border-t border-gray-200 mt-2">{formatCurrency(calculateBookingTotal(booking))}</span>
                </div>
              </div>
            </div>

            {/* Add/Edit Discount Button */}
            <div className="mb-4">
              <button
                onClick={() => {
                  setEditingBooking({ ...booking });
                  setExpandedBookingId(null);
                }}
                className="mt-2 px-3 py-1 bg-purple-600 text-white rounded-md text-sm flex items-center"
              >
                <Edit size={14} className="mr-1" /> Add/Edit Discount & Dates
              </button>
            </div>
            
            {/* Booking Items Section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-lg font-medium">Booking Items</h3>
                <button
                  onClick={() => handleEditItems(booking)}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
                >
                  <Edit size={16} className="mr-1" /> Edit Items
                </button>
              </div>
              
              <BookingItems 
                booking={booking} 
                products={products}
                systemSettings={systemSettings}
                formatCurrency={formatCurrency}
                onUpdate={(updatedBooking: Booking) => {
                  // Update the booking in the database
                  updateBooking(updatedBooking).then(() => {
                    // Update the local state
                    const updatedBookings = safeBookings.map(b => 
                      b.id === updatedBooking.id ? updatedBooking : b
                    );
                    onBookingsUpdate(updatedBookings);
                  }).catch((error: Error) => {
                    console.error('Error updating booking items:', error);
                    alert(`Failed to update booking items: ${error.message}`);
                  });
                }}
              />
            </div>
          </div>
        )
      ))}
    </div>

    {/* Edit booking items modal */}
    {editingBooking && (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold">Edit Booking</h2>
            <button 
              onClick={() => setEditingBooking(null)}
              className="text-gray-400 hover:text-gray-500"
            >
              <X size={24} />
            </button>
          </div>
          <div className="p-6">
            {/* Booking Dates */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">Rental Period</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={editingBooking.rentalPeriod.startDate.split('T')[0]}
                    onChange={(e) => {
                      const startDate = e.target.value;
                      const endDate = editingBooking.rentalPeriod.endDate;
                      
                      // Calculate days between dates
                      const start = new Date(startDate);
                      const end = new Date(endDate);
                      const diffTime = Math.abs(end.getTime() - start.getTime());
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                      
                      setEditingBooking({
                        ...editingBooking,
                        rentalPeriod: {
                          ...editingBooking.rentalPeriod,
                          startDate: startDate + 'T00:00:00.000Z',
                          days: diffDays
                        }
                      });
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={editingBooking.rentalPeriod.endDate.split('T')[0]}
                    onChange={(e) => {
                      const endDate = e.target.value;
                      const startDate = editingBooking.rentalPeriod.startDate;
                      
                      // Calculate days between dates
                      const start = new Date(startDate);
                      const end = new Date(endDate);
                      const diffTime = Math.abs(end.getTime() - start.getTime());
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                      
                      setEditingBooking({
                        ...editingBooking,
                        rentalPeriod: {
                          ...editingBooking.rentalPeriod,
                          endDate: endDate + 'T23:59:59.000Z',
                          days: diffDays
                        }
                      });
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    min={editingBooking.rentalPeriod.startDate.split('T')[0]}
                  />
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm text-gray-600">
                  Duration: {editingBooking.rentalPeriod.days} {editingBooking.rentalPeriod.days === 1 ? 'day' : 'days'}
                </p>
              </div>
            </div>

            {/* Discount Section */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">Discount</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Discount Type
                  </label>
                  <select
                    value={editingBooking.coupon ? (editingBooking.coupon.discountType || 'fixed') : 'fixed'}
                    onChange={(e) => {
                      const discountType = e.target.value as 'fixed' | 'percentage';
                      const discountValue = editingBooking.coupon ? editingBooking.coupon.discountValue : 0;
                      
                      setEditingBooking({
                        ...editingBooking,
                        coupon: {
                          id: `manual-${Date.now()}`,
                          code: 'MANUAL',
                          discountType,
                          discountValue,
                          expiryDate: new Date().toISOString(),
                          active: true
                        },
                        discount: discountType === 'percentage' 
                          ? (editingBooking.subtotal || 0) * discountValue / 100 
                          : discountValue
                      });
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="fixed">Fixed Amount</option>
                    <option value="percentage">Percentage</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Discount Value
                  </label>
                  <div className="relative">
                    {editingBooking.coupon && editingBooking.coupon.discountType === 'percentage' ? (
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                    ) : (
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">BHD</span>
                    )}
                    <input
                      type="number"
                      value={editingBooking.coupon ? editingBooking.coupon.discountValue : 0}
                      onChange={(e) => {
                        const discountValue = parseFloat(e.target.value) || 0;
                        const discountType = editingBooking.coupon ? editingBooking.coupon.discountType : 'fixed';
                        
                        setEditingBooking({
                          ...editingBooking,
                          coupon: {
                            id: `manual-${Date.now()}`,
                            code: 'MANUAL',
                            discountType: discountType as 'fixed' | 'percentage',
                            discountValue,
                            expiryDate: new Date().toISOString(),
                            active: true
                          },
                          discount: discountType === 'percentage' 
                            ? (editingBooking.subtotal || 0) * discountValue / 100 
                            : discountValue
                        });
                      }}
                      className={`w-full ${editingBooking.coupon && editingBooking.coupon.discountType === 'percentage' ? 'pr-8' : 'pl-10'} py-2 border border-gray-300 rounded-md`}
                      min="0"
                      step={editingBooking.coupon && editingBooking.coupon.discountType === 'percentage' ? "1" : "0.001"}
                      max={editingBooking.coupon && editingBooking.coupon.discountType === 'percentage' ? "100" : undefined}
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <div className="w-full p-3 bg-gray-100 rounded-md">
                    <p className="text-sm font-medium">Discount Amount:</p>
                    <p className="text-lg font-bold">{formatCurrency(editingBooking.discount || 0)}</p>
                  </div>
                </div>
              </div>
            </div>

            <BookingItems 
              booking={editingBooking}
              products={products}
              systemSettings={systemSettings}
              formatCurrency={formatCurrency}
              onBookingUpdate={setEditingBooking}
              maxItemsPerPage={5}
            />
            <div className="flex justify-end mt-6 space-x-3">
              <button
                onClick={() => setEditingBooking(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveBookingChanges}
                className="px-4 py-2 bg-blue-600 text-white rounded-md"
              >
                Update Booking
              </button>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Payment Modal */}
    {showPaymentModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-6 w-full max-w-md">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">{editingPaymentId ? 'Edit Payment' : 'Add Payment'}</h2>
            <button
              onClick={() => {
                setShowPaymentModal(false);
                setEditingPaymentId(null);
              }}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <X size={20} />
            </button>
          </div>
          
          <div className="space-y-4">
            {/* Amount */}
            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Payment Amount (BHD)*
              </label>
              <div className="relative">
                <DollarSign size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  value={newPayment.amount}
                  onChange={(e) => setNewPayment({...newPayment, amount: e.target.value})}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
                  placeholder="0.000"
                  min="0"
                  step="0.001"
                />
              </div>
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Payment Method
              </label>
              <div className="grid grid-cols-3 gap-2">
                <button
                  onClick={() => setNewPayment({...newPayment, method: 'cash'})}
                  className={`flex items-center justify-center p-3 rounded-md ${
                    newPayment.method === 'cash' 
                      ? 'bg-green-100 text-green-800 ring-1 ring-green-300' 
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                >
                  <DollarSign size={18} className="mr-1" />
                  Cash
                </button>
                <button
                  onClick={() => setNewPayment({...newPayment, method: 'card'})}
                  className={`flex items-center justify-center p-3 rounded-md ${
                    newPayment.method === 'card' 
                      ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-300' 
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                >
                  <CreditCard size={18} className="mr-1" />
                  Card
                </button>
                <button
                  onClick={() => setNewPayment({...newPayment, method: 'bank_transfer'})}
                  className={`flex items-center justify-center p-3 rounded-md ${
                    newPayment.method === 'bank_transfer' 
                      ? 'bg-purple-100 text-purple-800 ring-1 ring-purple-300' 
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                >
                  <Building size={18} className="mr-1" />
                  Bank
                </button>
              </div>
            </div>

            {/* Reference */}
            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Reference / Transaction ID
              </label>
              <input
                type="text"
                value={newPayment.reference}
                onChange={(e) => setNewPayment({...newPayment, reference: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter reference or transaction ID"
              />
            </div>

            {/* Notes */}
            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Notes
              </label>
              <textarea
                value={newPayment.notes}
                onChange={(e) => setNewPayment({...newPayment, notes: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
                placeholder="Add any additional notes"
              />
            </div>
          </div>

          <div className="flex justify-end mt-6 space-x-2">
            <button
              onClick={() => {
                setShowPaymentModal(false);
                setEditingPaymentId(null);
              }}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmitPayment}
              disabled={!newPayment.amount || parseFloat(newPayment.amount) <= 0}
              className={`px-4 py-2 rounded-md ${
                newPayment.amount && parseFloat(newPayment.amount) > 0
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {editingPaymentId ? 'Update Payment' : 'Add Payment'}
            </button>
          </div>
        </div>
      </div>
    )}
  </div>
);
};

export default BookingList;