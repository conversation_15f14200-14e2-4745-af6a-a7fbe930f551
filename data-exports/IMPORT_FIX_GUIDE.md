# Import Fix Guide - Foreign Key Constraint Error

## 🚨 Problem
You're getting this error:
```
#1452 - Cannot add or update a child row: a foreign key constraint fails
```

This happens because the data is being imported in the wrong order, and child records (booking_products) are trying to reference parent records (bookings) that don't exist yet.

## ✅ Solution

### Option 1: Use the Safe Import Script (Recommended)

1. **Delete the problematic data first:**
   ```sql
   SET FOREIGN_KEY_CHECKS = 0;
   DELETE FROM booking_products;
   DELETE FROM payments;
   DELETE FROM bookings;
   DELETE FROM products;
   DELETE FROM categories;
   DELETE FROM users;
   SET FOREIGN_KEY_CHECKS = 1;
   ```

2. **Import using the safe script:**
   - Use `import_data_safely.sql` instead of `complete_data_export.sql`
   - This script imports data in the correct order with proper foreign key handling

### Option 2: Fix the Current Import

If you want to continue with the current import, follow these steps:

1. **Clear the problematic tables:**
   ```sql
   SET FOREIGN_KEY_CHECKS = 0;
   DELETE FROM booking_products;
   DELETE FROM payments;
   SET FOREIGN_KEY_CHECKS = 1;
   ```

2. **Import in correct order:**
   ```sql
   -- First import bookings (make sure all bookings exist)
   -- Then import booking_products
   -- Finally import payments
   ```

### Option 3: Disable Foreign Key Checks Temporarily

1. **In phpMyAdmin, run this before importing:**
   ```sql
   SET FOREIGN_KEY_CHECKS = 0;
   ```

2. **Import your data**

3. **Re-enable foreign key checks:**
   ```sql
   SET FOREIGN_KEY_CHECKS = 1;
   ```

## 🔍 Root Cause Analysis

The error occurs because:
1. `booking_products` table has a foreign key to `bookings.id`
2. When importing `booking_products`, some booking IDs don't exist in the `bookings` table yet
3. MySQL enforces referential integrity and blocks the insert

## 📋 Verification Steps

After successful import, verify your data:

```sql
-- Check record counts
SELECT 'Users' as Table_Name, COUNT(*) as Record_Count FROM users
UNION ALL
SELECT 'Bookings', COUNT(*) FROM bookings
UNION ALL
SELECT 'Booking Products', COUNT(*) FROM booking_products
UNION ALL
SELECT 'Payments', COUNT(*) FROM payments;

-- Check for orphaned records
SELECT bp.booking_id 
FROM booking_products bp 
LEFT JOIN bookings b ON bp.booking_id = b.id 
WHERE b.id IS NULL;

-- Check for orphaned payments
SELECT p.booking_id 
FROM payments p 
LEFT JOIN bookings b ON p.booking_id = b.id 
WHERE b.id IS NULL;
```

## 🎯 Recommended Approach

**Use `import_data_safely.sql`** - This script:
- ✅ Disables foreign key checks temporarily
- ✅ Imports data in correct order (parents first, children second)
- ✅ Uses `INSERT IGNORE` to avoid duplicate key errors
- ✅ Re-enables foreign key checks
- ✅ Provides verification queries

## 🚀 Quick Fix Commands

If you're in phpMyAdmin right now:

```sql
-- 1. Clear problematic data
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM booking_products;
DELETE FROM payments;
SET FOREIGN_KEY_CHECKS = 1;

-- 2. Import bookings first (from your bookings.sql)
-- 3. Then import booking_products
-- 4. Finally import payments
```

## 📞 Need Help?

If you're still having issues:
1. Check that all booking IDs in `booking_products` exist in `bookings` table
2. Verify the database schema matches the migration script
3. Use the safe import script provided
4. Contact support with the specific error message

---

**The `import_data_safely.sql` file is ready to use and should resolve all foreign key constraint issues!**
