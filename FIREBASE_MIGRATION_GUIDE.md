# Firebase to MySQL Migration Guide

This guide will help you migrate all your existing data from Firebase to the new MySQL database.

## 📋 Prerequisites

1. **Firebase Admin SDK Access**: You need your Firebase service account key
2. **Firebase Project Access**: Admin access to your Firebase project
3. **Node.js Dependencies**: Firebase Admin SDK and MySQL2

## 🔧 Setup Steps

### Step 1: Install Required Dependencies

```bash
npm install firebase-admin mysql2
```

### Step 2: Get Firebase Service Account Key

1. Go to your [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** (gear icon)
4. Click on **Service Accounts** tab
5. Click **Generate new private key**
6. Download the JSON file
7. Save it as `scripts/firebase-service-account-key.json`

### Step 3: Update Migration Script Configuration

Edit `scripts/migrateFromFirebase.js` and update:

```javascript
// Update this line with your Firebase project URL
databaseURL: "https://YOUR-PROJECT-ID-default-rtdb.firebaseio.com"

// Update MySQL configuration if needed
const mysqlConfig = {
  host: 'localhost',
  user: 'root',
  password: '', // Add your MySQL password if you have one
  database: 'teamwork_app'
};
```

### Step 4: Run the Migration

```bash
node scripts/migrateFromFirebase.js
```

## 📊 What Gets Migrated

The migration script will transfer:

✅ **Products/Equipment**
- All product details (name, category, SKU, rates, etc.)
- Images and descriptions
- Vendor information
- Stock quantities

✅ **Bookings**
- All booking records
- Customer information
- Rental periods and dates
- Product associations
- Payment status

✅ **Clients**
- Customer profiles
- Contact information
- Booking history
- Payment records

✅ **Users**
- Admin users
- Roles and permissions
- Login credentials (passwords reset for security)

✅ **Coupons**
- Discount codes
- Expiry dates
- Usage rules

✅ **System Settings**
- Tax rates
- Quote numbering
- Delivery options
- General configuration

## 🔐 Security Notes

- **Passwords**: All user passwords will be reset to `defaultpassword` for security
- **Data Validation**: The script validates and sanitizes all data during migration
- **Backup**: Your Firebase data remains unchanged - this is a copy operation

## 🚨 Troubleshooting

### Common Issues:

1. **"Permission denied" error**
   - Make sure your service account key has the correct permissions
   - Verify the JSON file path is correct

2. **"Database connection failed"**
   - Check MySQL is running: `brew services start mysql`
   - Verify database exists: `mysql -u root -e "SHOW DATABASES;"`

3. **"Collection not found"**
   - Some collections might be empty in your Firebase
   - This is normal - the script will skip empty collections

4. **Duplicate key errors**
   - The script uses `ON DUPLICATE KEY UPDATE` to handle existing records
   - Safe to re-run if migration is interrupted

### Manual Verification:

After migration, verify your data:

```bash
# Check products
mysql -u root teamwork_app -e "SELECT COUNT(*) as products FROM products;"

# Check bookings
mysql -u root teamwork_app -e "SELECT COUNT(*) as bookings FROM bookings;"

# Check clients
mysql -u root teamwork_app -e "SELECT COUNT(*) as clients FROM clients;"

# Check users
mysql -u root teamwork_app -e "SELECT COUNT(*) as users FROM users;"
```

## 📝 Post-Migration Steps

1. **Update Passwords**: Change default passwords for all users
2. **Verify Data**: Check that all critical data has been migrated
3. **Test Functionality**: Test booking creation, product management, etc.
4. **Update Settings**: Review and update system settings as needed

## 🔄 Re-running Migration

The migration script is safe to run multiple times:
- Existing records will be updated, not duplicated
- New records will be added
- No data will be lost

## 📞 Support

If you encounter any issues:

1. Check the console output for specific error messages
2. Verify your Firebase permissions and MySQL connection
3. Ensure all prerequisites are met
4. The migration preserves all your existing data structure

---

**Note**: This migration is designed to be safe and non-destructive. Your original Firebase data remains untouched.
