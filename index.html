<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TW - Booking Manager</title>

    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="TW App">
    <link rel="apple-touch-icon" href="/logo192.png">

    <!-- Web app manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Theme color -->
    <meta name="theme-color" content="#000000">

    <!-- OneSignal SDK -->
    <script src="https://cdn.onesignal.com/sdks/OneSignalSDK.js" async=""></script>
    <script>
      // Initialize OneSignal
      window.OneSignal = window.OneSignal || [];

      // Enable debug mode
      window.OneSignal.push(["setLogLevel", 4]);

      // Initialize OneSignal
      window.OneSignal.push(["init", {
        appId: "************************************",
        safari_web_id: "web.onesignal.auto.************************************",
        allowLocalhostAsSecureOrigin: true,
        autoRegister: false,
        notifyButton: {
          enable: true,
          size: 'medium',
          theme: 'default',
          position: 'bottom-right',
          offset: {
            bottom: '20px',
            right: '20px'
          },
          showCredit: false,
          text: {
            'tip.state.unsubscribed': 'Subscribe to notifications',
            'tip.state.subscribed': 'You are subscribed to notifications',
            'tip.state.blocked': 'You have blocked notifications',
            'message.prenotify': 'Click to subscribe to notifications',
            'message.action.subscribed': 'Thanks for subscribing!',
            'message.action.resubscribed': 'You are subscribed to notifications',
            'message.action.unsubscribed': 'You will not receive notifications',
            'dialog.main.title': 'Manage Site Notifications',
            'dialog.main.button.subscribe': 'SUBSCRIBE',
            'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
            'dialog.blocked.title': 'Unblock Notifications',
            'dialog.blocked.message': 'Follow these instructions to allow notifications:'
          }
        },
        promptOptions: {
          slidedown: {
            prompts: [
              {
                type: 'push',
                autoPrompt: true,
                text: {
                  actionMessage: 'Would you like to receive notifications about new bookings and updates?',
                  acceptButton: 'Allow',
                  cancelButton: 'Cancel'
                },
                delay: {
                  pageViews: 1,
                  timeDelay: 5
                }
              }
            ]
          }
        },
        welcomeNotification: {
          title: "Teamwork App",
          message: "Thanks for subscribing to notifications!"
        },
        persistNotification: false
      }]);

      // Log subscription status
      window.OneSignal.push(function() {
        window.OneSignal.isPushNotificationsEnabled(function(isEnabled) {
          console.log('OneSignal Subscription Status:', isEnabled);

          // Get and log the player ID
          window.OneSignal.getUserId(function(userId) {
            console.log('OneSignal User ID:', userId);
          });

          // Test notification on page load (for debugging)
          if (isEnabled && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
            console.log('User is subscribed, showing test notification button');

            // Create a test button (only visible during development)
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Notification';
            testButton.style.position = 'fixed';
            testButton.style.bottom = '80px';
            testButton.style.right = '20px';
            testButton.style.zIndex = '9999';
            testButton.style.padding = '10px';
            testButton.style.backgroundColor = '#4CAF50';
            testButton.style.color = 'white';
            testButton.style.border = 'none';
            testButton.style.borderRadius = '5px';
            testButton.style.cursor = 'pointer';

            testButton.onclick = function() {
              // Show a browser notification
              if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🔔 Test Notification', {
                  body: 'This is a test notification',
                  icon: '/logo192.png'
                });
              }
            };

            document.body.appendChild(testButton);
          }
        });
      });

      // Handle subscription changes
      window.OneSignal.push(function() {
        window.OneSignal.on('subscriptionChange', function(isSubscribed) {
          console.log('OneSignal Subscription Changed:', isSubscribed);
        });
      });

      // Handle notification display
      window.OneSignal.push(function() {
        window.OneSignal.on('notificationDisplay', function(event) {
          console.log('OneSignal Notification Displayed:', event);
        });
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
