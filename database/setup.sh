#!/bin/bash

# TeamWork Rental Management System - Database Setup Script
# This script sets up the MySQL database for the PHP backend

echo "🚀 Setting up TeamWork Rental Management Database..."

# Check if MySQL is running
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL is not installed or not in PATH"
    echo "Please install MySQL first:"
    echo "  - macOS: brew install mysql"
    echo "  - Ubuntu: sudo apt-get install mysql-server"
    echo "  - Windows: Download from https://dev.mysql.com/downloads/"
    exit 1
fi

# Database configuration
DB_NAME="teamwork_app"
DB_USER="root"
DB_PASSWORD=""

echo "📋 Database Configuration:"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo "  Host: localhost"
echo ""

# Create database
echo "📊 Creating database '$DB_NAME'..."
mysql -u $DB_USER -p$DB_PASSWORD -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Database '$DB_NAME' created successfully"
else
    echo "❌ Failed to create database. Please check your MySQL credentials."
    echo "You may need to run: mysql -u root -p"
    echo "Then manually create the database: CREATE DATABASE teamwork_app;"
    exit 1
fi

# Import schema
echo "📋 Importing database schema..."
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < schema.sql

if [ $? -eq 0 ]; then
    echo "✅ Database schema imported successfully"
else
    echo "❌ Failed to import schema"
    exit 1
fi

echo ""
echo "🎉 Database setup completed successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Start your web server (Apache/Nginx) with PHP support"
echo "2. Place the 'api' folder in your web root directory"
echo "3. Update the database configuration in api/config/database.php if needed"
echo "4. Start the React development server: npm run dev"
echo ""
echo "🔐 Default login credentials:"
echo "  Username: admin"
echo "  Password: defaultpassword"
echo ""
echo "⚠️  Security Note: Change the default password after first login!"
echo ""
echo "🌐 For Hostinger deployment:"
echo "1. Create a MySQL database in your Hostinger control panel"
echo "2. Import the schema.sql file to your Hostinger database"
echo "3. Update the database credentials in your .env file"
echo "4. Upload the 'api' folder to your public_html directory"
