-- TeamWork Rental Management System Database Schema
-- MySQL Database Schema for PHP Backend

-- Create database (run this first)
-- CREATE DATABASE IF NOT EXISTS teamwork_app;
-- USE teamwork_app;

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255) NOT NULL,
    sku VARCHAR(255) UNIQUE NOT NULL,
    barcode VARCHAR(255),
    daily_rate DECIMAL(10,2) NOT NULL,
    weekly_rate DECIMAL(10,2),
    image TEXT,
    description TEXT,
    available BOOLEAN DEFAULT true,
    quantity INT DEFAULT 0,
    stock INT DEFAULT 0,
    featured <PERSON><PERSON><PERSON>EA<PERSON> DEFAULT false,
    custom_days INT,
    custom_price DECIMAL(10,2),
    temporary_daily_rate DECIMAL(10,2),
    temporary_weekly_rate DECIMAL(10,2),
    is_external_vendor_item BOOLEAN DEFAULT false,
    vendor_id VARCHAR(255),
    vendor_sku VARCHAR(255),
    vendor_cost DECIMAL(10,2),
    profit_margin DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table
CREATE TABLE IF NOT EXISTS clients (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_bookings INT DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    pending_payment DECIMAL(10,2) DEFAULT 0,
    last_booking TIMESTAMP,
    notes TEXT
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id VARCHAR(255) PRIMARY KEY,
    quote_number VARCHAR(255) UNIQUE NOT NULL,
    date TIMESTAMP NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    customer_address TEXT,
    rental_dates JSON NOT NULL,
    rental_days INT NOT NULL,
    rental_type ENUM('daily', 'weekly') NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    tax DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    remaining_amount DECIMAL(10,2) DEFAULT 0,
    delivery_option_id VARCHAR(255),
    coupon_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Booking products table (for many-to-many relationship)
CREATE TABLE IF NOT EXISTS booking_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity INT NOT NULL,
    daily_rate DECIMAL(10,2) NOT NULL,
    weekly_rate DECIMAL(10,2),
    total_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Coupons table
CREATE TABLE IF NOT EXISTS coupons (
    id VARCHAR(255) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    discount_type ENUM('percentage', 'fixed') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    expiry_date DATE,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(255) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Vendors table
CREATE TABLE IF NOT EXISTS vendors (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    notes TEXT,
    payment_terms VARCHAR(255),
    active BOOLEAN DEFAULT true,
    tax_percentage DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vendor transactions table
CREATE TABLE IF NOT EXISTS vendor_transactions (
    id VARCHAR(255) PRIMARY KEY,
    vendor_id VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    date TIMESTAMP NOT NULL,
    type ENUM('payment', 'invoice') NOT NULL,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    booking_id VARCHAR(255),
    reference VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE CASCADE
);

-- Client activities table
CREATE TABLE IF NOT EXISTS client_activities (
    id VARCHAR(255) PRIMARY KEY,
    client_id VARCHAR(255) NOT NULL,
    date TIMESTAMP NOT NULL,
    type ENUM('booking', 'quote', 'payment', 'note', 'merge') NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(10,2),
    booking_id VARCHAR(255),
    quote_number VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id VARCHAR(50) PRIMARY KEY,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    enable_tax BOOLEAN DEFAULT false,
    last_quote_number INT DEFAULT 0,
    settings_data JSON,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Delivery options table
CREATE TABLE IF NOT EXISTS delivery_options (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    fee DECIMAL(10,2) NOT NULL,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id VARCHAR(255) PRIMARY KEY,
    booking_id VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    transaction_id VARCHAR(255),
    date TIMESTAMP NOT NULL,
    method ENUM('cash', 'card', 'bank_transfer', 'cheque', 'other') NOT NULL,
    reference VARCHAR(255),
    notes TEXT,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT IGNORE INTO users (id, email, username, name, password, role, is_active)
VALUES ('admin_001', '<EMAIL>', 'admin', 'System Administrator', 'defaultpassword', 'admin', true);

-- Insert default system settings
INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data)
VALUES ('general', 10, true, 1000, JSON_OBJECT(
    'taxRate', 10,
    'enableTax', true,
    'lastQuoteNumber', 1000,
    'deliveryOptions', JSON_ARRAY(
        JSON_OBJECT('id', 'pickup', 'name', 'Customer Pickup', 'description', 'Customer will pick up items from our location', 'fee', 0),
        JSON_OBJECT('id', 'local_delivery', 'name', 'Local Delivery', 'description', 'Delivery within 10km radius', 'fee', 25),
        JSON_OBJECT('id', 'extended_delivery', 'name', 'Extended Delivery', 'description', 'Delivery beyond 10km radius', 'fee', 50)
    )
))
ON DUPLICATE KEY UPDATE
tax_rate = VALUES(tax_rate),
enable_tax = VALUES(enable_tax),
last_quote_number = VALUES(last_quote_number),
settings_data = VALUES(settings_data);

-- Insert sample coupon
INSERT IGNORE INTO coupons (id, code, discount_type, discount_value, expiry_date, active)
VALUES ('coupon_001', 'WELCOME10', 'percentage', 10, DATE_ADD(CURDATE(), INTERVAL 1 YEAR), true);

-- Insert sample products
INSERT IGNORE INTO products (id, name, category, sku, barcode, daily_rate, weekly_rate, description, available, quantity, stock, featured)
VALUES
('prod_001', 'Professional Camera', 'Photography', 'CAM001', '123456789001', 50.00, 300.00, 'High-quality DSLR camera for professional photography', true, 5, 5, true),
('prod_002', 'LED Light Panel', 'Lighting', 'LED001', '123456789002', 25.00, 150.00, 'Professional LED light panel for video and photography', true, 10, 10, false),
('prod_003', 'Tripod Stand', 'Accessories', 'TRI001', '123456789003', 15.00, 90.00, 'Sturdy tripod stand for cameras and lights', true, 8, 8, false),
('prod_004', 'Wireless Microphone', 'Audio', 'MIC001', '123456789004', 30.00, 180.00, 'Professional wireless microphone system', true, 6, 6, true),
('prod_005', 'Video Projector', 'Display', 'PROJ001', '123456789005', 75.00, 450.00, 'High-resolution video projector for presentations', true, 3, 3, true);

-- Insert sample clients
INSERT IGNORE INTO clients (id, name, email, phone, address, total_bookings, total_spent, pending_payment, notes)
VALUES
('<EMAIL>', 'John Smith', '<EMAIL>', '******-0101', '123 Main St, City, State 12345', 0, 0, 0, 'Regular customer, prefers weekend rentals'),
('<EMAIL>', 'Sarah Johnson', '<EMAIL>', '******-0102', '456 Oak Ave, City, State 12345', 0, 0, 0, 'Event planner, often books multiple items'),
('<EMAIL>', 'Mike Davis', '<EMAIL>', '******-0103', '789 Pine St, City, State 12345', 0, 0, 0, 'Photography business owner');
