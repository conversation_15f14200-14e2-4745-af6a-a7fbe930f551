// Test function for OneSignal notifications
import { ONESIGNAL_APP_ID, ONESIGNAL_REST_API_KEY } from '../config/oneSignalConfig';
import { getOneSignalPlayerId } from './oneSignalService';

/**
 * Send a test notification using the OneSignal REST API
 * This function is for testing purposes only
 */
export const sendTestNotification = async (): Promise<void> => {
  try {
    console.log('Sending test notification...');

    // Try to get the current device ID
    const playerId = await getOneSignalPlayerId();
    console.log('Current OneSignal Player ID for test:', playerId);

    // Create the notification payload
    const payload: any = {
      app_id: ONESIGNAL_APP_ID,
      headings: { en: '🔔 Test Notification' },
      contents: { en: 'This is a test notification from the app' },
      url: window.location.origin,
      chrome_web_icon: `${window.location.origin}/logo192.png`,
      firefox_icon: `${window.location.origin}/logo192.png`,
      data: { test: true, timestamp: new Date().toISOString() }
    };

    // Always include the 'All' segment to ensure all subscribers get the notification
    payload.included_segments = ['All'];

    // If we have a player ID, also target that specific device to ensure the current user gets it
    if (playerId) {
      payload.include_player_ids = [playerId];
    }

    console.log('Test notification payload:', payload);

    // Send the notification using the REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    const responseData = await response.json();

    if (response.ok) {
      console.log('✅ Test notification sent successfully:', responseData);
      alert('Test notification sent successfully! Check your device.');

      // Also try a direct notification using OneSignal's client API
      if (window.OneSignal) {
        try {
          // Try to show a notification using the browser's Notification API
          if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('🔔 Test Notification (Browser)', {
              body: 'This is a browser test notification',
              icon: '/logo192.png',
              data: { test: true, browser: true, timestamp: new Date().toISOString() }
            });
          }

          // Also try to use OneSignal's API to show a notification
          window.OneSignal.push(function() {
            try {
              // For newer versions of OneSignal - try different methods
              if (typeof window.OneSignal.showNativeNotification === 'function') {
                window.OneSignal.showNativeNotification({
                  title: '🔔 Test Notification (Direct)',
                  message: 'This is a direct test notification',
                  url: window.location.origin,
                  icon: '/logo192.png',
                  data: { test: true, direct: true, timestamp: new Date().toISOString() }
                });
                console.log('Sent test notification using showNativeNotification');
              } else if (typeof window.OneSignal.sendSelfNotification === 'function') {
                // Try the older method
                window.OneSignal.sendSelfNotification(
                  '🔔 Test Notification (Direct)',
                  'This is a direct test notification',
                  window.location.origin,
                  '/logo192.png',
                  { test: true, direct: true, timestamp: new Date().toISOString() }
                );
                console.log('Sent test notification using sendSelfNotification');
              } else {
                // Last resort - try to use the notification API directly
                console.log('No direct notification method available in OneSignal');
              }
            } catch (error) {
              console.warn('Error using OneSignal notification methods:', error);
            }
          });
        } catch (clientError) {
          console.warn('Error showing client-side notification:', clientError);
        }
      }
    } else {
      console.error('❌ Failed to send test notification:', responseData);
      alert(`Failed to send test notification: ${JSON.stringify(responseData)}`);

      // Try with different targeting options
      console.log('First attempt failed, retrying with different targeting options...');
      console.log('Error details:', responseData.errors);

      // Try with just the 'Subscribed Users' segment
      payload.included_segments = ['Subscribed Users'];
      if (payload.include_player_ids) {
        delete payload.include_player_ids;
      }

      console.log('Retrying with segment "Subscribed Users"...');

      const retryResponse = await fetch('https://onesignal.com/api/v1/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
        },
        body: JSON.stringify(payload)
      });

      const retryData = await retryResponse.json();

      if (retryResponse.ok && !retryData.errors) {
        console.log('✅ Retry test notification sent successfully:', retryData);
        alert('Retry test notification sent successfully! Check your device.');
      } else {
        console.error('❌ Retry also failed:', retryData);

        // Try one more time with 'All' segment only
        payload.included_segments = ['All'];
        console.log('Final retry with segment "All" only...');

        const finalResponse = await fetch('https://onesignal.com/api/v1/notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
          },
          body: JSON.stringify(payload)
        });

        const finalData = await finalResponse.json();

        if (finalResponse.ok && !finalData.errors) {
          console.log('✅ Final retry test notification sent successfully:', finalData);
          alert('Final retry test notification sent successfully! Check your device.');
        } else {
          console.error('❌ All notification attempts failed:', finalData);
          alert(`All notification attempts failed: ${JSON.stringify(finalData)}`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error sending test notification:', error);
    alert(`Error sending test notification: ${error}`);
  }
};
