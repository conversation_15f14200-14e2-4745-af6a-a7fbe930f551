import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Product, ContactInfo, RentalPeriodInfo, Coupon, DeliveryOption, SystemSettings, Booking, Payment } from '../types';
import { format, parseISO, isSameDay, eachDayOfInterval, isValid, addDays } from 'date-fns';

// Company info - in a real app this would come from environment variables or settings
const COMPANY_INFO = {
  name: 'Team Work Publicity Co. W.L.L',
  addressLine1: 'Office 33, Building 114, Road 383, Block 316',
  addressLine2: 'Manama, Kingdom of Bahrain',
  email: '<EMAIL>',
  website: 'www.teamwork.bh',
  phone: '+973 39 963 468                  CR: 1830220-1',
  logo: 'LOGO' // Just text placeholder for the logo
};

// Format currency with 3 decimal places
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'BHD',
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(amount);
};

// Format date
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format payment date (shorter format for table)
const formatPaymentDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Format time
const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Calculate item total based on rental type and custom days
const calculateItemTotal = (product: Product, rentalPeriod: RentalPeriodInfo) => {
  const productDays = product.customDays || rentalPeriod.days;
  const dailyRate = product.temporaryDailyRate || product.dailyRate;
  const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;

  if (rentalPeriod.rentalType === 'weekly' && weeklyRate) {
    const weeks = Math.ceil(productDays / 7);
    return weeklyRate * weeks * product.quantity;
  } else {
    return dailyRate * productDays * product.quantity;
  }
};

// Format a collection of dates
const formatRentalDates = (dates: string[]) => {
  if (!dates || dates.length === 0) return 'No dates selected';

  const validDates = dates
    .map(date => parseISO(date))
    .filter(date => isValid(date))
    .sort((a, b) => a.getTime() - b.getTime());

  if (validDates.length === 0) return 'No valid dates';

  // Remove duplicates
  const uniqueDates = validDates.filter((date, index, self) =>
    index === self.findIndex(d => format(d, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd'))
  );

  // Group dates by month and year
  const datesByMonth: Record<string, number[]> = {};

  uniqueDates.forEach(date => {
    const monthYearKey = format(date, 'MMMM yyyy');
    if (!datesByMonth[monthYearKey]) {
      datesByMonth[monthYearKey] = [];
    }
    // Store just the day number
    datesByMonth[monthYearKey].push(parseInt(format(date, 'd')));
  });

  // Format dates by month
  const formattedDatesByMonth = Object.entries(datesByMonth).map(([monthYear, days]) => {
    // Sort days numerically
    days.sort((a, b) => a - b);
    return `${days.join(', ')} ${monthYear}`;
  });

  // Join with new lines between months
  return formattedDatesByMonth.join('\n');
};

interface GeneratePDFOptions {
  quoteNumber: string;
  selectedProducts: Product[];
  contactInfo: ContactInfo;
  rentalPeriod: RentalPeriodInfo;
  appliedCoupon: Coupon | null;
  deliveryOption: DeliveryOption | null;
  systemSettings: SystemSettings;
  subtotal: number;
  deliveryFee: number;
  discount: number;
  tax: number;
  total: number;
  payments?: Payment[];
  paidAmount?: number;
  remainingAmount?: number;
  formatCurrencyTotal: (amount: number) => string;
  booking?: Booking;
}

export const generatePDF = async (
  options: GeneratePDFOptions | (Booking & { settings: SystemSettings }),
  saveFile: boolean = true
): Promise<string | void> => {
  try {
    // Handle both parameter structures
    let booking: Booking;
    let systemSettings: SystemSettings;

    if ('customer' in options) {
      // If options is a Booking object
      booking = options as Booking;
      systemSettings = (options as any).settings;
    } else {
      // If options is GeneratePDFOptions
      const opts = options as GeneratePDFOptions;
      booking = {
        id: '',
        quoteNumber: opts.quoteNumber,
        // Use the original booking date if available, otherwise use current date
        date: opts.booking?.date || new Date().toISOString(),
        customer: opts.contactInfo,
        products: opts.selectedProducts,
        rentalPeriod: opts.rentalPeriod,
        coupon: opts.appliedCoupon,
        delivery: opts.deliveryOption ? {
          option: opts.deliveryOption,
          fee: opts.deliveryFee
        } : undefined,
        status: opts.booking?.status || 'pending',
        subtotal: opts.subtotal,
        deliveryFee: opts.deliveryFee,
        discount: opts.discount,
        tax: opts.tax,
        total: opts.total,
        payments: opts.payments || [],
        paidAmount: opts.paidAmount,
        remainingAmount: opts.remainingAmount
      };
      systemSettings = opts.systemSettings;
    }

    // Initialize jsPDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Define page dimensions and margins
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const leftMargin = 20;
    const rightMargin = pageWidth - 20;
    let currentY = 20;

    // HEADER SECTION
    // Company info with smaller font (9 points)
    doc.setFontSize(9); // Decreased by 2 points from 11
    doc.setTextColor(80, 80, 80);
    doc.setFont('helvetica', 'bold');
    doc.text(COMPANY_INFO.name, leftMargin, currentY);
    doc.setFont('helvetica', 'normal');
    currentY += 5;
    doc.text(COMPANY_INFO.addressLine1, leftMargin, currentY);
    currentY += 5;
    doc.text(COMPANY_INFO.addressLine2, leftMargin, currentY);
    currentY += 5;
    doc.text(`${COMPANY_INFO.email} | ${COMPANY_INFO.website}`, leftMargin, currentY);
    currentY += 5;
    // Phone number
    doc.text(COMPANY_INFO.phone, leftMargin, currentY);

    // Add logo from external URL
    try {
      // Use the external image URL for the logo
      const logoUrl = 'https://i.ibb.co/20KtQDCs/output-onlinepngtools.png';
      // Reduce size by 20%
      const logoWidth = 32; // 40 * 0.8 = 32
      const logoHeight = 16; // 20 * 0.8 = 16

      // Add the image to the PDF - moved one line up
      doc.addImage(logoUrl, 'PNG', rightMargin - logoWidth, currentY - 20, logoWidth, logoHeight);
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
      // Fallback to text if image fails
      doc.setFontSize(24);
      doc.setTextColor(37, 99, 235); // Blue color for logo
      doc.setFont('helvetica', 'bold');
      doc.text('tw.', rightMargin, currentY - 20, { align: 'right' });
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(80, 80, 80); // Reset to dark gray
    }

    currentY += 20;

    // CLIENT SECTION
    doc.setFontSize(11);
    doc.setFont('helvetica', 'bold');
    doc.text('CLIENT:', leftMargin, currentY);
    doc.setFont('helvetica', 'normal');
    currentY += 5;
    doc.text(booking.customer.name, leftMargin, currentY);
    currentY += 5;
    doc.text(booking.customer.email || '<EMAIL>', leftMargin, currentY);
    currentY += 5;
    doc.text(booking.customer.phone || '+973 1234 5678', leftMargin, currentY);

    // QUOTE DETAILS SECTION
    const quoteDetailsX = pageWidth - 100;
    let quoteDetailsY = currentY - 15;

    doc.setFont('helvetica', 'bold');
    // Change text from "QUOTE #" to "INVOICE #" when booking status is "completed" or "confirmed"
    const documentType = (booking.status === 'completed' || booking.status === 'confirmed') ? 'INVOICE #' : 'QUOTE #';
    doc.text(documentType, quoteDetailsX, quoteDetailsY);
    doc.setFont('helvetica', 'normal');
    doc.text(booking.quoteNumber, rightMargin, quoteDetailsY, { align: 'right' });
    quoteDetailsY += 5;

    doc.setFont('helvetica', 'bold');
    doc.text('DATE:', quoteDetailsX, quoteDetailsY);
    doc.setFont('helvetica', 'normal');
    doc.text(format(new Date(booking.date), 'dd MMMM yyyy'), rightMargin, quoteDetailsY, { align: 'right' });
    quoteDetailsY += 5;

    doc.setFont('helvetica', 'bold');
    // Add the booking dates label in bold
    doc.text('BOOKING DATES: ', quoteDetailsX, quoteDetailsY);
    // Add the number of days in normal font
    doc.setFont('helvetica', 'normal');
    doc.text(`(${booking.rentalPeriod.days} ${booking.rentalPeriod.days === 1 ? 'day' : 'days'})`, quoteDetailsX + doc.getTextWidth('BOOKING DATES:  '), quoteDetailsY);

    // Move to next line for booking dates
    quoteDetailsY += 5;

    // Format booking dates
    const bookingDateStr = booking.rentalPeriod.dates && booking.rentalPeriod.dates.length > 0
      ? formatRentalDates(booking.rentalPeriod.dates)
      : 'N/A';

    // Handle long booking date strings with line wrapping
    const maxWidth = rightMargin - quoteDetailsX - 5;
    // Split into multiple lines if needed
    const dateLines = doc.splitTextToSize(bookingDateStr, maxWidth);
    // Left align the booking dates
    doc.text(dateLines, quoteDetailsX, quoteDetailsY);

    // Adjust the Y position based on number of lines
    if (dateLines.length > 1) {
      quoteDetailsY += (dateLines.length - 1) * 5;
    }
    quoteDetailsY += 5; // Add some space after the dates

    currentY += 20;

    // PRODUCTS TABLE HEADER
    currentY += 10;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');

    // Define column positions
    const col1 = leftMargin;           // ITEM (no header)
    const col2 = pageWidth - 100;      // DAYS (no header) - moved further right
    const col3 = pageWidth - 80;       // PRICE (was DAILY RATE)
    const col4 = rightMargin;          // TOTAL

    // Show necessary headers
    doc.text('PRICE', col3, currentY);
    doc.text('TOTAL', col4, currentY, { align: 'right' });
    currentY += 5;

    // Add horizontal line below headers
    doc.setDrawColor(200, 200, 200);
    doc.line(leftMargin, currentY, rightMargin, currentY);
    currentY += 10;

    // PRODUCTS TABLE ROWS
    doc.setFont('helvetica', 'normal');

    // Define bottom margin to prevent content from being cut off
    const bottomMargin = pageHeight - 40; // 40mm from bottom of page

    booking.products.forEach(product => {
      // Check if we need to add a new page before adding this product
      if (currentY > bottomMargin) {
        doc.addPage();
        currentY = 40; // Reset Y position with some margin from top

        // Redraw column headers on new page
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.text('PRICE', col3, currentY);
        doc.text('TOTAL', col4, currentY, { align: 'right' });
        currentY += 5;

        // Add horizontal line below headers
        doc.setDrawColor(200, 200, 200);
        doc.line(leftMargin, currentY, rightMargin, currentY);
        currentY += 10;
      }

      const productDays = product.customDays || booking.rentalPeriod.days;
      const itemName = `${product.quantity}x    ${product.name}`;
      const total = calculateItemTotal(product, booking.rentalPeriod);

      // Item name (bold and larger font)
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(11); // Increase font size by 1 point

      // Handle long product names with line wrapping
      const maxWidth = col2 - col1 - 5;
      const nameLines = doc.splitTextToSize(itemName, maxWidth);
      doc.text(nameLines, col1, currentY);

      // Adjust Y position if product name wraps to multiple lines
      const nameHeight = (nameLines.length - 1) * 5;

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10); // Reset font size back to default

      // Daily rate with discount percentage if applicable
      let priceText = `BHD ${product.dailyRate.toFixed(3)}`;

      // Check if there's a discount
      if (product.temporaryDailyRate && product.temporaryDailyRate < product.dailyRate) {
        // Calculate discount percentage
        const discountPercentage = Math.round((1 - product.temporaryDailyRate / product.dailyRate) * 100);
        priceText += `   (-${discountPercentage}%)`;
      } else if (booking.coupon?.discountType === 'percentage') {
        priceText += `   (-${booking.coupon.discountValue}%)`;
      }

      doc.text(priceText, col3, currentY);

      // Days with singular/plural
      doc.text(`${productDays} ${productDays === 1 ? 'day' : 'days'}`, col2, currentY);

      // Add total (right aligned)
      doc.text(`BHD ${total.toFixed(3)}`, col4, currentY, { align: 'right' });

      // Adjust Y position for next item, accounting for wrapped text
      currentY += 10 + nameHeight;
    });

    // Add horizontal line after products
    doc.setDrawColor(200, 200, 200);
    doc.line(leftMargin, currentY, rightMargin, currentY);
    currentY += 5;

    // Check if we need to add a new page before the summary section
    if (currentY > bottomMargin - 50) { // Need more space for summary
      doc.addPage();
      currentY = 40; // Reset Y position with some margin from top
    }

    // SUMMARY SECTION
    // Position subtotal group
    const summaryLabelX = pageWidth - 90; // Move to the left
    const summaryValueX = col4;

    // Subtotal
    doc.setFont('helvetica', 'bold');
    doc.text('SUBTOTAL', summaryLabelX, currentY);
    doc.text(`BHD ${booking.subtotal.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
    currentY += 6;

    // Discount (if applicable)
    if (booking.discount > 0) {
      doc.setFont('helvetica', 'normal');
      doc.text('Discount', summaryLabelX, currentY);
      doc.text(`-BHD ${booking.discount.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
      currentY += 6;
    }

    // Total excluding taxes
    doc.setFont('helvetica', 'normal');
    doc.text('Total excl. taxes', summaryLabelX, currentY);
    const totalExclTaxes = booking.subtotal - booking.discount;
    doc.text(`BHD ${totalExclTaxes.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
    currentY += 6;

    // VAT
    doc.text(`VAT (${systemSettings.taxRate || 0}%)`, summaryLabelX, currentY);
    doc.text(`${booking.tax > 0 ? 'BHD ' + booking.tax.toFixed(3) : '0'}`, summaryValueX, currentY, { align: 'right' });
    currentY += 6;

    // Add horizontal line before total
    doc.setDrawColor(200, 200, 200);
    doc.line(summaryLabelX, currentY, rightMargin, currentY);
    currentY += 6;

    // Reset font for total
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(11);
    doc.setTextColor(80, 80, 80);
    doc.text('TOTAL', summaryLabelX, currentY);
    doc.text(`BHD ${booking.total.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
    currentY += 20;

    // PAYMENT HISTORY SECTION (if payments exist)
    if (booking.payments && booking.payments.length > 0) {
      // Check if we need to add a new page before payment history
      if (currentY > bottomMargin - 40) {
        doc.addPage();
        currentY = 40; // Reset Y position with some margin from top
      }

      // Payment History heading
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text('PAYMENT HISTORY', leftMargin, currentY);
      currentY += 5;

      // Add horizontal line
      doc.setDrawColor(200, 200, 200);
      doc.line(leftMargin, currentY, rightMargin, currentY);
      currentY += 5;

      // Payment table headers
      doc.setFontSize(10);

      // Define payment column positions
      const paymentDateCol = leftMargin;
      const methodCol = pageWidth / 2;
      const amountCol = rightMargin;

      doc.text('Payment Date', paymentDateCol, currentY);
      doc.text('Method', methodCol, currentY);
      doc.text('Amount', amountCol, currentY, { align: 'right' });
      currentY += 5;

      // Skip the line below headers
      currentY += 5;

      // Payment rows
      doc.setFont('helvetica', 'normal');
      booking.payments.forEach(payment => {
        // Check if we need to add a new page before this payment
        if (currentY > bottomMargin) {
          doc.addPage();
          currentY = 40; // Reset Y position with some margin from top

          // Redraw payment table headers on new page
          doc.setFontSize(10);
          doc.setFont('helvetica', 'bold');
          doc.text('Payment Date', paymentDateCol, currentY);
          doc.text('Method', methodCol, currentY);
          doc.text('Amount', amountCol, currentY, { align: 'right' });
          currentY += 10;

          doc.setFont('helvetica', 'normal');
        }

        const paymentDate = formatPaymentDate(payment.date);
        const paymentMethod = payment.method.replace('_', ' ').toUpperCase();

        doc.text(paymentDate, paymentDateCol, currentY);
        doc.text(paymentMethod, methodCol, currentY);
        doc.text(`BHD ${payment.amount.toFixed(3)}`, amountCol, currentY, { align: 'right' });

        currentY += 6;
      });

      // Add horizontal line after payments
      doc.setDrawColor(200, 200, 200);
      doc.line(leftMargin, currentY, rightMargin, currentY);
      currentY += 5;

      // Check if we need to add a new page before payment summary
      if (currentY > bottomMargin - 20) {
        doc.addPage();
        currentY = 40; // Reset Y position with some margin from top
      }

      // Calculate payment totals
      const paidAmount = booking.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const remainingAmount = booking.total - paidAmount;

      // Payment received
      doc.text('Payment Received', summaryLabelX, currentY);
      doc.text(`BHD ${paidAmount.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
      currentY += 6;

      // Remaining balance
      doc.setFont('helvetica', 'bold');
      doc.text('Remaining Balance', summaryLabelX, currentY);
      doc.text(`BHD ${remainingAmount.toFixed(3)}`, summaryValueX, currentY, { align: 'right' });
    }

    // Add Terms & Conditions section
    currentY += 20;

    // Check if we need to add a new page for terms
    if (currentY > bottomMargin - 60) { // Need more space for T&C heading and initial content
      doc.addPage();
      currentY = 40;
    }

    // Terms & Conditions heading
    doc.setFontSize(11);
    doc.setTextColor(80, 80, 80);
    doc.setFont('helvetica', 'bold');
    doc.text('TERMS & CONDITIONS', leftMargin, currentY);
    currentY += 10;

    // Terms & Conditions content
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');

    const termsText = [
      "OWNERSHIP: Lessor is sole owner of all leased equipment and accessories.",
      "PAYMENT: Final invoice issued upon equipment return; payment due immediately.",
      "LEASE PERIOD: Begins when Lessee signs pullout form. Ends 12:00 p.m. on final day. Late returns incur daily fees.",
      "LESSEE RESPONSIBILITIES:",
      "Report damage immediately via email",
      "No unauthorized repairs",
      "Pay all repair/replacement costs",
      "Cover 50% of lease value during repair period",
      "Late payment (>2 days) incurs fees equal to value of delayed days"
    ];

    // Add terms text with line spacing
    termsText.forEach((line, index) => {
      // Check if we need to add a new page
      if (currentY > bottomMargin) {
        doc.addPage();
        currentY = 40;
      }

      // Check if line contains all caps words
      const words = line.split(' ');
      let currentX = leftMargin;

      words.forEach(word => {
        // Check if word is all caps (and at least 2 characters)
        if (word.length >= 2 && word === word.toUpperCase() && /[A-Z]/.test(word)) {
          doc.setFont('helvetica', 'bold');
        } else {
          doc.setFont('helvetica', 'normal');
        }

        // Add the word
        const wordWidth = doc.getTextWidth(word + ' ');

        // Check if we need to wrap to next line
        if (currentX + wordWidth > rightMargin) {
          currentY += 5;
          currentX = leftMargin;

          // Check if we need a new page after wrapping
          if (currentY > bottomMargin) {
            doc.addPage();
            currentY = 40;
          }
        }

        doc.text(word + ' ', currentX, currentY);
        currentX += wordWidth;
      });

      // Move to next line
      currentY += 5;

      // Check if we need a new page before the next line
      if (currentY > bottomMargin && index < termsText.length - 1) {
        doc.addPage();
        currentY = 40;
      }

      // Reset font
      doc.setFont('helvetica', 'normal');
    });

    // Add bank details after terms and conditions
    currentY += 10; // Add some space before bank details

    // Check if we need to add a new page for bank details
    if (currentY > bottomMargin - 30) {
      doc.addPage();
      currentY = 40;
    }

    // Bank details heading
    doc.setFontSize(8);
    doc.setTextColor(80, 80, 80);
    doc.setFont('helvetica', 'bold');
    doc.text('BANK DETAILS', leftMargin, currentY);
    currentY += 5;

    // Bank details content with same font as terms and conditions
    doc.setFont('helvetica', 'normal');

    const bankDetails = [
      'Bank: National Bank of Bahrain (NBB)',
      'Account Name: Team Work Publicity Co. W.L.L',
      'Account Number: 0384-123456-001',
      'IBAN: BH12 NBOB 0384 1234 5600 01',
      'Swift Code: NBOBBHBM',
      'Branch: Manama, Kingdom of Bahrain'
    ];

    // Add bank details with the same line spacing as terms
    bankDetails.forEach(line => {
      // Check if we need to add a new page
      if (currentY > bottomMargin) {
        doc.addPage();
        currentY = 40;
      }

      doc.text(line, leftMargin, currentY);
      currentY += 5; // Same line spacing as terms
    });

    // Reset font
    doc.setFont('helvetica', 'normal');

    // Add "PAID" watermark if payment is complete
    const paidAmount = booking.payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0;
    const isFullyPaid = paidAmount >= booking.total;

    if (isFullyPaid) {
      // Add watermark to each page
      const totalPages = (doc as any).internal.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);

        // Set watermark properties
        doc.setTextColor(0, 128, 0); // Green color
        doc.setGState(new (doc as any).GState({ opacity: 0.3 })); // 20% opacity
        doc.setFontSize(80);
        doc.setFont('helvetica', 'bold');

        // Create a diagonal watermark
        // Position in the center of the page
        const centerX = pageWidth / 2;
        const centerY = pageHeight / 1.5;

        // Apply rotation for diagonal text
        doc.text('PAID', centerX, centerY, {
          align: 'center',
          angle: 25,
          renderingMode: 'stroke'
        });
      }
    }

    // Add page numbers
    const totalPages = (doc as any).internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setTextColor(150, 150, 150);
      doc.text(
        `Page ${i} of ${totalPages}`,
        pageWidth - 25,
        pageHeight - 10,
        { align: 'right' }
      );
    }

    // If saveFile is true, save the PDF directly
    if (saveFile) {
      doc.save(`TW_${booking.quoteNumber}.pdf`);
      return;
    }

    // Otherwise, return the PDF as a data URL for sharing
    return doc.output('datauristring');
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};