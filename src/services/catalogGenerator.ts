import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Product } from '../types';

interface TableRow {
  name: string;
  sku: string;
  dailyRate: string;
  weeklyRate: string;
  quantity: string;
}

// Company info
const COMPANY_INFO = {
  name: 'Equipment Rental',
  address: '123 Main Street',
  city: 'Anytown',
  state: 'ST',
  zip: '12345',
  country: 'United States',
  phone: '(*************',
  email: '<EMAIL>'
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'BHD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Format currency without decimals (specifically for PDF catalog)
const formatCurrencyNoDec = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'BHD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const generateCatalog = async (products: Product[], selectedCategory?: string) => {
  try {
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Add header
    doc.setFontSize(24);
    doc.setTextColor(0, 0, 255);
    doc.text('Equipment Rental Catalog', 105, 20, { align: 'center' });

    // Add company info
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text(COMPANY_INFO.address, 20, 30);
    doc.text(`${COMPANY_INFO.city}, ${COMPANY_INFO.state} ${COMPANY_INFO.zip}`, 20, 35);
    doc.text(COMPANY_INFO.phone, 20, 40);
    doc.text(COMPANY_INFO.email, 20, 45);

    // Add date
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 55);

    let yPos = 65;

    // Filter products by category if specified
    const filteredProducts = selectedCategory 
      ? products.filter(product => product.category === selectedCategory)
      : products;

    // Group products by category
    const groupedProducts = filteredProducts.reduce((acc, product) => {
      if (!acc[product.category]) {
        acc[product.category] = [];
      }
      acc[product.category].push(product);
      return acc;
    }, {} as Record<string, Product[]>);

    // Sort categories alphabetically
    const sortedCategories = Object.keys(groupedProducts).sort();

    // Define table columns
    const columns = [
      { header: 'Name', dataKey: 'name' },
      { header: 'SKU', dataKey: 'sku' },
      { header: 'Daily Rate', dataKey: 'dailyRate' },
      { header: 'Weekly Rate', dataKey: 'weeklyRate' },
      { header: 'Qty', dataKey: 'quantity' }
    ];

    // Draw products for each category
    for (const category of sortedCategories) {
      // Add category header
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 255);
      doc.text(category.charAt(0).toUpperCase() + category.slice(1), 20, yPos);
      yPos += 10;

      const categoryProducts = groupedProducts[category];
      
      // Prepare data for the table
      const tableData = categoryProducts.map(product => {
        // Use stock for the quantity column, ensure it's a number
        const stock = typeof product.stock === 'number' ? product.stock : 0;
        
        return {
          name: product.name,
          sku: product.sku || 'n/a',
          dailyRate: formatCurrencyNoDec(product.dailyRate),
          weeklyRate: formatCurrencyNoDec(product.weeklyRate || product.dailyRate * 5),
          quantity: stock.toString()
        };
      });

      // Draw table using jspdf-autotable
      autoTable(doc, {
        startY: yPos,
        head: [columns.map(col => col.header)],
        body: tableData.map(row => columns.map(col => row[col.dataKey as keyof TableRow])),
        theme: 'grid',
        headStyles: {
          fillColor: [37, 99, 235],
          textColor: 255,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 10,
          cellPadding: 2,
          overflow: 'linebreak'
        },
        columnStyles: {
          0: { cellWidth: 70 }, // Name (reduced from 80)
          1: { cellWidth: 25 }, // SKU (reduced from 30)
          2: { cellWidth: 25 }, // Daily Rate (reduced from 30)
          3: { cellWidth: 25 }, // Weekly Rate (reduced from 30)
          4: { cellWidth: 15 }  // Quantity (reduced from 20)
        },
        margin: { left: 15, right: 15 }, // Reduced margins
        didDrawPage: (data: any) => {
          yPos = data.cursor.y + 10;
        }
      });
    }

    // Save the PDF
    doc.save(selectedCategory ? 
      `equipment_catalog_${selectedCategory}.pdf` : 
      'equipment_catalog_full.pdf'
    );
  } catch (error) {
    console.error('Error generating catalog:', error);
    throw error;
  }
}; 