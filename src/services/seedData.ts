import { addUser, getUsers } from './database';
import { User } from '../types';

export const seedDefaultAdmin = async (): Promise<User | null> => {
  try {
    // Check if any users exist
    const existingUsers = await getUsers();

    if (existingUsers.length > 0) {
      console.log('Users already exist, skipping default admin creation');
      return existingUsers.find(user => user.role === 'admin') || existingUsers[0];
    }

    // Create default admin user
    const defaultAdmin = {
      name: 'System Administrator',
      email: '<EMAIL>',
      username: 'admin',
      role: 'admin' as const,
      createdAt: new Date().toISOString(),
      isActive: true
    };

    const adminId = await addUser(defaultAdmin);
    const createdAdmin: User = {
      id: adminId,
      name: defaultAdmin.name,
      email: defaultAdmin.email,
      username: defaultAdmin.username,
      role: defaultAdmin.role,
      createdAt: defaultAdmin.createdAt,
      isActive: defaultAdmin.isActive
    };

    console.log('Default admin user created successfully');
    return createdAdmin;
  } catch (error) {
    console.error('Error seeding default admin user:', error);
    return null;
  }
};