// OneSignal Service for Push Notifications
import { User } from '../types';
import { ONESIGNAL_APP_ID, ONESIGNAL_REST_API_KEY } from '../config/oneSignalConfig';

/**
 * Get the current OneSignal player ID (device ID)
 */
export const getOneSignalPlayerId = async (): Promise<string | null> => {
  if (!window.OneSignal) {
    console.error('OneSignal not initialized');
    return null;
  }

  try {
    // Use the OneSignal API to get the user ID
    let userId = null;

    // Try different methods to get the user ID based on the SDK version
    await window.OneSignal.push(async function() {
      try {
        // Method 1: Try to get the user ID from getUserId (older versions)
        userId = await window.OneSignal.getUserId();
        console.log('Got OneSignal User ID using getUserId():', userId);
      } catch (error1) {
        console.warn('Error using getUserId():', error1);

        try {
          // Method 2: Try to get the user ID from getSubscription (newer versions)
          const subscription = await window.OneSignal.getSubscription();
          console.log('OneSignal subscription state:', subscription);

          // If subscribed, try to get the ID
          if (subscription) {
            try {
              // Try to get the ID from internal methods
              const internalData = await window.OneSignal.getRegistrationId();
              userId = internalData;
              console.log('Got OneSignal User ID using getRegistrationId():', userId);
            } catch (error3) {
              console.warn('Error using getRegistrationId():', error3);
            }
          }
        } catch (error2) {
          console.warn('Error using getSubscription():', error2);
        }
      }
    });

    return userId;
  } catch (error) {
    console.error('Error getting OneSignal player ID:', error);
    return null;
  }
};

/**
 * Send a notification to all subscribed users using OneSignal REST API
 */
export const sendNotificationToAll = async (
  title: string,
  message: string,
  url: string = window.location.origin,
  data: any = {}
): Promise<void> => {
  try {
    console.log('Preparing to send notification:', { title, message });

    // Try to get the current device ID
    const playerId = await getOneSignalPlayerId();
    console.log('Current OneSignal Player ID:', playerId);

    // Create the notification payload
    const payload: any = {
      app_id: ONESIGNAL_APP_ID,
      headings: { en: title },
      contents: { en: message },
      url: url,
      data: data,
      chrome_web_icon: `${window.location.origin}/logo192.png`,  // Use absolute URL
      firefox_icon: `${window.location.origin}/logo192.png`,     // Use absolute URL
    };

    // Always include the 'All' segment to ensure all subscribers get the notification
    payload.included_segments = ['All'];

    // If we have a player ID, also target that specific device to ensure the current user gets it
    if (playerId) {
      payload.include_player_ids = [playerId];
    }

    console.log('OneSignal notification payload:', payload);
    console.log('Using REST API Key:', ONESIGNAL_REST_API_KEY.substring(0, 10) + '...');

    // Send the notification using the REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    const responseData = await response.json();

    if (response.ok) {
      console.log('✅ Notification sent successfully:', responseData);
      return;
    } else {
      console.error('❌ Failed to send notification:', responseData);
      console.error('Response status:', response.status);

      // Try with different targeting options
      console.log('First attempt failed, retrying with different targeting options...');
      console.log('Error details:', responseData.errors);

      // Try with just the 'Subscribed Users' segment
      payload.included_segments = ['Subscribed Users'];
      if (payload.include_player_ids) {
        delete payload.include_player_ids;
      }

      console.log('Retrying with segment "Subscribed Users"...');

      const retryResponse = await fetch('https://onesignal.com/api/v1/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
        },
        body: JSON.stringify(payload)
      });

      const retryData = await retryResponse.json();

      if (retryResponse.ok && !retryData.errors) {
        console.log('✅ Retry notification sent successfully:', retryData);
        return;
      } else {
        console.error('❌ Retry also failed:', retryData);

        // Try one more time with 'All' segment only
        payload.included_segments = ['All'];
        console.log('Final retry with segment "All" only...');

        const finalResponse = await fetch('https://onesignal.com/api/v1/notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
          },
          body: JSON.stringify(payload)
        });

        const finalData = await finalResponse.json();

        if (finalResponse.ok && !finalData.errors) {
          console.log('✅ Final retry notification sent successfully:', finalData);
          return;
        } else {
          console.error('❌ All notification attempts failed:', finalData);
        }
      }

      // Fallback to browser notification
      showBrowserNotification(title, message);
    }
  } catch (error) {
    console.error('❌ Error sending notification:', error);
    // Fallback to browser notification
    showBrowserNotification(title, message);
  }
};

/**
 * Show a browser notification as a fallback
 */
const showBrowserNotification = (title: string, message: string): void => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(title, {
      body: message,
      icon: '/logo192.png'
    });
  }
};

/**
 * Send a direct notification to the current user (admin)
 * This is a special method for admin users who create bookings
 */
export const sendAdminDirectNotification = async (
  title: string,
  message: string,
  data: any = {}
): Promise<void> => {
  try {
    console.log('Sending direct admin notification:', { title, message });

    // First try browser notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/logo192.png',
        data: data
      });
      console.log('Browser notification shown to admin');
    }

    // Then try OneSignal direct methods
    if (window.OneSignal) {
      // Get the player ID
      const playerId = await getOneSignalPlayerId();
      console.log('Admin OneSignal Player ID:', playerId);

      if (playerId) {
        // Create a special payload just for this admin
        const payload = {
          app_id: ONESIGNAL_APP_ID,
          include_player_ids: [playerId],
          headings: { en: title },
          contents: { en: message },
          url: window.location.origin,
          data: data,
          chrome_web_icon: `${window.location.origin}/logo192.png`,
          firefox_icon: `${window.location.origin}/logo192.png`,
        };

        // Send the notification directly to this admin
        const response = await fetch('https://onesignal.com/api/v1/notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${ONESIGNAL_REST_API_KEY}`
          },
          body: JSON.stringify(payload)
        });

        const responseData = await response.json();

        if (response.ok && !responseData.errors) {
          console.log('✅ Admin direct notification sent successfully:', responseData);
        } else {
          console.warn('⚠️ Admin direct notification had issues:', responseData);
        }
      }
    }
  } catch (error) {
    console.error('Error sending admin direct notification:', error);
  }
};

/**
 * Send notification when a new booking is created
 */
export const sendNewBookingNotification = async (bookingDetails: any): Promise<void> => {
  try {
    console.log('Received booking details for notification:', bookingDetails);

    // Extract booking details safely
    let customerName = 'a customer';
    let quoteNumber = bookingDetails.quoteNumber || bookingDetails.id || 'Unknown';
    let totalItems = 0;
    let totalPrice = 0;
    let bookingDays = 0;

    try {
      // Extract customer name
      if (bookingDetails && bookingDetails.customer && bookingDetails.customer.name) {
        customerName = bookingDetails.customer.name;
      } else if (bookingDetails && bookingDetails.customer && bookingDetails.customer.contactInfo && bookingDetails.customer.contactInfo.name) {
        customerName = bookingDetails.customer.contactInfo.name;
      }

      // Extract total items
      if (bookingDetails && bookingDetails.products && Array.isArray(bookingDetails.products)) {
        totalItems = bookingDetails.products.length;
      }

      // Extract total price
      if (bookingDetails && typeof bookingDetails.totalPrice === 'number') {
        totalPrice = bookingDetails.totalPrice;
      } else if (bookingDetails && bookingDetails.total && typeof bookingDetails.total === 'number') {
        totalPrice = bookingDetails.total;
      }

      // Extract booking days
      if (bookingDetails && bookingDetails.rentalPeriod) {
        if (typeof bookingDetails.rentalPeriod.days === 'number') {
          bookingDays = bookingDetails.rentalPeriod.days;
        } else if (bookingDetails.rentalPeriod.dates && Array.isArray(bookingDetails.rentalPeriod.dates)) {
          bookingDays = bookingDetails.rentalPeriod.dates.length;
        }
      }
    } catch (error) {
      console.warn('Could not extract all booking details:', error);
    }

    // Format the price with 3 decimal places and currency symbol
    // Make sure totalPrice is a valid number to avoid NaN
    const validTotalPrice = isNaN(totalPrice) ? 0 : totalPrice;
    const formattedPrice = validTotalPrice.toFixed(3);

    // Create notification with the requested format
    const title = `New Booking - ${bookingDays} day${bookingDays !== 1 ? 's' : ''}`;
    const subject = `Quote: ${quoteNumber}`;
    const details = `${customerName}, booked: ${totalItems} item${totalItems !== 1 ? 's' : ''}, total: BD ${formattedPrice}`;
    const message = `${subject}\n${details}`;

    console.log(`Sending notification: "${title} - ${message}"`);

    // First, send a direct notification to the admin user who created the booking
    await sendAdminDirectNotification(
      title,
      message,
      {
        bookingId: bookingDetails.id || 'unknown',
        quoteNumber: quoteNumber,
        customerName: customerName,
        totalItems: totalItems,
        totalPrice: totalPrice,
        type: 'new_booking',
        timestamp: new Date().toISOString(),
        admin: true
      }
    );

    // Then send the OneSignal notification to all subscribers
    await sendNotificationToAll(
      title,
      message,
      window.location.origin,
      {
        bookingId: bookingDetails.id || 'unknown',
        quoteNumber: quoteNumber,
        customerName: customerName,
        totalItems: totalItems,
        totalPrice: totalPrice,
        type: 'new_booking',
        timestamp: new Date().toISOString()
      }
    );

    // Also try a direct notification using OneSignal's client API
    if (window.OneSignal) {
      try {
        // Try to show a notification using the browser's Notification API
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(title, {
            body: message,
            icon: '/logo192.png',
            data: {
              bookingId: bookingDetails.id || 'unknown',
              quoteNumber: quoteNumber,
              customerName: customerName,
              totalItems: totalItems,
              totalPrice: totalPrice,
              type: 'new_booking',
              timestamp: new Date().toISOString()
            }
          });
        }

        // Also try to use OneSignal's API to show a notification
        window.OneSignal.push(function() {
          try {
            // For newer versions of OneSignal - try different methods
            if (typeof window.OneSignal.showNativeNotification === 'function') {
              window.OneSignal.showNativeNotification({
                title: title,
                message: message,
                url: window.location.origin,
                icon: '/logo192.png',
                data: {
                  bookingId: bookingDetails.id || 'unknown',
                  type: 'new_booking',
                  timestamp: new Date().toISOString()
                }
              });
              console.log('Sent notification using showNativeNotification');
            } else if (typeof window.OneSignal.sendSelfNotification === 'function') {
              // Try the older method
              window.OneSignal.sendSelfNotification(
                title,
                message,
                window.location.origin,
                '/logo192.png',
                {
                  bookingId: bookingDetails.id || 'unknown',
                  type: 'new_booking',
                  timestamp: new Date().toISOString()
                }
              );
              console.log('Sent notification using sendSelfNotification');
            } else {
              // Last resort - try to use the notification API directly
              console.log('No direct notification method available in OneSignal');
            }
          } catch (error) {
            console.warn('Error using OneSignal notification methods:', error);
          }
        });
      } catch (clientError) {
        console.warn('Error showing client-side notification:', clientError);
      }
    }

  } catch (error) {
    console.error('Error sending booking notification:', error);
    // Fallback to browser notification
    try {
      showBrowserNotification('New Booking', 'A new booking has been created');
    } catch (notificationError) {
      console.error('Even browser notification failed:', notificationError);
    }
  }
};

/**
 * Set user information for OneSignal
 */
export const setOneSignalUser = (user: User): void => {
  if (!window.OneSignal) {
    console.error('OneSignal not initialized');
    return;
  }

  window.OneSignal.push(function() {
    // Set external user ID
    window.OneSignal.setExternalUserId(user.id);

    // Set user tags
    window.OneSignal.sendTags({
      userId: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });

    console.log('OneSignal user set:', user.id);
  });
};

/**
 * Request notification permission
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (!window.OneSignal) {
    console.error('OneSignal not initialized');
    return false;
  }

  try {
    // Show slidedown prompt
    window.OneSignal.push(function() {
      window.OneSignal.showSlidedownPrompt();
    });

    return true;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

// Add TypeScript definitions for OneSignal
declare global {
  interface Window {
    OneSignal: any;
  }
}
