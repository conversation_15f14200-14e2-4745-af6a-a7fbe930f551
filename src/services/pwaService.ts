/**
 * PWA Service - Handles service worker registration and updates
 */

/**
 * Register the service worker
 */
export const registerServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker registered with scope:', registration.scope);
      
      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        console.log('Service Worker update found!');
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            console.log('Service Worker state changed:', newWorker.state);
            
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, show update notification
              showUpdateNotification();
            }
          });
        }
      });
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  } else {
    console.log('Service Worker is not supported in this browser');
  }
};

/**
 * Show a notification when an update is available
 */
const showUpdateNotification = (): void => {
  const notification = document.createElement('div');
  notification.className = 'update-notification';
  notification.innerHTML = `
    <div class="update-notification-content">
      <p>A new version of the app is available!</p>
      <button id="update-button">Update Now</button>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Add styles
  const style = document.createElement('style');
  style.textContent = `
    .update-notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      padding: 16px;
      z-index: 9999;
    }
    .update-notification-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .update-notification p {
      margin: 0 16px 0 0;
    }
    #update-button {
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      cursor: pointer;
    }
  `;
  document.head.appendChild(style);
  
  // Add event listener to update button
  document.getElementById('update-button')?.addEventListener('click', () => {
    window.location.reload();
  });
};

/**
 * Check if the app is installed (in standalone mode)
 */
export const isAppInstalled = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any).standalone === true;
};

/**
 * Check if the app can be installed
 */
export const canInstallApp = async (): Promise<boolean> => {
  if (!window.BeforeInstallPromptEvent) {
    return false;
  }
  
  return !isAppInstalled();
};

// Store the install prompt event
let deferredPrompt: any;

/**
 * Set up the install prompt event
 */
export const setupInstallPrompt = (): void => {
  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent Chrome 67 and earlier from automatically showing the prompt
    e.preventDefault();
    // Stash the event so it can be triggered later
    deferredPrompt = e;
    
    console.log('App can be installed');
  });
  
  // When the app is installed
  window.addEventListener('appinstalled', () => {
    console.log('App was installed');
    deferredPrompt = null;
  });
};

/**
 * Show the install prompt
 */
export const showInstallPrompt = async (): Promise<boolean> => {
  if (!deferredPrompt) {
    console.log('Install prompt not available');
    return false;
  }
  
  // Show the install prompt
  deferredPrompt.prompt();
  
  // Wait for the user to respond to the prompt
  const choiceResult = await deferredPrompt.userChoice;
  
  // Reset the deferred prompt variable
  deferredPrompt = null;
  
  return choiceResult.outcome === 'accepted';
};

// Add TypeScript definitions
declare global {
  interface Window {
    BeforeInstallPromptEvent: any;
  }
}
