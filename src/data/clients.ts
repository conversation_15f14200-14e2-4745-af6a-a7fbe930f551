import { Client, ClientActivity, ClientStatistics } from '../types';

// Generate some sample clients
export const clients: Client[] = [];

// Generate client activities
export const clientActivities: ClientActivity[] = [
  {
    id: 1,
    clientId: 1,
    date: "2025-01-15T10:30:00Z",
    type: "booking",
    description: "Rented Excavator - Mini and Skid Steer Loader",
    amount: 650,
    bookingId: 1,
    quoteNumber: "Q-123456"
  },
  {
    id: 2,
    clientId: 1,
    date: "2025-01-15T10:35:00Z",
    type: "payment",
    description: "Initial payment for booking #1",
    amount: 325
  },
  {
    id: 3,
    clientId: 1,
    date: "2025-02-05T14:30:00Z",
    type: "payment",
    description: "Final payment for booking #1",
    amount: 325
  },
  {
    id: 4,
    clientId: 1,
    date: "2025-02-10T09:00:00Z",
    type: "booking",
    description: "Rented Bulldozer - D6",
    amount: 900,
    bookingId: 3,
    quoteNumber: "Q-345678"
  },
  {
    id: 5,
    clientId: 1,
    date: "2025-02-10T09:05:00Z",
    type: "payment",
    description: "Full payment for booking #3",
    amount: 900
  },
  {
    id: 6,
    clientId: 1,
    date: "2025-03-01T11:15:00Z",
    type: "note",
    description: "Client requested information about long-term rentals"
  },
  {
    id: 7,
    clientId: 1,
    date: "2025-03-15T13:45:00Z",
    type: "booking",
    description: "Rented Concrete Mixer - 9 cu ft",
    amount: 300,
    bookingId: 5,
    quoteNumber: "Q-567890"
  },
  {
    id: 8,
    clientId: 2,
    date: "2025-02-14T09:15:00Z",
    type: "booking",
    description: "Rented Scissor Lift - 19ft",
    amount: 750,
    bookingId: 2,
    quoteNumber: "Q-234567"
  },
  {
    id: 9,
    clientId: 2,
    date: "2025-02-14T09:20:00Z",
    type: "payment",
    description: "Full payment for booking #2",
    amount: 750
  },
  {
    id: 10,
    clientId: 3,
    date: "2025-01-05T10:00:00Z",
    type: "booking",
    description: "Rented Boom Lift - 45ft",
    amount: 550,
    bookingId: 4,
    quoteNumber: "Q-456789"
  },
  {
    id: 11,
    clientId: 3,
    date: "2025-01-20T11:45:00Z",
    type: "booking",
    description: "Rented Generator - 7500W and Air Compressor - 185 CFM",
    amount: 650,
    bookingId: 6,
    quoteNumber: "Q-678901"
  },
  {
    id: 12,
    clientId: 4,
    date: "2025-03-02T13:00:00Z",
    type: "booking",
    description: "Rented Concrete Saw - 14\"",
    amount: 450,
    bookingId: 7,
    quoteNumber: "Q-789012"
  },
  {
    id: 13,
    clientId: 5,
    date: "2025-01-10T08:30:00Z",
    type: "booking",
    description: "Rented Excavator - Mini",
    amount: 500,
    bookingId: 8,
    quoteNumber: "Q-890123"
  },
  {
    id: 14,
    clientId: 5,
    date: "2025-02-01T09:45:00Z",
    type: "booking",
    description: "Rented Backhoe Loader",
    amount: 650,
    bookingId: 9,
    quoteNumber: "Q-901234"
  },
  {
    id: 15,
    clientId: 5,
    date: "2025-02-20T14:15:00Z",
    type: "booking",
    description: "Rented Trencher - 36\"",
    amount: 540,
    bookingId: 10,
    quoteNumber: "Q-012345"
  },
  {
    id: 16,
    clientId: 5,
    date: "2025-03-15T10:30:00Z",
    type: "booking",
    description: "Rented Forklift - 5000 lb",
    amount: 610,
    bookingId: 11,
    quoteNumber: "Q-123450"
  },
  {
    id: 17,
    clientId: 6,
    date: "2025-04-05T09:45:00Z",
    type: "booking",
    description: "Rented Concrete Mixer - 9 cu ft",
    amount: 350,
    bookingId: 12,
    quoteNumber: "Q-234501"
  }
];

// Generate client statistics
export const clientStatistics: ClientStatistics = {
  totalClients: clients.length,
  newClientsThisMonth: 2,
  activeClients: 4,
  topClients: [
    {
      clientId: 5,
      name: "Robert Wilson",
      totalSpent: 2300
    },
    {
      clientId: 1,
      name: "John Smith",
      totalSpent: 1850
    },
    {
      clientId: 3,
      name: "Michael Brown",
      totalSpent: 1200
    }
  ],
  monthlyRevenue: [
    { month: "Jan", revenue: 2025 },
    { month: "Feb", revenue: 3165 },
    { month: "Mar", revenue: 1360 },
    { month: "Apr", revenue: 350 }
  ]
};