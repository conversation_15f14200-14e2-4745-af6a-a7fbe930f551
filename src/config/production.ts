// Production configuration for Hostinger deployment
export const PRODUCTION_CONFIG = {
  // Update this with your actual Hostinger domain
  API_BASE_URL: 'https://lawngreen-manatee-444371.hostingersite.com/api',
  
  // OneSignal configuration (keep your existing keys)
  ONESIGNAL_APP_ID: 'your-onesignal-app-id',
  
  // Environment
  NODE_ENV: 'production',
  
  // API endpoints
  ENDPOINTS: {
    BOOKINGS: '/bookings.php',
    PRODUCTS: '/products.php',
    PAYMENTS: '/payments.php',
    CATEGORIES: '/categories.php',
    COUPONS: '/coupons.php',
    USERS: '/users.php',
    VENDORS: '/vendors.php',
    CLIENTS: '/clients.php',
    SYSTEM_SETTINGS: '/system_settings.php'
  }
};

// Development configuration (for local development)
export const DEVELOPMENT_CONFIG = {
  API_BASE_URL: 'http://localhost:8000',
  ONESIGNAL_APP_ID: 'your-onesignal-app-id',
  NODE_ENV: 'development',
  ENDPOINTS: {
    BOOKINGS: '/bookings.php',
    PRODUCTS: '/products.php',
    PAYMENTS: '/payments.php',
    CATEGORIES: '/categories.php',
    COUPONS: '/coupons.php',
    USERS: '/users.php',
    VENDORS: '/vendors.php',
    CLIENTS: '/clients.php',
    SYSTEM_SETTINGS: '/system_settings.php'
  }
};

// Get current configuration based on environment
export const getConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production' || 
                      window.location.hostname !== 'localhost';
  
  return isProduction ? PRODUCTION_CONFIG : DEVELOPMENT_CONFIG;
};
