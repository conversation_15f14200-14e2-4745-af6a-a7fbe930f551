import React, { useState } from 'react';
import { X, DollarSign } from 'lucide-react';
import { Coupon, SystemSettings } from '../../types';

interface QuoteDiscountModalProps {
  show: boolean;
  onClose: () => void;
  onSave: (discount: Coupon) => void;
  subtotal: number;
  deliveryFee: number;
  systemSettings: SystemSettings;
  formatCurrency: (amount: number) => string;
}

const QuoteDiscountModal: React.FC<QuoteDiscountModalProps> = ({
  show,
  onClose,
  onSave,
  subtotal,
  deliveryFee,
  systemSettings,
  formatCurrency
}) => {
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>('percentage');
  const [discountValue, setDiscountValue] = useState<string>('');
  const [calculatedDiscount, setCalculatedDiscount] = useState<number>(0);

  if (!show) return null;

  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDiscountValue(value);

    // Calculate the discount amount
    if (value && !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      if (discountType === 'percentage') {
        const discount = (subtotal * numValue) / 100;
        setCalculatedDiscount(parseFloat(discount.toFixed(3)));
      } else {
        setCalculatedDiscount(parseFloat(numValue.toFixed(3)));
      }
    } else {
      setCalculatedDiscount(0);
    }
  };

  const handleSaveDiscount = () => {
    // Create a coupon object with the discount information
    const coupon: Coupon = {
      id: 'manual-discount',
      code: 'MANUAL',
      discountType: discountType,
      discountValue: parseFloat(discountValue || '0'),
      expiryDate: new Date().toISOString().split('T')[0],
      active: true
    };

    onSave(coupon);
    onClose();
  };

  // Calculate subtotal with delivery
  const subtotalWithDelivery = subtotal + deliveryFee;

  // Calculate taxable amount and tax
  const taxableAmount = subtotalWithDelivery - calculatedDiscount;
  const tax = taxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);

  // Calculate final total amount
  const totalAmount = taxableAmount + tax;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold">Add Discount</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Discount Type
            </label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="discountType"
                  value="percentage"
                  checked={discountType === 'percentage'}
                  onChange={() => setDiscountType('percentage')}
                />
                <span className="ml-2">Percentage (%)</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="discountType"
                  value="fixed"
                  checked={discountType === 'fixed'}
                  onChange={() => setDiscountType('fixed')}
                />
                <span className="ml-2">Fixed Amount</span>
              </label>
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {discountType === 'percentage' ? 'Discount Percentage' : 'Discount Amount'}
            </label>
            <input
              type="number"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={discountValue}
              onChange={handleDiscountChange}
              placeholder={discountType === 'percentage' ? 'Enter percentage' : 'Enter amount'}
              min="0"
              max={discountType === 'percentage' ? '100' : undefined}
              step={discountType === 'percentage' ? '1' : '0.001'}
            />
          </div>

          <div className="border-t pt-4 mt-4">
            <h4 className="font-medium mb-2">Or Set New Total Amount</h4>
            <p className="text-sm text-gray-500 mb-2">
              Enter your desired final amount to automatically calculate the discount
            </p>
            <div className="relative">
              <DollarSign size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="number"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
                placeholder="Enter desired total"
                min="0"
                step="0.01"
                onChange={(e) => {
                  const newTotal = parseFloat(e.target.value);
                  if (!isNaN(newTotal) && newTotal >= 0) {
                    // Apply tax rate in reverse to find pre-tax amount
                    const taxRate = systemSettings.enableTax ? systemSettings.taxRate / 100 : 0;
                    const preTaxAmount = newTotal / (1 + taxRate);

                    // Calculate needed discount
                    const neededDiscount = Math.max(0, subtotalWithDelivery - preTaxAmount);

                    // Update discount value and type
                    setDiscountType('fixed');
                    setDiscountValue(neededDiscount.toFixed(3));
                    setCalculatedDiscount(neededDiscount);
                  }
                }}
              />
            </div>
          </div>

          <div className="mb-6 p-4 bg-gray-50 rounded-md mt-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal:</span>
              <span className="font-medium">{formatCurrency(subtotal)}</span>
            </div>
            {deliveryFee > 0 && (
              <div className="flex justify-between mt-2">
                <span className="text-gray-600">Delivery:</span>
                <span className="font-medium">{formatCurrency(deliveryFee)}</span>
              </div>
            )}
            <div className="flex justify-between mt-2">
              <span className="text-gray-600">Discount:</span>
              <span className="font-medium text-red-600">-{formatCurrency(calculatedDiscount)}</span>
            </div>
            {systemSettings.enableTax && (
              <div className="flex justify-between mt-2">
                <span className="text-gray-600">Tax ({systemSettings.taxRate}%):</span>
                <span className="font-medium">{formatCurrency(
                  (subtotalWithDelivery - calculatedDiscount) * systemSettings.taxRate / 100
                )}</span>
              </div>
            )}
            <div className="flex justify-between mt-2 pt-2 border-t border-gray-200">
              <span className="text-gray-600 font-bold">Total:</span>
              <span className="font-medium">{formatCurrency(totalAmount)}</span>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveDiscount}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Apply Discount
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteDiscountModal;
