import React, { useState, useEffect } from 'react';
import { <PERSON>, BellOff } from 'lucide-react';
import { requestNotificationPermission } from '../services/oneSignalService';
import { User } from '../types';

interface NotificationButtonProps {
  user: User;
}

const NotificationButton: React.FC<NotificationButtonProps> = ({ user }) => {
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');

  useEffect(() => {
    // Check if notifications are supported
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return;
    }

    // Get the current permission status
    setPermissionStatus(Notification.permission);
  }, []);

  const handleRequestPermission = async () => {
    try {
      await requestNotificationPermission();
      // OneSignal will handle the permission flow
      // We'll update our UI based on the current permission
      setTimeout(() => {
        if (window.OneSignal) {
          window.OneSignal.push(function() {
            window.OneSignal.isPushNotificationsEnabled().then(function(isEnabled) {
              setPermissionStatus(isEnabled ? 'granted' : 'denied');
            });
          });
        }
      }, 1000); // Give OneSignal time to update its state
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  // If notifications are not supported, don't render the button
  if (!('Notification' in window)) {
    return null;
  }

  return (
    <button
      onClick={handleRequestPermission}
      disabled={permissionStatus === 'granted'}
      className={`flex items-center px-3 py-2 rounded-md ${
        permissionStatus === 'granted'
          ? 'bg-green-100 text-green-800'
          : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
      }`}
    >
      {permissionStatus === 'granted' ? (
        <>
          <Bell size={18} className="mr-2" />
          Notifications Enabled
        </>
      ) : (
        <>
          <BellOff size={18} className="mr-2" />
          Enable Notifications
        </>
      )}
    </button>
  );
};

export default NotificationButton;
