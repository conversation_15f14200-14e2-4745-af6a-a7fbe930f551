import React, { useState, useEffect } from 'react';
import { Download } from 'lucide-react';
import { canInstallApp, showInstallPrompt } from '../services/pwaService';

const InstallButton: React.FC = () => {
  const [canInstall, setCanInstall] = useState<boolean>(false);
  
  useEffect(() => {
    const checkInstallability = async () => {
      const installable = await canInstallApp();
      setCanInstall(installable);
    };
    
    checkInstallability();
    
    // Check again when the window is focused
    window.addEventListener('focus', checkInstallability);
    
    return () => {
      window.removeEventListener('focus', checkInstallability);
    };
  }, []);
  
  const handleInstallClick = async () => {
    const installed = await showInstallPrompt();
    if (installed) {
      setCanInstall(false);
    }
  };
  
  if (!canInstall) {
    return null;
  }
  
  return (
    <button
      onClick={handleInstallClick}
      className="flex items-center px-3 py-2 rounded-md bg-green-100 text-green-800 hover:bg-green-200"
    >
      <Download size={18} className="mr-2" />
      Install App
    </button>
  );
};

export default InstallButton;
