import React, { useState, useEffect } from 'react';
import { Calendar, Clock, ArrowLeft, RefreshCw, ShoppingCart, ChevronLeft, ChevronRight } from 'lucide-react';
import { format, parseISO, isSameDay, addDays, isValid, eachDayOfInterval, startOfMonth, endOfMonth, getDate, getDay, isSameMonth, addMonths, subMonths } from 'date-fns';

interface RentalPeriodProps {
  rentalPeriod: {
    dates: string[];
    days: number;
    rentalType: 'daily' | 'weekly';
  };
  onRentalPeriodSubmit: (rentalPeriod: {
    dates: string[];
    days: number;
    rentalType: 'daily' | 'weekly';
  }) => void;
  onBack: () => void;
}

const RentalPeriod: React.FC<RentalPeriodProps> = ({
  rentalPeriod,
  onRentalPeriodSubmit,
  onBack
}) => {
  const [formData, setFormData] = useState({
    dates: rentalPeriod?.dates || [],
    days: rentalPeriod?.days || 0,
    rentalType: rentalPeriod?.rentalType || 'daily'
  });

  const [selectedDates, setSelectedDates] = useState<Date[]>(
    (rentalPeriod?.dates || []).map(date => {
      const parsedDate = parseISO(date);
      return isValid(parsedDate) ? parsedDate : new Date();
    }).filter(date => isValid(date))
  );

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [errors, setErrors] = useState({
    dates: ''
  });

  // Generate dates for the current month
  const generateCalendarDates = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = monthStart;
    const endDate = monthEnd;

    const days = eachDayOfInterval({ start: startDate, end: endDate });
    const startDay = getDay(monthStart);

    // Add empty cells for days before the first of the month
    const emptyDays = Array(startDay).fill(null);

    return [...emptyDays, ...days];
  };

  const handleDateClick = (date: Date | null) => {
    if (!date) return;

    const isSelected = selectedDates.some(selectedDate => isSameDay(selectedDate, date));

    if (isSelected) {
      setSelectedDates(selectedDates.filter(selectedDate => !isSameDay(selectedDate, date)));
    } else {
      setSelectedDates([...selectedDates, date].sort((a, b) => a.getTime() - b.getTime()));
    }
  };

  useEffect(() => {
    setFormData({
      ...formData,
      dates: selectedDates.map(date => date.toISOString()),
      days: selectedDates.length
    });
  }, [selectedDates]);

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      dates: ''
    };

    if (selectedDates.length === 0) {
      newErrors.dates = 'Please select at least one date';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      window.scrollTo(0, 0);
      onRentalPeriodSubmit(formData);
    }
  };

  // Format selected dates for display in a grouped format
  const formatSelectedDates = () => {
    if (selectedDates.length === 0) return 'No dates selected';

    // Group dates by month and year
    const groupedDates = selectedDates.reduce((acc: Record<string, number[]>, date: Date) => {
      const key = format(date, 'MMM yyyy');
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(getDate(date));
      return acc;
    }, {});

    // Convert the grouped dates to a formatted string
    return Object.entries(groupedDates)
      .map(([monthYear, days]) => {
        // Sort the days
        const sortedDays = [...days].sort((a, b) => a - b);

        // Find consecutive ranges of days
        const ranges: (number | [number, number])[] = [];
        let rangeStart: number | null = null;
        let prev: number | null = null;

        for (const day of sortedDays) {
          if (prev === null) {
            rangeStart = day;
          } else if (day !== prev + 1) {
            if (rangeStart === prev) {
              ranges.push(rangeStart);
            } else {
              ranges.push([rangeStart as number, prev]);
            }
            rangeStart = day;
          }
          prev = day;
        }

        // Add the last range
        if (rangeStart !== null) {
          if (rangeStart === prev) {
            ranges.push(rangeStart);
          } else {
            ranges.push([rangeStart, prev as number]);
          }
        }

        // Format the ranges
        const formattedDays = ranges.map(range => {
          if (Array.isArray(range)) {
            return `${range[0]}-${range[1]}`;
          }
          return `${range}`;
        }).join(', ');

        return `${formattedDays} ${monthYear}`;
      })
      .join(', ');
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <Calendar size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div>
            <span className="font-medium">
              Selected Dates: {formatSelectedDates()}
            </span>
            <div className="text-sm text-gray-600">
              Total Days: {selectedDates.length}
            </div>
          </div>
        </div>
        <button
          onClick={handleSubmit}
          disabled={selectedDates.length === 0}
          className={`px-4 py-2 rounded-md font-medium w-full sm:w-auto ${
            selectedDates.length > 0
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          } transition-colors`}
        >
          Next
        </button>
      </div>
    </div>
  );

  return (
    <div className="pb-20">
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </button>
        <h2 className="text-2xl font-bold">Select Rental Dates</h2>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <div className="flex items-center mb-2">
              <RefreshCw size={18} className="mr-2 text-blue-600" />
              <h3 className="font-medium">Rental Type</h3>
            </div>
            <div className="flex space-x-4 mt-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="rentalType"
                  value="daily"
                  checked={formData.rentalType === 'daily'}
                  onChange={(e) => setFormData({ ...formData, rentalType: e.target.value as 'daily' | 'weekly' })}
                  className="mr-2"
                />
                <span>Daily Rate</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="rentalType"
                  value="weekly"
                  checked={formData.rentalType === 'weekly'}
                  onChange={(e) => setFormData({ ...formData, rentalType: e.target.value as 'daily' | 'weekly' })}
                  className="mr-2"
                />
                <span>Weekly Rate</span>
              </label>
            </div>
          </div>

          {/* Month and Year Navigation */}
          <div className="flex justify-between items-center mb-4">
            <button
              type="button"
              onClick={prevMonth}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronLeft size={20} />
            </button>
            <h3 className="font-bold text-lg">
              {format(currentMonth, 'MMMM yyyy')}
            </h3>
            <button
              type="button"
              onClick={nextMonth}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ChevronRight size={20} />
            </button>
          </div>

          <div className="grid grid-cols-7 gap-1">
            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
              <div key={day} className="text-center font-medium text-gray-600 py-2 text-sm">
                {day}
              </div>
            ))}
            {generateCalendarDates().map((date, index) => {
              if (!date) {
                return <div key={`empty-${index}`} className="p-2" />;
              }

              const isSelected = selectedDates.some(selectedDate => isSameDay(selectedDate, date));
              const isToday = isSameDay(date, new Date());
              const isCurrentMonth = isSameMonth(date, currentMonth);
              const isPastDate = date < new Date() && !isToday;

              return (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleDateClick(date)}
                  className={`p-2 rounded-full text-center ${
                    !isCurrentMonth ? 'text-gray-300' :
                    isSelected
                      ? 'bg-blue-600 text-white'
                      : isToday
                      ? 'bg-blue-50 text-blue-600'
                      : isPastDate
                      ? 'text-gray-500 hover:bg-gray-100 italic'
                      : 'hover:bg-gray-100'
                  }`}
                  disabled={!isCurrentMonth}
                >
                  {format(date, 'd')}
                </button>
              );
            })}
          </div>

          {errors.dates && (
            <p className="text-red-500 text-sm mt-2">{errors.dates}</p>
          )}

          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium mb-2">Rental Information</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Click on dates to select them for rental.</li>
              <li>• Click again to deselect a date.</li>
              <li>• Both past and future dates can be selected.</li>
              <li>• Equipment will be delivered on each selected date between 8am-12pm.</li>
              <li>• Pickup will be scheduled on each selected date between 1pm-5pm.</li>
              <li>• You will be charged for each selected date.</li>
              <li>• Cancellations must be made at least 48 hours in advance for a full refund.</li>
              {formData.rentalType === 'weekly' && (
                <li>• Weekly rates are applied for rentals of 7 days or more, offering significant savings.</li>
              )}
            </ul>
          </div>

          {selectedDates.length > 0 && (
            <div className="bg-blue-50 p-4 rounded-md">
              <h3 className="font-medium mb-2">Selected Dates</h3>
              <p className="text-sm">{formatSelectedDates()}</p>
              <p className="text-sm mt-2">Total Days: {selectedDates.length}</p>
            </div>
          )}
        </div>
      </form>

      {/* Fixed Cart Summary */}
      <CartSummary />
    </div>
  );
};

export default RentalPeriod;