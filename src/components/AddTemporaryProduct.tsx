import React, { useState } from 'react';
import { Camera, X } from 'lucide-react';
import { Product } from '../types';

interface AddTemporaryProductProps {
  onClose: () => void;
  onAdd: (product: Product) => void;
}

const AddTemporaryProduct: React.FC<AddTemporaryProductProps> = ({ onClose, onAdd }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [dailyRate, setDailyRate] = useState('');
  const [quantity, setQuantity] = useState(1);

  const handleSubmit = () => {
    if (!name || !dailyRate) {
      alert('Please fill in all required fields');
      return;
    }

    const tempProduct: Product = {
      id: Date.now(),
      name,
      category: 'temporary',
      dailyRate: parseFloat(dailyRate),
      description,
      image: 'https://placehold.co/400x300/2563eb/ffffff?text=Camera',
      available: true,
      quantity,
      stock: 99,
      featured: false
    };

    onAdd(tempProduct);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Add Temporary Item</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          {/* Image Placeholder */}
          <div className="bg-blue-100 rounded-lg p-8 flex items-center justify-center">
            <Camera size={64} className="text-blue-600" />
          </div>

          {/* Name */}
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              Item Name*
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Enter item name"
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              placeholder="Enter item description"
            />
          </div>

          {/* Daily Rate */}
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              Daily Rate (BHD)*
            </label>
            <input
              type="number"
              value={dailyRate}
              onChange={(e) => setDailyRate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              min="0"
              step="0.001"
              placeholder="0.000"
            />
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-gray-700 mb-2 font-medium">
              Quantity
            </label>
            <input
              type="number"
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
              className="w-full p-2 border border-gray-300 rounded-md"
              min="1"
            />
          </div>
        </div>

        <div className="flex justify-end mt-6 space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!name || !dailyRate}
            className={`px-4 py-2 rounded-md ${
              name && dailyRate
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Add Item
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddTemporaryProduct;