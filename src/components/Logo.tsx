import React from 'react';

interface LogoProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  width = 48, 
  height = 24, 
  color = 'currentColor',
  className = ''
}) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width={width} 
      height={height} 
      viewBox="0 0 282 139" 
      preserveAspectRatio="xMidYMid meet"
      className={className}
    >
      <g transform="translate(0.000000,139.000000) scale(0.100000,-0.100000)" fill={color} stroke="none">
        <path d="M140 1210 l0 -179 -67 -3 -68 -3 -3 -127 -3 -128 71 0 70 0 0 -192 c0 -234 11 -313 51 -395 55 -113 139 -158 294 -158 90 1 182 21 219 48 18 13 17 18 -19 138 -21 68 -39 127 -41 130 -2 4 -22 1 -44 -7 -52 -18 -86 -5 -105 41 -11 26 -15 80 -15 215 l0 180 83 0 82 0 191 -358 192 -357 114 -3 115 -3 56 123 c30 68 79 177 108 243 29 66 55 123 58 127 3 4 56 -104 116 -240 l111 -247 114 -3 115 -3 258 480 c141 264 257 485 257 491 0 6 -66 10 -189 10 l-188 0 -120 -265 -120 -266 -118 266 -119 266 -114 -3 -115 -3 -117 -263 -117 -263 -119 263 -120 263 -207 3 -207 2 0 180 0 180 -170 0 -170 0 0 -180z"/>
        <path d="M2521 450 c-92 -23 -161 -111 -161 -208 0 -217 270 -301 395 -124 27 40 30 51 30 121 0 70 -3 82 -29 120 -51 74 -150 112 -235 91z"/>
      </g>
    </svg>
  );
};

// Export a base64 version of the logo for use in PDFs
export const getLogoAsBase64 = (color: string = '#2563eb'): string => {
  const svgContent = `
    <svg xmlns="http://www.w3.org/2000/svg" width="282" height="139" viewBox="0 0 282 139" preserveAspectRatio="xMidYMid meet">
      <g transform="translate(0.000000,139.000000) scale(0.100000,-0.100000)" fill="${color}" stroke="none">
        <path d="M140 1210 l0 -179 -67 -3 -68 -3 -3 -127 -3 -128 71 0 70 0 0 -192 c0 -234 11 -313 51 -395 55 -113 139 -158 294 -158 90 1 182 21 219 48 18 13 17 18 -19 138 -21 68 -39 127 -41 130 -2 4 -22 1 -44 -7 -52 -18 -86 -5 -105 41 -11 26 -15 80 -15 215 l0 180 83 0 82 0 191 -358 192 -357 114 -3 115 -3 56 123 c30 68 79 177 108 243 29 66 55 123 58 127 3 4 56 -104 116 -240 l111 -247 114 -3 115 -3 258 480 c141 264 257 485 257 491 0 6 -66 10 -189 10 l-188 0 -120 -265 -120 -266 -118 266 -119 266 -114 -3 -115 -3 -117 -263 -117 -263 -119 263 -120 263 -207 3 -207 2 0 180 0 180 -170 0 -170 0 0 -180z"/>
        <path d="M2521 450 c-92 -23 -161 -111 -161 -208 0 -217 270 -301 395 -124 27 40 30 51 30 121 0 70 -3 82 -29 120 -51 74 -150 112 -235 91z"/>
      </g>
    </svg>
  `;
  
  // Convert SVG to base64
  return `data:image/svg+xml;base64,${btoa(svgContent.trim())}`;
};

export default Logo;
