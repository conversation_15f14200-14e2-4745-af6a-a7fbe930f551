import React, { useState, useEffect } from 'react';
import { Calendar, DollarSign, TrendingUp, TrendingDown, BarChart4 } from 'lucide-react';
import { Vendor, Product } from '../../types';
import { getVendorRevenueData } from '../../services/database';

interface VendorReportingProps {
  vendor: Vendor;
  vendorProducts: Product[];
  onDateRangeChange?: (startDate: Date, endDate: Date) => void;
}

const VendorReporting: React.FC<VendorReportingProps> = ({ 
  vendor, 
  vendorProducts,
  onDateRangeChange
}) => {
  const [dateRange, setDateRange] = useState<'7days' | '30days' | '90days' | 'custom'>('30days');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [revenueData, setRevenueData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (vendor?.id) {
      loadReportData();
    }
  }, [vendor, dateRange, startDate, endDate]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // Calculate date range
      const end = endDate ? new Date(endDate) : new Date();
      let start = new Date();
      
      if (dateRange === '7days') {
        start.setDate(end.getDate() - 7);
      } else if (dateRange === '30days') {
        start.setDate(end.getDate() - 30);
      } else if (dateRange === '90days') {
        start.setDate(end.getDate() - 90);
      } else if (dateRange === 'custom' && startDate) {
        start = new Date(startDate);
      }
      
      // Ensure end date includes the entire day
      end.setHours(23, 59, 59, 999);
      
      if (onDateRangeChange) {
        onDateRangeChange(start, end);
      }
      
      const data = await getVendorRevenueData(vendor.id, start, end);
      setRevenueData(data);
    } catch (error) {
      console.error('Error loading vendor revenue data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (range: '7days' | '30days' | '90days' | 'custom') => {
    setDateRange(range);
    
    if (range !== 'custom') {
      setStartDate('');
      setEndDate('');
    } else if (!startDate || !endDate) {
      // Set default custom range if not already set
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 30);
      
      setStartDate(start.toISOString().split('T')[0]);
      setEndDate(end.toISOString().split('T')[0]);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h2 className="text-xl font-semibold mb-4">Financial Report</h2>
      
      {/* Date Range Controls */}
      <div className="flex flex-wrap items-center gap-3 mb-6">
        <div className="flex rounded-md overflow-hidden border border-gray-300">
          <button
            className={`px-3 py-2 text-sm ${dateRange === '7days' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-50 text-gray-700 hover:bg-gray-100'}`}
            onClick={() => handleDateRangeChange('7days')}
          >
            7 Days
          </button>
          <button
            className={`px-3 py-2 text-sm ${dateRange === '30days' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-50 text-gray-700 hover:bg-gray-100'}`}
            onClick={() => handleDateRangeChange('30days')}
          >
            30 Days
          </button>
          <button
            className={`px-3 py-2 text-sm ${dateRange === '90days' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-50 text-gray-700 hover:bg-gray-100'}`}
            onClick={() => handleDateRangeChange('90days')}
          >
            90 Days
          </button>
          <button
            className={`px-3 py-2 text-sm ${dateRange === 'custom' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-50 text-gray-700 hover:bg-gray-100'}`}
            onClick={() => handleDateRangeChange('custom')}
          >
            Custom
          </button>
        </div>
        
        {dateRange === 'custom' && (
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <span className="text-gray-600 mr-2 text-sm">From:</span>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="flex items-center">
              <span className="text-gray-600 mr-2 text-sm">To:</span>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>
        )}
      </div>
      
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading financial data...</p>
        </div>
      ) : (
        <>
          {revenueData ? (
            <div>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <DollarSign className="text-blue-600 mr-2" size={20} />
                    <h3 className="text-blue-800 font-medium">Total Revenue</h3>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">
                    {formatCurrency(revenueData.totalRevenue || 0)}
                  </p>
                </div>
                
                <div className="bg-amber-50 border border-amber-100 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <TrendingDown className="text-amber-600 mr-2" size={20} />
                    <h3 className="text-amber-800 font-medium">Vendor Cost</h3>
                  </div>
                  <p className="text-2xl font-bold text-amber-900">
                    {formatCurrency(revenueData.totalCost || 0)}
                  </p>
                </div>
                
                <div className="bg-green-50 border border-green-100 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <TrendingUp className="text-green-600 mr-2" size={20} />
                    <h3 className="text-green-800 font-medium">Profit</h3>
                  </div>
                  <p className="text-2xl font-bold text-green-900">
                    {formatCurrency(revenueData.totalProfit || 0)}
                  </p>
                  {revenueData.totalRevenue > 0 && (
                    <p className="text-sm text-green-700">
                      {Math.round((revenueData.totalProfit / revenueData.totalRevenue) * 100)}% margin
                    </p>
                  )}
                </div>
              </div>
              
              {/* Bookings Table */}
              <div className="overflow-x-auto">
                <h3 className="text-lg font-medium mb-3">Bookings with Vendor Products</h3>
                {revenueData.bookings.length > 0 ? (
                  <table className="min-w-full bg-white border border-gray-200">
                    <thead>
                      <tr className="bg-gray-50 text-gray-700 text-left">
                        <th className="py-2 px-3 border-b text-sm font-medium">Quote #</th>
                        <th className="py-2 px-3 border-b text-sm font-medium">Date</th>
                        <th className="py-2 px-3 border-b text-sm font-medium">Customer</th>
                        <th className="py-2 px-3 border-b text-sm font-medium">Revenue</th>
                        <th className="py-2 px-3 border-b text-sm font-medium">Cost</th>
                        <th className="py-2 px-3 border-b text-sm font-medium">Profit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {revenueData.bookings.map((bookingData: any, index: number) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-2 px-3 text-sm font-medium">{bookingData.booking.quoteNumber}</td>
                          <td className="py-2 px-3 text-sm">
                            {new Date(bookingData.booking.date).toLocaleDateString()}
                          </td>
                          <td className="py-2 px-3 text-sm">{bookingData.booking.customer.name}</td>
                          <td className="py-2 px-3 text-sm">{formatCurrency(bookingData.revenue || 0)}</td>
                          <td className="py-2 px-3 text-sm">{formatCurrency(bookingData.cost || 0)}</td>
                          <td className="py-2 px-3 text-sm font-medium text-green-600">
                            {formatCurrency(bookingData.profit || 0)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-50 font-medium">
                        <td colSpan={3} className="py-2 px-3 text-right text-sm">Totals:</td>
                        <td className="py-2 px-3 text-sm">{formatCurrency(revenueData.totalRevenue || 0)}</td>
                        <td className="py-2 px-3 text-sm">{formatCurrency(revenueData.totalCost || 0)}</td>
                        <td className="py-2 px-3 text-sm text-green-600">
                          {formatCurrency(revenueData.totalProfit || 0)}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <BarChart4 className="mx-auto text-gray-400 mb-3" size={32} />
                    <p className="text-gray-500">No bookings found for this vendor in the selected period.</p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Calendar className="mx-auto text-gray-400 mb-3" size={32} />
              <p className="text-gray-500">Select a date range to view revenue data.</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default VendorReporting; 