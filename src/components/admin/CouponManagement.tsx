import React, { useState } from 'react';
import { Plus, Pencil, Trash2, X, Save, Tag, Calendar } from 'lucide-react';
import { Coupon } from '../../types';

interface CouponManagementProps {
  coupons: Coupon[];
  onCouponsUpdate: (coupons: Coupon[]) => void;
}

const CouponManagement: React.FC<CouponManagementProps> = ({ coupons, onCouponsUpdate }) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCouponId, setEditingCouponId] = useState<number | null>(null);
  const [newCoupon, setNewCoupon] = useState<Partial<Coupon>>({
    code: '',
    discountType: 'percentage',
    discountValue: 10,
    expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
    active: true
  });
  const [error, setError] = useState('');

  const handleAddCoupon = () => {
    if (!newCoupon.code || !newCoupon.discountValue || !newCoupon.expiryDate) {
      setError('Please fill in all required fields');
      return;
    }

    // Check if coupon code already exists
    if (coupons.some(coupon => coupon.code.toLowerCase() === newCoupon.code?.toLowerCase())) {
      setError('Coupon code already exists');
      return;
    }

    const couponToAdd: Coupon = {
      id: Date.now(),
      code: newCoupon.code,
      discountType: newCoupon.discountType || 'percentage',
      discountValue: newCoupon.discountValue,
      expiryDate: newCoupon.expiryDate,
      active: newCoupon.active || true
    };

    const updatedCoupons = [...coupons, couponToAdd];
    onCouponsUpdate(updatedCoupons);
    
    // Reset form
    setNewCoupon({
      code: '',
      discountType: 'percentage',
      discountValue: 10,
      expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
      active: true
    });
    setShowAddForm(false);
    setError('');
  };

  const handleEditCoupon = (coupon: Coupon) => {
    setEditingCouponId(coupon.id);
    setNewCoupon({ ...coupon });
  };

  const handleUpdateCoupon = () => {
    if (!newCoupon.code || !newCoupon.discountValue || !newCoupon.expiryDate) {
      setError('Please fill in all required fields');
      return;
    }

    // Check if coupon code already exists (excluding the current coupon)
    if (coupons.some(coupon => 
      coupon.code.toLowerCase() === newCoupon.code?.toLowerCase() && 
      coupon.id !== editingCouponId
    )) {
      setError('Coupon code already exists');
      return;
    }

    const updatedCoupons = coupons.map(coupon => 
      coupon.id === editingCouponId ? { ...coupon, ...newCoupon } as Coupon : coupon
    );
    
    onCouponsUpdate(updatedCoupons);
    setEditingCouponId(null);
    setNewCoupon({
      code: '',
      discountType: 'percentage',
      discountValue: 10,
      expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
      active: true
    });
    setError('');
  };

  const handleDeleteCoupon = (id: number) => {
    if (window.confirm('Are you sure you want to delete this coupon?')) {
      const updatedCoupons = coupons.filter(coupon => coupon.id !== id);
      onCouponsUpdate(updatedCoupons);
    }
  };

  const handleToggleActive = (id: number) => {
    const updatedCoupons = coupons.map(coupon => 
      coupon.id === id ? { ...coupon, active: !coupon.active } : coupon
    );
    onCouponsUpdate(updatedCoupons);
  };

  const handleCancelEdit = () => {
    setEditingCouponId(null);
    setNewCoupon({
      code: '',
      discountType: 'percentage',
      discountValue: 10,
      expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
      active: true
    });
    setError('');
  };

  const isCouponExpired = (expiryDate: string) => {
    return new Date(expiryDate) < new Date();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Coupon Management</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <Plus size={18} className="mr-2" />
          Add Coupon
        </button>
      </div>

      {/* Add/Edit Coupon Form */}
      {(showAddForm || editingCouponId !== null) && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg">
              {editingCouponId !== null ? 'Edit Coupon' : 'Add New Coupon'}
            </h3>
            <button
              onClick={() => {
                setShowAddForm(false);
                handleCancelEdit();
              }}
              className="p-1 rounded-full hover:bg-gray-200"
            >
              <X size={20} />
            </button>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Coupon Code*
              </label>
              <div className="flex">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Tag size={18} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={newCoupon.code || ''}
                    onChange={(e) => setNewCoupon({ ...newCoupon, code: e.target.value.toUpperCase() })}
                    className="pl-10 w-full p-2 border border-gray-300 rounded-md"
                    placeholder="SUMMER25"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Discount Type*
              </label>
              <select
                value={newCoupon.discountType || 'percentage'}
                onChange={(e) => setNewCoupon({ ...newCoupon, discountType: e.target.value as 'percentage' | 'fixed' })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="percentage">Percentage (%)</option>
                <option value="fixed">Fixed Amount ($)</option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                {newCoupon.discountType === 'percentage' ? 'Discount Percentage (%)' : 'Discount Amount ($)'}*
              </label>
              <input
                type="number"
                value={newCoupon.discountValue || ''}
                onChange={(e) => setNewCoupon({ ...newCoupon, discountValue: parseFloat(e.target.value) })}
                className="w-full p-2 border border-gray-300 rounded-md"
                min="0"
                max={newCoupon.discountType === 'percentage' ? "100" : undefined}
                step={newCoupon.discountType === 'percentage' ? "1" : "0.01"}
              />
            </div>

            <div>
              <label className="block text-gray-700 mb-2 font-medium">
                Expiry Date*
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar size={18} className="text-gray-400" />
                </div>
                <input
                  type="date"
                  value={newCoupon.expiryDate || ''}
                  onChange={(e) => setNewCoupon({ ...newCoupon, expiryDate: e.target.value })}
                  className="pl-10 w-full p-2 border border-gray-300 rounded-md"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={newCoupon.active || false}
                  onChange={(e) => setNewCoupon({ ...newCoupon, active: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-gray-700">Active</span>
              </label>
              <p className="text-sm text-gray-500 mt-1">
                Inactive coupons cannot be used by customers
              </p>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={() => {
                setShowAddForm(false);
                handleCancelEdit();
              }}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2"
            >
              Cancel
            </button>
            <button
              onClick={editingCouponId !== null ? handleUpdateCoupon : handleAddCoupon}
              className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center"
            >
              <Save size={18} className="mr-2" />
              {editingCouponId !== null ? 'Update Coupon' : 'Add Coupon'}
            </button>
          </div>
        </div>
      )}

      {/* Coupons Table */}
      <div className="border rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Code
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry Date
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {coupons.map(coupon => {
                const isExpired = isCouponExpired(coupon.expiryDate);
                
                return (
                  <tr key={coupon.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Tag size={18} className="text-blue-600 mr-2" />
                        <span className="font-bold">{coupon.code}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {coupon.discountType === 'percentage' 
                        ? `${coupon.discountValue}%` 
                        : `$${coupon.discountValue.toFixed(2)}`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={isExpired ? 'text-red-600' : ''}>
                        {new Date(coupon.expiryDate).toLocaleDateString()}
                      </span>
                      {isExpired && (
                        <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                          Expired
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleToggleActive(coupon.id)}
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          coupon.active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {coupon.active ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex justify-center space-x-2">
                        <button
                          onClick={() => handleEditCoupon(coupon)}
                          className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
                          title="Edit"
                        >
                          <Pencil size={18} />
                        </button>
                        <button
                          onClick={() => handleDeleteCoupon(coupon.id)}
                          className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                          title="Delete"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {coupons.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No coupons available. Add your first coupon!</p>
        </div>
      )}
    </div>
  );
};

export default CouponManagement;