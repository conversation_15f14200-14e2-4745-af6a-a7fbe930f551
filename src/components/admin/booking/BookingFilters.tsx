import React from 'react';
import { Search, Filter, Calendar } from 'lucide-react';

interface BookingFiltersProps {
  searchTerm: string;
  statusFilter: string;
  startDate: string;
  endDate: string;
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onDateChange: (start: string, end: string) => void;
}

const BookingFilters: React.FC<BookingFiltersProps> = ({
  searchTerm,
  statusFilter,
  startDate,
  endDate,
  onSearchChange,
  onStatusFilterChange,
  onDateChange
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-2 w-full">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        {/* Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
            <Search size={16} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search bookings..."
            className="pl-8 pr-3 py-1.5 w-full border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        
        {/* Date Range - Mobile friendly layout */}
        <div className="flex items-center space-x-1">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
              <Calendar size={16} className="text-gray-400" />
            </div>
            <input
              type="date"
              className="pl-8 pr-1 py-1.5 w-full border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              value={startDate}
              onChange={(e) => onDateChange(e.target.value, endDate)}
            />
          </div>
          <span className="text-gray-500 text-xs px-1">to</span>
          <div className="relative flex-1">
            <input
              type="date"
              className="pl-2 pr-1 py-1.5 w-full border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              value={endDate}
              min={startDate}
              onChange={(e) => onDateChange(startDate, e.target.value)}
            />
          </div>
        </div>
        
        {/* Status Filter */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
            <Filter size={16} className="text-gray-400" />
          </div>
          <select
            className="pl-8 pr-3 py-1.5 border border-gray-300 rounded-md text-sm w-full appearance-none bg-white focus:ring-blue-500 focus:border-blue-500"
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="paid">Fully Paid</option>
            <option value="partial">Partially Paid</option>
            <option value="unpaid">Unpaid</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingFilters;