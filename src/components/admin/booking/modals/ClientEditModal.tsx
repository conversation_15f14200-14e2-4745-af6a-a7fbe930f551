import React from 'react';
import { X } from 'lucide-react';
import { Booking, Client } from '../../../../types';
import ClientSelector from '../ClientSelector';

interface ClientEditModalProps {
  booking: Booking | null;
  onClose: () => void;
  onSave: () => void;
  onClientChange: (client: Client) => void;
}

const ClientEditModal: React.FC<ClientEditModalProps> = ({
  booking,
  onClose,
  onSave,
  onClientChange
}) => {
  if (!booking) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold">Change Client</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Client
            </label>
            <ClientSelector
              selectedClientEmail={booking.customer.email}
              onClientSelect={onClientChange}
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={onSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientEditModal;
