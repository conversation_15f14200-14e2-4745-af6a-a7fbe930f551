import React from 'react';
import { X, CreditCard, DollarSign, Building } from 'lucide-react';

interface PaymentModalProps {
  show: boolean;
  isEditing: boolean;
  payment: {
    amount: string;
    method: 'cash' | 'card' | 'bank_transfer';
    reference: string;
    notes: string;
    date?: string;
  };
  onClose: () => void;
  onSave: () => void;
  onPaymentChange: (field: string, value: string) => void;
  bookingId?: string;
  calculateRemainingAmount?: (bookingId: string) => number;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  show,
  isEditing,
  payment,
  onClose,
  onSave,
  onPaymentChange,
  bookingId,
  calculateRemainingAmount
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">{isEditing ? 'Edit Payment' : 'Add Payment'}</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount
            </label>
            <div className="relative">
              <DollarSign size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="number"
                className="w-full pl-10 p-2 border border-gray-300 rounded-md"
                value={payment.amount}
                onChange={(e) => onPaymentChange('amount', e.target.value)}
                placeholder="Enter amount"
                step="0.001"
                min="0"
              />
            </div>

            {bookingId && calculateRemainingAmount && (
              <div className="mt-2">
                <button
                  type="button"
                  onClick={() => {
                    if (!bookingId) return;
                    const remainingAmount = calculateRemainingAmount(bookingId);
                    onPaymentChange('amount', remainingAmount.toFixed(3));
                  }}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  Pay Full Amount (Remaining Balance)
                </button>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Method
            </label>
            <div className="grid grid-cols-3 gap-2">
              <button
                type="button"
                className={`p-2 border rounded-md flex items-center justify-center ${
                  payment.method === 'cash'
                    ? 'bg-green-100 text-green-800 ring-1 ring-green-300'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
                onClick={() => onPaymentChange('method', 'cash')}
              >
                <DollarSign size={16} className="mr-1" />
                <span>Cash</span>
              </button>
              <button
                type="button"
                className={`p-2 border rounded-md flex items-center justify-center ${
                  payment.method === 'card'
                    ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-300'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
                onClick={() => onPaymentChange('method', 'card')}
              >
                <CreditCard size={16} className="mr-1" />
                <span>Card</span>
              </button>
              <button
                type="button"
                className={`p-2 border rounded-md flex items-center justify-center ${
                  payment.method === 'bank_transfer'
                    ? 'bg-purple-100 text-purple-800 ring-1 ring-purple-300'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
                onClick={() => onPaymentChange('method', 'bank_transfer')}
              >
                <Building size={16} className="mr-1" />
                <span>Transfer</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Date
            </label>
            <input
              type="date"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={payment.date || new Date().toISOString().split('T')[0]}
              onChange={(e) => onPaymentChange('date', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reference
            </label>
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={payment.reference}
              onChange={(e) => onPaymentChange('reference', e.target.value)}
              placeholder="Transaction reference (optional)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              value={payment.notes}
              onChange={(e) => onPaymentChange('notes', e.target.value)}
              placeholder="Additional notes (optional)"
              rows={3}
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            disabled={!payment.amount || parseFloat(payment.amount) <= 0}
          >
            {isEditing ? 'Update Payment' : 'Add Payment'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
