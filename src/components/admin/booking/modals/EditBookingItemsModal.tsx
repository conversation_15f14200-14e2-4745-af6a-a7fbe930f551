import React from 'react';
import { X } from 'lucide-react';
import { Booking, Product, SystemSettings } from '../../../../types';
import BookingItems from '../BookingItems';

interface EditBookingItemsModalProps {
  booking: Booking | null;
  products: Product[];
  systemSettings: SystemSettings;
  formatCurrency: (amount: number) => string;
  onClose: () => void;
  onSave: () => void;
  onBookingUpdate: (updatedBooking: Booking) => void;
}

const EditBookingItemsModal: React.FC<EditBookingItemsModalProps> = ({
  booking,
  products,
  systemSettings,
  formatCurrency,
  onClose,
  onSave,
  onBookingUpdate
}) => {
  if (!booking) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto relative">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="text-xl font-bold">Edit Items</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-6 pb-20">
          <BookingItems
            booking={booking}
            products={products}
            systemSettings={systemSettings}
            formatCurrency={formatCurrency}
            onBookingUpdate={onBookingUpdate}
            maxItemsPerPage={5}
          />
        </div>
        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditBookingItemsModal;
