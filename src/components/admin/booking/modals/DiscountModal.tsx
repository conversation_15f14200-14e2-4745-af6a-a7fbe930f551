import React, { useState, useEffect } from 'react';
import { X, DollarSign } from 'lucide-react';
import { Booking } from '../../../../types';

interface DiscountModalProps {
  booking: Booking | null;
  onClose: () => void;
  onSave: (updatedBooking: Booking) => void;
  formatCurrency: (amount: number) => string;
  systemSettings?: {
    enableTax: boolean;
    taxRate: number;
  };
}

const DiscountModal: React.FC<DiscountModalProps> = ({
  booking,
  onClose,
  onSave,
  formatCurrency,
  systemSettings = { enableTax: false, taxRate: 0 }
}) => {
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>('percentage');
  const [discountValue, setDiscountValue] = useState<string>('');
  const [calculatedDiscount, setCalculatedDiscount] = useState<number>(0);

  useEffect(() => {
    if (booking) {
      // Initialize with existing discount if any
      if (booking.coupon) {
        if (booking.coupon.discountType === 'percentage') {
          setDiscountType('percentage');
          setDiscountValue(booking.coupon.discountValue?.toString() || '');
        } else {
          setDiscountType('fixed');
          setDiscountValue(booking.coupon.discountValue?.toString() || '');
        }

        // Calculate the discount amount
        const subtotalWithDelivery = booking.subtotal + (booking.deliveryFee || 0);
        const discount = booking.coupon.discountType === 'percentage' ?
          (subtotalWithDelivery * booking.coupon.discountValue / 100) :
          booking.coupon.discountValue;

        setCalculatedDiscount(discount);
      } else if (booking.discount) {
        // Fallback to old discount structure if present
        if (booking.discountType === 'percentage') {
          setDiscountType('percentage');
          setDiscountValue(booking.discountPercentage?.toString() || '');
        } else {
          setDiscountType('fixed');
          setDiscountValue(booking.discount.toString());
        }
        setCalculatedDiscount(booking.discount);
      } else {
        setDiscountType('percentage');
        setDiscountValue('');
        setCalculatedDiscount(0);
      }
    }
  }, [booking]);

  if (!booking) return null;

  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDiscountValue(value);

    // Calculate the discount amount
    if (value && !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      if (discountType === 'percentage') {
        const discount = (booking.subtotal * numValue) / 100;
        setCalculatedDiscount(parseFloat(discount.toFixed(3)));
      } else {
        setCalculatedDiscount(parseFloat(numValue.toFixed(3)));
      }
    } else {
      setCalculatedDiscount(0);
    }
  };

  const handleSaveDiscount = () => {
    if (!booking) return;

    // Calculate subtotal with delivery
    const subtotalWithDelivery = booking.subtotal + (booking.deliveryFee || 0);

    // Calculate discount
    const discount = discountType === 'percentage' ?
      (subtotalWithDelivery * parseFloat(discountValue || '0') / 100) :
      parseFloat(discountValue || '0');

    // Calculate taxable amount and tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);

    // Calculate final total amount
    const totalAmount = booking.status === 'cancelled' ? 0 : (taxableAmount + tax);

    const updatedBooking = {
      ...booking,
      // Add both the old and new discount structure to ensure compatibility
      discount: discount,
      discountType: discountType,
      discountPercentage: discountType === 'percentage' ? parseFloat(discountValue || '0') : undefined,
      // Add the coupon structure that's used in calculateBookingTotal
      coupon: {
        id: 'manual-discount',
        code: 'MANUAL',
        discountType: discountType,
        discountValue: parseFloat(discountValue || '0'),
        expiryDate: new Date().toISOString().split('T')[0],
        active: true
      },
      // Update the tax and total amount
      tax: tax,
      totalAmount: totalAmount,
      total: totalAmount
    };

    onSave(updatedBooking);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold">Manage Discount</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-6">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Discount Type
            </label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="discountType"
                  value="percentage"
                  checked={discountType === 'percentage'}
                  onChange={() => setDiscountType('percentage')}
                />
                <span className="ml-2">Percentage (%)</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio"
                  name="discountType"
                  value="fixed"
                  checked={discountType === 'fixed'}
                  onChange={() => setDiscountType('fixed')}
                />
                <span className="ml-2">Fixed Amount</span>
              </label>
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {discountType === 'percentage' ? 'Discount Percentage' : 'Discount Amount'}
            </label>
            <input
              type="number"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={discountValue}
              onChange={handleDiscountChange}
              placeholder={discountType === 'percentage' ? 'Enter percentage' : 'Enter amount'}
              min="0"
              max={discountType === 'percentage' ? '100' : undefined}
              step={discountType === 'percentage' ? '1' : '0.001'}
            />
          </div>

          <div className="border-t pt-4 mt-4">
            <h4 className="font-medium mb-2">Or Set New Total Amount</h4>
            <p className="text-sm text-gray-500 mb-2">
              Enter your desired final amount to automatically calculate the discount
            </p>
            <div className="relative">
              <DollarSign size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="number"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
                placeholder="Enter desired total"
                min="0"
                step="0.01"
                onChange={(e) => {
                  const newTotal = parseFloat(e.target.value);
                  if (!isNaN(newTotal) && newTotal >= 0) {
                    // Calculate subtotal with delivery
                    const subtotalWithDelivery = booking.subtotal + (booking.deliveryFee || 0);

                    // Apply tax rate in reverse to find pre-tax amount
                    const taxRate = systemSettings.enableTax ? systemSettings.taxRate / 100 : 0;
                    const preTaxAmount = newTotal / (1 + taxRate);

                    // Calculate needed discount
                    const neededDiscount = Math.max(0, subtotalWithDelivery - preTaxAmount);

                    // Update discount value and type
                    setDiscountType('fixed');
                    setDiscountValue(neededDiscount.toFixed(3));
                    setCalculatedDiscount(neededDiscount);
                  }
                }}
              />
            </div>
          </div>

          <div className="mb-6 p-4 bg-gray-50 rounded-md mt-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal:</span>
              <span className="font-medium">{formatCurrency(booking.subtotal || 0)}</span>
            </div>
            {booking.deliveryFee > 0 && (
              <div className="flex justify-between mt-2">
                <span className="text-gray-600">Delivery:</span>
                <span className="font-medium">{formatCurrency(booking.deliveryFee || 0)}</span>
              </div>
            )}
            <div className="flex justify-between mt-2">
              <span className="text-gray-600">Discount:</span>
              <span className="font-medium text-red-600">-{formatCurrency(calculatedDiscount)}</span>
            </div>
            {systemSettings.enableTax && (
              <div className="flex justify-between mt-2">
                <span className="text-gray-600">Tax ({systemSettings.taxRate}%):</span>
                <span className="font-medium">{formatCurrency(
                  ((booking.subtotal + (booking.deliveryFee || 0) - calculatedDiscount) * systemSettings.taxRate / 100)
                )}</span>
              </div>
            )}
            <div className="flex justify-between mt-2 pt-2 border-t border-gray-200">
              <span className="text-gray-600 font-bold">Total:</span>
              <span className="font-medium">{formatCurrency(
                (booking.subtotal + (booking.deliveryFee || 0) - calculatedDiscount) *
                (1 + (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0))
              )}</span>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveDiscount}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Apply Discount
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiscountModal;
