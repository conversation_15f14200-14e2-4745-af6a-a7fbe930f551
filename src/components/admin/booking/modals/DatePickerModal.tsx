import React, { lazy, Suspense } from 'react';
import { X, Loader } from 'lucide-react';
import { Booking } from '../../../../types';

// Import RentalPeriod component to use in the modal
const RentalPeriod = lazy(() => import('../../../../components/RentalPeriod'));

interface DatePickerModalProps {
  show: boolean;
  onClose: () => void;
  booking: Booking;
  onSave: (dates: string[], rentalType: 'daily' | 'weekly') => void;
}

const DatePickerModal: React.FC<DatePickerModalProps> = ({
  show,
  onClose,
  booking,
  onSave
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="text-xl font-bold">Select Rental Dates</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-4">
          <Suspense fallback={
            <div className="flex items-center justify-center p-12">
              <Loader className="animate-spin text-blue-600 mr-2" />
              <span>Loading calendar...</span>
            </div>
          }>
            <RentalPeriod
              rentalPeriod={{
                dates: booking.rentalPeriod.dates || [],
                days: booking.rentalPeriod.days,
                rentalType: booking.rentalPeriod.rentalType || 'daily'
              }}
              onRentalPeriodSubmit={(rentalPeriod) => {
                onSave(rentalPeriod.dates, rentalPeriod.rentalType);
                onClose();
              }}
              onBack={onClose}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default DatePickerModal;
