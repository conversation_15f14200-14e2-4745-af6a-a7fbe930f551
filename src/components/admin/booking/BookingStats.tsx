import React from 'react';
import { FileText, Check, DollarSign, AlertCircle, Clock } from 'lucide-react';
import { Booking } from '../../../types';

interface BookingStatsProps {
  bookings: Booking[];
  getPaymentStatus: (booking: Booking) => string;
  totalBookingAmount?: number;
  totalPaidAmount?: number;
  totalPendingAmount?: number;
  formatCurrency?: (amount: number) => string;
}

const BookingStats: React.FC<BookingStatsProps> = ({ 
  bookings, 
  getPaymentStatus,
  totalBookingAmount = 0,
  totalPaidAmount = 0,
  totalPendingAmount = 0,
  formatCurrency = (amount) => `BHD ${amount.toFixed(3)}`
}) => {
  const paidBookings = bookings.filter(booking => getPaymentStatus(booking) === 'paid').length;
  const pendingPayments = bookings.filter(booking => getPaymentStatus(booking) !== 'paid').length;
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Payment Progress */}
      <div className="h-1.5 w-full bg-gray-200">
        <div 
          className="h-full bg-green-500" 
          style={{ width: `${totalBookingAmount > 0 ? (totalPaidAmount / totalBookingAmount) * 100 : 0}%` }}
        ></div>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6">
        {/* First Row (Mobile) / First Three Columns (Desktop) */}
        <div className="col-span-2 sm:col-span-3 grid grid-cols-2 sm:grid-cols-3 divide-x divide-gray-200">
          {/* Total Booking Amount */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <DollarSign size={14} className="text-blue-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Total Booking</p>
            </div>
            <p className="text-lg font-bold" title={formatCurrency(totalBookingAmount)}>
              {formatCurrency(totalBookingAmount)}
            </p>
          </div>
          
          {/* Paid Amount */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-green-100 rounded-full flex items-center justify-center mr-2">
                <Check size={14} className="text-green-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Paid</p>
            </div>
            <p className="text-lg font-bold text-green-600" title={formatCurrency(totalPaidAmount)}>
              {formatCurrency(totalPaidAmount)}
            </p>
          </div>
          
          {/* Pending Amount */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-red-100 rounded-full flex items-center justify-center mr-2">
                <AlertCircle size={14} className="text-red-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Pending</p>
            </div>
            <p className="text-lg font-bold text-red-600" title={formatCurrency(totalPendingAmount)}>
              {formatCurrency(totalPendingAmount)}
            </p>
          </div>
        </div>
        
        {/* Second Row (Mobile) / Last Three Columns (Desktop) */}
        <div className="col-span-2 sm:col-span-3 grid grid-cols-3 divide-x divide-gray-200 border-t sm:border-t-0 sm:border-l border-gray-200">
          {/* Total Bookings */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <FileText size={14} className="text-blue-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Total Bookings</p>
            </div>
            <p className="text-lg font-bold">{bookings.length}</p>
          </div>
          
          {/* Fully Paid */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-green-100 rounded-full flex items-center justify-center mr-2">
                <Check size={14} className="text-green-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Fully Paid</p>
            </div>
            <p className="text-lg font-bold">{paidBookings}</p>
          </div>
          
          {/* Pending Payments */}
          <div className="p-3">
            <div className="flex items-center mb-1">
              <div className="w-6 h-6 flex-shrink-0 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                <Clock size={14} className="text-yellow-600" />
              </div>
              <p className="text-xs text-gray-500 font-medium">Pending</p>
            </div>
            <p className="text-lg font-bold">{pendingPayments}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingStats;