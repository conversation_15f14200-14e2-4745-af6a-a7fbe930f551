import React, { useState } from 'react';
import { FileText, Check, X, ChevronDown, ChevronUp, Calendar, User, Download, Plus, Trash2, DollarSign, CreditCard, AlertCircle, Truck, Edit, Building } from 'lucide-react';
import { Booking, Payment, Product, SystemSettings, DeliveryOption } from '../../../types';
import { generatePDF } from '../../../services/pdfGenerator';
import { updateBooking } from '../../../services/database';
import { formatCurrency } from '../../../utils';
import BookingItems from './BookingItems';

const BookingList: React.FC<BookingListProps> = ({
  bookings,
  products,
  systemSettings,
  onBookingsUpdate,
  expandedBookingId,
  setExpandedBookingId,
  onDeleteBooking,
  onAddPayment,
  onEditPayment,
  onDeletePayment,
  formatDate,
  getPaymentStatus,
  getPaymentStatusBadgeClass,
  calculatePaymentProgress,
  onStatusChange
}) => {
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  
  // Payment related state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentBookingId, setPaymentBookingId] = useState<string | null>(null);
  const [editingPaymentId, setEditingPaymentId] = useState<string | null>(null);
  const [newPayment, setNewPayment] = useState<{
    amount: string;
    method: 'cash' | 'card' | 'bank_transfer';
    reference: string;
    notes: string;
  }>({
    amount: '',
    method: 'cash',
    reference: '',
    notes: ''
  });

  // Calculate item total based on rental type and custom days
  // ... existing functions ...

  // Handle adding a payment
  const handleAddPayment = (bookingId: string) => {
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: '',
      method: 'cash',
      reference: '',
      notes: ''
    });
    setEditingPaymentId(null);
    setShowPaymentModal(true);
  };

  // Handle editing a payment
  const handleEditPayment = (bookingId: string, paymentId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;
    
    const payment = booking.payments.find(p => p.id === paymentId);
    if (!payment) return;
    
    // Populate the payment form with existing data
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: payment.amount.toString(),
      method: payment.method as 'cash' | 'card' | 'bank_transfer',
      reference: payment.reference || '',
      notes: payment.notes || ''
    });
    
    // Set editing state with payment ID
    setEditingPaymentId(paymentId);
    setShowPaymentModal(true);
  };

  // Handle submitting payment
  const handleSubmitPayment = () => {
    if (!paymentBookingId) return;
    
    const booking = bookings.find(b => b.id === paymentBookingId);
    if (!booking) return;
    
    // Validate amount
    const amount = parseFloat(newPayment.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid payment amount');
      return;
    }

    let updatedBooking;
    
    if (editingPaymentId) {
      // Editing existing payment
      updatedBooking = {
        ...booking,
        payments: (booking.payments || []).map(payment => {
          if (payment.id === editingPaymentId) {
            return {
              ...payment,
              amount: amount,
              method: newPayment.method,
              reference: newPayment.reference || undefined,
              notes: newPayment.notes || undefined
            };
          }
          return payment;
        })
      };
    } else {
      // Adding new payment
      const paymentData: Payment = {
        id: Date.now().toString(),
        bookingId: paymentBookingId,
        amount: amount,
        transactionId: `TXN-${Date.now()}`,
        date: new Date().toISOString(),
        method: newPayment.method,
        status: 'completed',
        reference: newPayment.reference || undefined,
        notes: newPayment.notes || undefined
      };

      updatedBooking = {
        ...booking,
        payments: [...(booking.payments || []), paymentData]
      };
    }

    // Update the booking in the database
    updateBooking(updatedBooking).then(() => {
      // Update the local state
      const updatedBookings = bookings.map(b => 
        b.id === paymentBookingId ? updatedBooking : b
      );
      onBookingsUpdate(updatedBookings);
      setShowPaymentModal(false);
      setPaymentBookingId(null);
      setEditingPaymentId(null);
    }).catch((error: Error) => {
      console.error('Error updating payment:', error);
      alert(`Failed to update payment: ${error.message}`);
    });
  };

  // ... existing functions and rendering logic ...

  return (
    <div>
      {/* ... existing components ... */}

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold">{editingPaymentId ? 'Edit Payment' : 'Add Payment'}</h2>
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setEditingPaymentId(null);
                }}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              {/* Amount */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Payment Amount (BHD)*
                </label>
                <div className="relative">
                  <DollarSign size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="number"
                    value={newPayment.amount}
                    onChange={(e) => setNewPayment({...newPayment, amount: e.target.value})}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
                    placeholder="0.000"
                    min="0"
                    step="0.001"
                  />
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Payment Method
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setNewPayment({...newPayment, method: 'cash'})}
                    className={`flex items-center justify-center p-3 rounded-md ${
                      newPayment.method === 'cash' 
                        ? 'bg-green-100 text-green-800 ring-1 ring-green-300' 
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    <DollarSign size={18} className="mr-1" />
                    Cash
                  </button>
                  <button
                    onClick={() => setNewPayment({...newPayment, method: 'card'})}
                    className={`flex items-center justify-center p-3 rounded-md ${
                      newPayment.method === 'card' 
                        ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-300' 
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    <CreditCard size={18} className="mr-1" />
                    Card
                  </button>
                  <button
                    onClick={() => setNewPayment({...newPayment, method: 'bank_transfer'})}
                    className={`flex items-center justify-center p-3 rounded-md ${
                      newPayment.method === 'bank_transfer' 
                        ? 'bg-purple-100 text-purple-800 ring-1 ring-purple-300' 
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    <Building size={18} className="mr-1" />
                    Bank
                  </button>
                </div>
              </div>

              {/* Reference */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Reference / Transaction ID
                </label>
                <input
                  type="text"
                  value={newPayment.reference}
                  onChange={(e) => setNewPayment({...newPayment, reference: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Enter reference or transaction ID"
                />
              </div>

              {/* Notes */}
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  Notes
                </label>
                <textarea
                  value={newPayment.notes}
                  onChange={(e) => setNewPayment({...newPayment, notes: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={3}
                  placeholder="Add any additional notes"
                />
              </div>
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setEditingPaymentId(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitPayment}
                disabled={!newPayment.amount || parseFloat(newPayment.amount) <= 0}
                className={`px-4 py-2 rounded-md ${
                  newPayment.amount && parseFloat(newPayment.amount) > 0
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {editingPaymentId ? 'Update Payment' : 'Add Payment'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingList; 