import React, { useState, useEffect } from 'react';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Info,
  X,
  Clock,
  User,
  Phone,
  Mail,
  FileText,
  Package
} from 'lucide-react';
import { Booking, Product, SystemSettings } from '../../types';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, parseISO, getDaysInMonth, addDays, getDay } from 'date-fns';
import { formatDateWithOrdinal } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils';

interface CalendarViewProps {
  bookings: Booking[];
  products: Product[];
  systemSettings: SystemSettings;
  onBookingSelect: (booking: Booking) => void;
}

const CalendarView: React.FC<CalendarViewProps> = ({
  bookings,
  products,
  systemSettings,
  onBookingSelect
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [hoveredBooking, setHoveredBooking] = useState<Booking | null>(null);
  const [view, setView] = useState<'month' | 'week' | 'agenda'>('month');
  const [bookingsByDate, setBookingsByDate] = useState<{[date: string]: Booking[]}>({});

  // Process the bookings to index them by date
  useEffect(() => {
    const byDate: {[date: string]: Booking[]} = {};

    bookings.forEach(booking => {
      if (booking.rentalPeriod && booking.rentalPeriod.dates && booking.rentalPeriod.dates.length > 0) {
        booking.rentalPeriod.dates.forEach(dateStr => {
          const dateKey = dateStr.split('T')[0];
          if (!byDate[dateKey]) {
            byDate[dateKey] = [];
          }
          // Only add the booking once to each date
          if (!byDate[dateKey].some(b => b.id === booking.id)) {
            byDate[dateKey].push(booking);
          }
        });
      }
    });

    setBookingsByDate(byDate);
  }, [bookings]);

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const goToToday = () => {
    setCurrentMonth(new Date());
    setSelectedDate(new Date());
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const handleBookingClick = (booking: Booking) => {
    setSelectedBooking(booking);
    if (onBookingSelect) {
      onBookingSelect(booking);
    }
  };

  const closeBookingDetails = () => {
    setSelectedBooking(null);
  };

  const getBookingsForDate = (date: Date): Booking[] => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return bookingsByDate[dateKey] || [];
  };

  // Add functions for week navigation
  const nextWeek = () => {
    if (selectedDate) {
      setSelectedDate(addDays(selectedDate, 7));
    } else {
      setSelectedDate(addDays(new Date(), 7));
    }
  };

  const prevWeek = () => {
    if (selectedDate) {
      setSelectedDate(addDays(selectedDate, -7));
    } else {
      setSelectedDate(addDays(new Date(), -7));
    }
  };

  const renderCalendarHeader = () => {
    return (
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center">
          <h2 className="text-xl md:text-2xl font-bold mr-2 md:mr-4">{format(currentMonth, 'MMMM yyyy')}</h2>
          <div className="flex space-x-1">
            {/* Show different navigation buttons based on view */}
            {view === 'month' && (
              <>
                <button
                  onClick={prevMonth}
                  className="p-2 rounded-full hover:bg-gray-100"
                  aria-label="Previous month"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={nextMonth}
                  className="p-2 rounded-full hover:bg-gray-100"
                  aria-label="Next month"
                >
                  <ChevronRight size={20} />
                </button>
              </>
            )}

            {(view === 'week' || view === 'agenda') && (
              <>
                <button
                  onClick={prevWeek}
                  className="p-2 rounded-full hover:bg-gray-100"
                  aria-label="Previous week"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={nextWeek}
                  className="p-2 rounded-full hover:bg-gray-100"
                  aria-label="Next week"
                >
                  <ChevronRight size={20} />
                </button>
              </>
            )}

            <button
              onClick={goToToday}
              className="ml-2 px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
            >
              Today
            </button>
          </div>
        </div>
        <div className="flex space-x-2 w-full md:w-auto justify-center md:justify-end">
          <button
            onClick={() => setView('month')}
            className={`px-3 py-1 rounded-md text-sm ${view === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
          >
            Month
          </button>
          <button
            onClick={() => setView('week')}
            className={`px-3 py-1 rounded-md text-sm ${view === 'week' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
          >
            Week
          </button>
          <button
            onClick={() => setView('agenda')}
            className={`px-3 py-1 rounded-md text-sm ${view === 'agenda' ? 'bg-blue-600 text-white' : 'bg-gray-100'}`}
          >
            Agenda
          </button>
        </div>
      </div>
    );
  };

  const renderMonthView = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const startOfMonthDay = getDay(startOfMonth(currentMonth)); // 0-6, 0 is Sunday
    const daysFromPrevMonth = startOfMonthDay;
    const days = [];
    const monthStart = startOfMonth(currentMonth);

    // Add days from previous month
    for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
      const day = subMonths(addDays(monthStart, -i), 1);
      days.push(day);
    }

    // Add days of current month
    for (let i = 1; i <= daysInMonth; i++) {
      const day = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), i);
      days.push(day);
    }

    // Add days from next month to fill grid
    const daysToAdd = 42 - days.length; // 6 rows * 7 columns = 42 cells
    for (let i = 1; i <= daysToAdd; i++) {
      const day = addDays(endOfMonth(currentMonth), i);
      days.push(day);
    }

    return (
      <div className="mb-6">
        <div className="grid grid-cols-7 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="text-sm font-medium text-gray-500 text-center py-2">
              {day}
            </div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {days.map((day, index) => {
            const dateBookings = getBookingsForDate(day);
            const isToday = isSameDay(day, new Date());
            const isSelected = selectedDate && isSameDay(day, selectedDate);
            const isCurrentMonth = day.getMonth() === currentMonth.getMonth();

            return (
              <div
                key={index}
                className={`min-h-32 bg-white p-1 relative ${
                  !isCurrentMonth ? 'text-gray-400' : ''
                } ${isToday ? 'bg-blue-50' : ''} ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => handleDateClick(day)}
              >
                <div className="flex justify-between items-start">
                  <span className={`text-sm font-medium ${isToday ? 'bg-blue-500 text-white w-6 h-6 flex items-center justify-center rounded-full' : ''}`}>
                    {format(day, 'd')}
                  </span>
                  {dateBookings.length > 0 && (
                    <span className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full">
                      {dateBookings.length}
                    </span>
                  )}
                </div>
                <div className="mt-1 overflow-y-auto max-h-28">
                  {dateBookings.slice(0, 3).map((booking) => (
                    <div
                      key={booking.id}
                      className="text-xs p-1 mb-1 rounded bg-blue-50 hover:bg-blue-100 cursor-pointer truncate"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleBookingClick(booking);
                      }}
                      onMouseEnter={() => setHoveredBooking(booking)}
                      onMouseLeave={() => setHoveredBooking(null)}
                    >
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                        <span className="truncate">{booking.quoteNumber}: {booking.customer.name}</span>
                      </div>
                    </div>
                  ))}
                  {dateBookings.length > 3 && (
                    <div className="text-xs text-blue-600 p-1 cursor-pointer">
                      + {dateBookings.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    // Calculate the start of the week (Sunday) for the current selected date or today
    const baseDate = selectedDate || new Date();
    const dayOfWeek = getDay(baseDate);
    const startOfWeek = addDays(baseDate, -dayOfWeek);

    // Update current month to match the week view for proper header display
    if (startOfWeek.getMonth() !== currentMonth.getMonth()) {
      setCurrentMonth(new Date(startOfWeek));
    }

    // Generate the 7 days of the week
    const days = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek, i));

    return (
      <div className="mb-6">
        <div className="flex justify-between mb-2">
          {days.map((day, index) => (
            <div key={index} className="text-center px-1 flex-1 min-w-0">
              <div className="text-xs sm:text-sm font-medium text-gray-500 truncate">
                {format(day, 'EEE')}
              </div>
              <div
                className={`text-base sm:text-lg font-bold mt-1 w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center mx-auto rounded-full ${
                  isSameDay(day, new Date()) ? 'bg-blue-500 text-white' : ''
                } ${selectedDate && isSameDay(day, selectedDate) ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => handleDateClick(day)}
              >
                {format(day, 'd')}
              </div>
            </div>
          ))}
        </div>

        <div className="border rounded-lg overflow-hidden mt-4">
          {days.map((day, dayIndex) => {
            const dateBookings = getBookingsForDate(day);
            return (
              <div key={dayIndex} className="border-b last:border-b-0">
                <div className="bg-gray-50 px-2 sm:px-4 py-2 flex items-center">
                  <div className={`font-medium text-sm sm:text-base truncate ${isSameDay(day, new Date()) ? 'text-blue-600' : 'text-gray-700'}`}>
                    {format(day, 'EEE, MMM d')}
                  </div>
                </div>
                {dateBookings.length === 0 ? (
                  <div className="px-2 sm:px-4 py-4 sm:py-6 text-center text-gray-500 text-sm sm:text-base">
                    No bookings for this day
                  </div>
                ) : (
                  <div>
                    {dateBookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="px-2 sm:px-4 py-2 sm:py-3 hover:bg-gray-50 cursor-pointer border-t"
                        onClick={() => handleBookingClick(booking)}
                      >
                        <div className="flex items-start">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 mr-1.5 sm:mt-2 sm:mr-2 flex-shrink-0"></div>
                          <div className="flex-grow min-w-0">
                            <div className="font-medium text-sm sm:text-base truncate">{booking.quoteNumber}</div>
                            <div className="text-xs sm:text-sm text-gray-600 flex items-center mt-0.5 sm:mt-1">
                              <User size={12} className="mr-1 flex-shrink-0" />
                              <span className="truncate">{booking.customer.name}</span>
                            </div>
                            <div className="flex items-center justify-between mt-0.5 sm:mt-1">
                              <div className="text-xs sm:text-sm text-gray-500">
                                {booking.products.reduce((total, p) => total + p.quantity, 0)} items
                              </div>
                              <div className="font-medium text-xs sm:text-sm text-blue-600">
                                {formatCurrency(booking.total)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderAgendaView = () => {
    // If we have a selected date, use it to filter the agenda view
    // This allows the prev/next week buttons to work with the agenda view
    const baseDate = selectedDate || new Date();
    const dayOfWeek = getDay(baseDate);
    const startOfWeek = addDays(baseDate, -dayOfWeek);
    const endOfWeek = addDays(startOfWeek, 6);

    // Update current month to match the week view for proper header display
    if (startOfWeek.getMonth() !== currentMonth.getMonth()) {
      setCurrentMonth(new Date(startOfWeek));
    }

    // Group bookings by month and day for the agenda view
    const months: { [month: string]: { [day: string]: Booking[] } } = {};

    // Fill in all bookings
    Object.entries(bookingsByDate).forEach(([dateStr, dayBookings]) => {
      const date = parseISO(dateStr);

      // Only include dates in the current week when a date is selected
      if (selectedDate && (date < startOfWeek || date > endOfWeek)) {
        return;
      }

      const monthKey = format(date, 'MMMM yyyy');
      const dayKey = format(date, 'd');

      if (!months[monthKey]) {
        months[monthKey] = {};
      }

      if (!months[monthKey][dayKey]) {
        months[monthKey][dayKey] = [];
      }

      dayBookings.forEach(booking => {
        if (!months[monthKey][dayKey].some(b => b.id === booking.id)) {
          months[monthKey][dayKey].push(booking);
        }
      });
    });

    return (
      <div className="space-y-6">
        {Object.entries(months).map(([month, days]) => (
          <div key={month} className="border rounded-lg overflow-hidden">
            <div className="bg-gray-100 px-4 py-2 font-bold">
              {month}
            </div>
            {Object.entries(days).map(([day, dayBookings]) => (
              <div key={`${month}-${day}`} className="border-t">
                <div className="px-4 py-2 bg-gray-50 font-medium">
                  {formatDateWithOrdinal(new Date(`${month.split(' ')[0]} ${day}, ${month.split(' ')[1]}`))}
                </div>
                <div>
                  {dayBookings.map((booking) => (
                    <div
                      key={booking.id}
                      className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-t"
                      onClick={() => handleBookingClick(booking)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium text-blue-600">{booking.quoteNumber}</div>
                          <div className="flex items-center mt-1">
                            <User size={14} className="mr-1 text-gray-500" />
                            <span className="text-sm text-gray-700">{booking.customer.name}</span>
                          </div>
                          <div className="flex items-center mt-1">
                            <Package size={14} className="mr-1 text-gray-500" />
                            <span className="text-sm text-gray-600">
                              {booking.products.reduce((total, p) => total + p.quantity, 0)} items
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(booking.total)}</div>
                          <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                            booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                            booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  };

  const renderHoveredBookingDetails = () => {
    if (!hoveredBooking) return null;

    return (
      <div className="absolute z-10 bg-white shadow-lg rounded-lg p-4 w-80 border border-gray-200">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-bold text-lg">{hoveredBooking.quoteNumber}</h3>
          <span className={`text-xs px-2 py-1 rounded-full ${
            hoveredBooking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
            hoveredBooking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
            hoveredBooking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
            'bg-blue-100 text-blue-800'
          }`}>
            {hoveredBooking.status.charAt(0).toUpperCase() + hoveredBooking.status.slice(1)}
          </span>
        </div>
        <div className="mb-2">
          <div className="flex items-center">
            <User size={14} className="mr-2 text-gray-500" />
            <span className="text-sm">{hoveredBooking.customer.name}</span>
          </div>
          <div className="flex items-center mt-1">
            <Mail size={14} className="mr-2 text-gray-500" />
            <span className="text-sm">{hoveredBooking.customer.email}</span>
          </div>
          {hoveredBooking.customer.phone && (
            <div className="flex items-center mt-1">
              <Phone size={14} className="mr-2 text-gray-500" />
              <span className="text-sm">{hoveredBooking.customer.phone}</span>
            </div>
          )}
        </div>
        <div className="border-t border-gray-200 pt-2 mt-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Total:</span>
            <span className="font-bold">{formatCurrency(hoveredBooking.total)}</span>
          </div>
          <div className="flex items-center justify-between mt-1">
            <span className="text-sm text-gray-600">Rental Period:</span>
            <span className="text-sm">
              {hoveredBooking.rentalPeriod.days} {hoveredBooking.rentalPeriod.days === 1 ? 'day' : 'days'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  const renderBookingDetailModal = () => {
    if (!selectedBooking) return null;

    return (
      <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="booking-detail-modal">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={closeBookingDetails}></div>

          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                      Booking Details - {selectedBooking.quoteNumber}
                    </h3>
                    <button
                      onClick={closeBookingDetails}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <X size={20} />
                    </button>
                  </div>

                  <div className="mt-4 space-y-4">
                    {/* Status Badge */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Status</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        selectedBooking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        selectedBooking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        selectedBooking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      } capitalize`}>
                        {selectedBooking.status}
                      </span>
                    </div>

                    {/* Customer Info */}
                    <div className="bg-gray-50 p-3 rounded">
                      <h4 className="font-medium mb-2">Customer Information</h4>
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <User size={16} className="mr-2 text-gray-500" />
                          <span>{selectedBooking.customer.name}</span>
                        </div>
                        <div className="flex items-center">
                          <Mail size={16} className="mr-2 text-gray-500" />
                          <span>{selectedBooking.customer.email}</span>
                        </div>
                        {selectedBooking.customer.phone && (
                          <div className="flex items-center">
                            <Phone size={16} className="mr-2 text-gray-500" />
                            <span>{selectedBooking.customer.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Rental Period */}
                    <div>
                      <h4 className="font-medium mb-2">Rental Period</h4>
                      <div className="flex items-center">
                        <Clock size={16} className="mr-2 text-gray-500" />
                        <span>
                          {selectedBooking.rentalPeriod.days} {selectedBooking.rentalPeriod.days === 1 ? 'day' : 'days'}
                          ({selectedBooking.rentalPeriod.rentalType === 'weekly' ? 'Weekly Rate' : 'Daily Rate'})
                        </span>
                      </div>
                      <div className="mt-2">
                        <div className="text-sm text-gray-600 mb-1">Selected Dates:</div>
                        <div className="bg-blue-50 p-2 rounded text-sm">
                          {selectedBooking.rentalPeriod.dates && selectedBooking.rentalPeriod.dates.length > 0 ? (
                            selectedBooking.rentalPeriod.dates.map(date => formatDateWithOrdinal(date)).join(', ')
                          ) : (
                            'No dates specified'
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Booked Items */}
                    <div>
                      <h4 className="font-medium mb-2">Booked Items</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {selectedBooking.products.map(product => (
                          <div key={product.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <div>
                              <span className="font-medium">{product.quantity}x</span> {product.name}
                            </div>
                            <span className="text-gray-600">
                              {formatCurrency((product.dailyRate || 0) * product.quantity)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Payment Info */}
                    <div>
                      <h4 className="font-medium mb-2">Payment Information</h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm text-gray-600">Subtotal:</div>
                        <div className="text-right">{formatCurrency(selectedBooking.subtotal || 0)}</div>

                        {selectedBooking.deliveryFee && selectedBooking.deliveryFee > 0 && (
                          <>
                            <div className="text-sm text-gray-600">Delivery:</div>
                            <div className="text-right">{formatCurrency(selectedBooking.deliveryFee)}</div>
                          </>
                        )}

                        {selectedBooking.discount && selectedBooking.discount > 0 && (
                          <>
                            <div className="text-sm text-gray-600">Discount:</div>
                            <div className="text-right text-green-600">-{formatCurrency(selectedBooking.discount)}</div>
                          </>
                        )}

                        {selectedBooking.tax && selectedBooking.tax > 0 && (
                          <>
                            <div className="text-sm text-gray-600">VAT ({systemSettings.taxRate}%):</div>
                            <div className="text-right">{formatCurrency(selectedBooking.tax)}</div>
                          </>
                        )}

                        <div className="text-sm font-medium border-t pt-1">Total:</div>
                        <div className="text-right font-bold border-t pt-1">{formatCurrency(selectedBooking.total)}</div>

                        {selectedBooking.payments && selectedBooking.payments.length > 0 && (
                          <>
                            <div className="text-sm text-green-600">Paid:</div>
                            <div className="text-right text-green-600">
                              {formatCurrency(
                                selectedBooking.payments.reduce((total, payment) => total + payment.amount, 0)
                              )}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={() => {
                  if (onBookingSelect) {
                    onBookingSelect(selectedBooking);
                  }
                  closeBookingDetails();
                }}
              >
                View Full Details
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={closeBookingDetails}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Legend for color coding
  const renderLegend = () => (
    <div className="mb-6 flex items-center space-x-4 text-sm text-gray-600">
      <div className="flex items-center">
        <span className="w-3 h-3 bg-blue-500 rounded-full inline-block mr-1"></span>
        <span>Confirmed</span>
      </div>
      <div className="flex items-center">
        <span className="w-3 h-3 bg-yellow-500 rounded-full inline-block mr-1"></span>
        <span>Pending</span>
      </div>
      <div className="flex items-center">
        <span className="w-3 h-3 bg-green-500 rounded-full inline-block mr-1"></span>
        <span>Completed</span>
      </div>
      <div className="flex items-center">
        <span className="w-3 h-3 bg-red-500 rounded-full inline-block mr-1"></span>
        <span>Cancelled</span>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow p-4 md:p-6 overflow-hidden relative">
      {renderCalendarHeader()}
      {renderLegend()}

      <div className="overflow-x-auto">
        <div className="min-w-[300px]">
          {view === 'month' && renderMonthView()}
          {view === 'week' && renderWeekView()}
          {view === 'agenda' && renderAgendaView()}
        </div>
      </div>

      {hoveredBooking && renderHoveredBookingDetails()}
      {renderBookingDetailModal()}
    </div>
  );
};

export default CalendarView;