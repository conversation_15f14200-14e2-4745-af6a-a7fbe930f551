import React, { useState, useRef, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Save, Image, Star, Minus, Search, Filter, Download, ChevronLeft, ChevronRight, Upload, AlertCircle } from 'lucide-react';
import { Product, Vendor } from '../../types';
import { generateCatalog } from '../../services/catalogGenerator';
import { addProduct, deleteProduct, updateProduct, getVendors } from '../../services/database';

interface ProductFormData {
  name: string;
  category: string;
  sku: string;
  barcode?: string;
  dailyRate: number;
  weeklyRate: number;
  description: string;
  image: string;
  available: boolean;
  stock: number;
  quantity: number;
  featured: boolean;
  isExternalVendorItem?: boolean;
  vendorId?: string;
  vendorSku?: string;
  vendorCost?: number;
  profitMargin?: number;
}

interface ProductManagementProps {
  products: Product[];
  categories: string[];
  onProductsUpdate: (products: Product[]) => void;
}

const ProductManagement: React.FC<ProductManagementProps> = ({ products, categories, onProductsUpdate }) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [newProduct, setNewProduct] = useState<ProductFormData>({
    name: '',
    category: '',
    sku: '',
    barcode: '',
    dailyRate: 0,
    weeklyRate: 0,
    description: '',
    image: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
    available: true,
    stock: 1,
    quantity: 1,
    featured: false
  });
  const [customQuantity, setCustomQuantity] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showCatalogModal, setShowCatalogModal] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importErrors, setImportErrors] = useState<string[]>([]);
  const [showImportModal, setShowImportModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const itemsPerPage = 15;
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loadingVendors, setLoadingVendors] = useState(false);

  // Load vendors when component mounts
  useEffect(() => {
    const loadVendors = async () => {
      setLoadingVendors(true);
      try {
        const fetchedVendors = await getVendors();
        setVendors(fetchedVendors);
      } catch (error) {
        console.error('Error loading vendors:', error);
      } finally {
        setLoadingVendors(false);
      }
    };
    
    loadVendors();
  }, []);

  // Calculate profit margin when vendor cost or daily rate changes
  useEffect(() => {
    if (newProduct.isExternalVendorItem && newProduct.vendorCost && newProduct.dailyRate) {
      const cost = newProduct.vendorCost;
      const price = newProduct.dailyRate;
      const profitMargin = ((price - cost) / price) * 100;
      
      setNewProduct({
        ...newProduct,
        profitMargin: Math.round(profitMargin * 100) / 100 // Round to 2 decimal places
      });
    }
  }, [newProduct.vendorCost, newProduct.dailyRate, newProduct.isExternalVendorItem]);

  // Update vendor cost when profit margin changes
  const handleProfitMarginChange = (margin: number) => {
    if (newProduct.dailyRate) {
      const price = newProduct.dailyRate;
      const cost = price * (1 - margin / 100);
      
      setNewProduct({
        ...newProduct,
        profitMargin: margin,
        vendorCost: Math.round(cost * 100) / 100 // Round to 2 decimal places
      });
    }
  };

  // Filter products based on search term and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  // Get paginated products
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Calculate total pages
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  // Handle catalog download
  const handleDownloadCatalog = async (category?: string) => {
    try {
      await generateCatalog(products, category);
      setShowCatalogModal(false);
    } catch (error) {
      console.error('Error generating catalog:', error);
      alert('Error generating catalog. Please try again.');
    }
  };

  const handleAddProduct = async () => {
    if (!newProduct.name || !newProduct.category || !newProduct.sku || newProduct.dailyRate <= 0) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      // Calculate weekly rate as 5x daily rate
      const weeklyRate = newProduct.dailyRate * 5;

      const productToAdd = {
        ...newProduct,
        weeklyRate
      };

      // Add product to Firebase
      const newProductId = await addProduct(productToAdd);
      
      // Update local state with the new product including the Firebase ID
      const updatedProduct = { ...productToAdd, id: newProductId };
      onProductsUpdate([...products, updatedProduct]);
      
      // Reset form
      setNewProduct({
        name: '',
        category: '',
        sku: '',
        barcode: '',
        dailyRate: 0,
        weeklyRate: 0,
        description: '',
        image: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
        available: true,
        stock: 1,
        quantity: 1,
        featured: false
      });
      setShowAddForm(false);
      setCustomQuantity(false);
    } catch (error) {
      console.error('Error adding product:', error);
      alert(error instanceof Error ? error.message : 'Failed to add product. Please try again.');
    }
  };

  const handleEditProduct = (product: Product) => {
    setEditingProductId(product.id);
    setNewProduct({
      name: product.name,
      category: product.category,
      sku: product.sku,
      barcode: product.barcode,
      dailyRate: product.dailyRate,
      weeklyRate: product.weeklyRate || product.dailyRate * 5,
      description: product.description,
      image: product.image,
      available: product.available,
      stock: product.stock,
      quantity: product.quantity,
      featured: product.featured || false
    });
  };

  const handleUpdateProduct = async () => {
    if (!newProduct.name || !newProduct.category || !newProduct.image || newProduct.dailyRate <= 0) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      if (!editingProductId) {
        throw new Error('No product selected for editing');
      }

      const updatedProduct = {
        ...newProduct,
        id: editingProductId
      };

      await updateProduct(updatedProduct);
      
      const updatedProducts = products.map(product => 
        product.id === editingProductId ? updatedProduct : product
      );
      
      onProductsUpdate(updatedProducts);
      setEditingProductId(null);
      setNewProduct({
        name: '',
        category: '',
        sku: '',
        barcode: '',
        dailyRate: 0,
        weeklyRate: 0,
        description: '',
        image: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
        available: true,
        stock: 1,
        quantity: 1,
        featured: false
      });
    } catch (error) {
      console.error('Error updating product:', error);
      alert('Failed to update product. Please try again.');
    }
  };

  const handleDeleteProduct = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        console.log('Attempting to delete product:', id);
        await deleteProduct(id);
        console.log('Product deleted successfully, updating local state');
        const updatedProducts = products.filter(product => product.id !== id);
        onProductsUpdate(updatedProducts);
      } catch (error) {
        console.error('Error deleting product:', error);
        alert(error instanceof Error ? error.message : 'Failed to delete product. Please try again.');
      }
    }
  };

  const handleCancelEdit = () => {
    setEditingProductId(null);
    setNewProduct({
      name: '',
      category: '',
      sku: '',
      barcode: '',
      dailyRate: 0,
      weeklyRate: 0,
      description: '',
      image: 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
      available: true,
      stock: 1,
      quantity: 1,
      featured: false
    });
  };

  const handleToggleFeatured = async (id: string) => {
    try {
      const product = products.find(p => p.id === id);
      if (!product) {
        throw new Error('Product not found');
      }

      const updatedProduct = {
        ...product,
        featured: !product.featured
      };

      await updateProduct(updatedProduct);
      
      const updatedProducts = products.map(p => 
        p.id === id ? updatedProduct : p
      );
      
      onProductsUpdate(updatedProducts);
    } catch (error) {
      console.error('Error toggling featured status:', error);
      alert('Failed to update featured status. Please try again.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Export products to CSV
  const handleExportCSV = () => {
    // Define CSV headers
    const headers = [
      'SKU',
      'Name',
      'Category',
      'Description',
      'Daily Rate',
      'Weekly Rate',
      'Barcode',
      'Image URL',
      'Stock',
      'Available',
      'Featured'
    ].join(',');

    // Convert products to CSV rows
    const rows = products.map(product => {
      return [
        `"${product.sku}"`,
        `"${product.name.replace(/"/g, '""')}"`,
        `"${product.category}"`,
        `"${(product.description || '').replace(/"/g, '""')}"`,
        product.dailyRate,
        product.weeklyRate || product.dailyRate * 5,
        `"${product.barcode || ''}"`,
        `"${product.image}"`,
        product.stock,
        product.available ? 'Yes' : 'No',
        product.featured ? 'Yes' : 'No'
      ].join(',');
    });

    // Combine headers and rows
    const csv = [headers, ...rows].join('\n');

    // Create and download the file
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `products_export_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Trigger file input click
  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Process the imported CSV file
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportErrors([]);
    setShowImportModal(true);

    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const csvData = event.target?.result as string;
        const results = parseCSV(csvData);
        
        if (results.length === 0) {
          setImportErrors(['No valid data found in CSV file']);
          setIsImporting(false);
          return;
        }

        await processCSVData(results);
        
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } catch (error) {
        console.error('Error importing CSV:', error);
        setImportErrors([`Error importing CSV: ${error instanceof Error ? error.message : 'Unknown error'}`]);
      } finally {
        setIsImporting(false);
      }
    };

    reader.onerror = () => {
      setImportErrors(['Error reading file']);
      setIsImporting(false);
    };

    reader.readAsText(file);
  };

  // Parse CSV data
  const parseCSV = (csvData: string) => {
    const lines = csvData.split(/\r?\n/);
    const headers = lines[0].split(',').map(header => 
      header.trim().replace(/^"(.*)"$/, '$1').toLowerCase()
    );
    
    const skuIndex = headers.findIndex(h => h === 'sku');
    if (skuIndex === -1) {
      setImportErrors(['CSV file must contain a "SKU" column']);
      return [];
    }

    const results: Record<string, any>[] = [];

    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;
      
      // Handle quoted values with commas inside
      const values: string[] = [];
      let insideQuotes = false;
      let currentValue = '';
      
      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        
        if (char === '"') {
          insideQuotes = !insideQuotes;
        } else if (char === ',' && !insideQuotes) {
          values.push(currentValue);
          currentValue = '';
        } else {
          currentValue += char;
        }
      }
      
      values.push(currentValue); // Add the last value
      
      // Clean up values
      const cleanValues = values.map(value => value.trim().replace(/^"(.*)"$/, '$1'));
      
      const row: Record<string, any> = {};
      headers.forEach((header, index) => {
        if (index < cleanValues.length) {
          row[header] = cleanValues[index];
        }
      });

      if (!row.sku) {
        setImportErrors(prev => [...prev, `Row ${i} is missing SKU value, skipping`]);
        continue;
      }

      results.push(row);
    }

    return results;
  };

  // Process CSV data and update products
  const processCSVData = async (csvData: Record<string, any>[]) => {
    // Convert CSV rows to product format
    const csvProducts = csvData.map(row => {
      return {
        sku: row.sku?.toString().trim() || '',
        name: row.name?.toString().trim() || '',
        category: row.category?.toString().trim() || categories[0] || '',
        description: row.description?.toString().trim() || '',
        dailyRate: parseFloat(row['daily rate'] || '0') || 0,
        weeklyRate: parseFloat(row['weekly rate'] || '0') || 0,
        barcode: row.barcode?.toString().trim() || '',
        image: row['image url']?.toString().trim() || 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png',
        stock: parseInt(row.stock || '1') || 1,
        available: row.available?.toString().toLowerCase() === 'yes',
        featured: row.featured?.toString().toLowerCase() === 'yes',
        quantity: 1
      };
    });

    // Create sets of SKUs for comparison
    const existingSkus = new Set(products.map(p => p.sku));
    const importedSkus = new Set(csvProducts.map(p => p.sku));
    
    // Arrays to track actions
    const productsToAdd: Product[] = [];
    const productsToUpdate: Product[] = [];
    const productIdsToDelete: string[] = [];
    
    // Find products to add or update
    for (const csvProduct of csvProducts) {
      if (!csvProduct.sku) continue;
      
      // Validate required fields
      if (!csvProduct.name || csvProduct.dailyRate <= 0) {
        setImportErrors(prev => [...prev, `Product with SKU ${csvProduct.sku} has missing required fields (name, dailyRate), skipping`]);
        continue;
      }

      // Check if product already exists
      const existingProduct = products.find(p => p.sku === csvProduct.sku);
      
      if (existingProduct) {
        // Update existing product
        productsToUpdate.push({
          ...existingProduct,
          name: csvProduct.name,
          category: csvProduct.category,
          description: csvProduct.description,
          dailyRate: csvProduct.dailyRate,
          weeklyRate: csvProduct.weeklyRate || csvProduct.dailyRate * 5,
          barcode: csvProduct.barcode,
          image: csvProduct.image,
          stock: csvProduct.stock,
          available: csvProduct.available,
          featured: csvProduct.featured
        });
      } else {
        // New product
        productsToAdd.push({
          ...csvProduct,
          id: '', // Will be assigned by Firebase
          weeklyRate: csvProduct.weeklyRate || csvProduct.dailyRate * 5
        });
      }
    }
    
    // Find products to delete (in existing but not in imported)
    for (const product of products) {
      if (!importedSkus.has(product.sku)) {
        productIdsToDelete.push(product.id);
      }
    }
    
    // Apply changes
    const updates: Promise<any>[] = [];
    const errors: string[] = [];
    let addedCount = 0;
    let updatedCount = 0;
    let deletedCount = 0;
    
    // Add new products
    for (const product of productsToAdd) {
      try {
        const newProductId = await addProduct(product);
        product.id = newProductId;
        addedCount++;
      } catch (error) {
        console.error(`Error adding product ${product.sku}:`, error);
        errors.push(`Failed to add product ${product.sku}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    // Update existing products
    for (const product of productsToUpdate) {
      try {
        await updateProduct(product);
        updatedCount++;
      } catch (error) {
        console.error(`Error updating product ${product.sku}:`, error);
        errors.push(`Failed to update product ${product.sku}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    // Delete products
    for (const productId of productIdsToDelete) {
      try {
        await deleteProduct(productId);
        deletedCount++;
      } catch (error) {
        console.error(`Error deleting product ${productId}:`, error);
        errors.push(`Failed to delete product ID ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    if (errors.length > 0) {
      setImportErrors(errors);
    }
    
    // Update local products state
    const newProducts = [
      ...products.filter(p => !productIdsToDelete.includes(p.id)), // Keep existing products that weren't deleted
      ...productsToAdd, // Add new products
      ...productsToUpdate // Update existing products
    ];
    
    onProductsUpdate(newProducts);
    
    // Show success message with counts
    alert(`Import complete!\nAdded: ${addedCount}\nUpdated: ${updatedCount}\nDeleted: ${deletedCount}\nErrors: ${errors.length}`);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Product Management</h2> 
        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleExportCSV}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Download size={18} className="mr-2" />
            Export CSV
          </button>
          <button
            onClick={handleImportClick}
            className="bg-purple-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Upload size={18} className="mr-2" />
            Import CSV
          </button>
          <input
            type="file"
            accept=".csv"
            onChange={handleImportCSV}
            ref={fileInputRef}
            className="hidden"
          />
          <button
            onClick={() => setShowCatalogModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Download size={18} className="mr-2" />
            Download Catalog
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Plus size={18} className="mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Import CSV Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">Import Products</h3>
              <button
                onClick={() => setShowImportModal(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            {isImporting ? (
              <div className="text-center py-6">
                <div className="spinner mb-4"></div>
                <p>Importing products, please wait...</p>
              </div>
            ) : (
              <>
                {importErrors.length > 0 ? (
                  <div className="mb-4">
                    <div className="flex items-center text-amber-700 mb-2">
                      <AlertCircle size={20} className="mr-2" />
                      <h4 className="font-medium">Issues found during import:</h4>
                    </div>
                    <div className="bg-amber-50 border border-amber-200 p-3 rounded-md max-h-60 overflow-y-auto">
                      <ul className="list-disc pl-5 space-y-1">
                        {importErrors.map((error, index) => (
                          <li key={index} className="text-sm text-amber-800">{error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 p-4 rounded-md mb-4">
                    <p className="text-green-800">
                      Import completed successfully!
                    </p>
                  </div>
                )}
                
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-md mb-4">
                  <h4 className="font-medium text-blue-800 mb-2">CSV Format Requirements:</h4>
                  <ul className="list-disc pl-5 space-y-1 text-sm text-blue-700">
                    <li>Required columns: SKU, Name, Daily Rate</li>
                    <li>Optional columns: Category, Description, Weekly Rate, Barcode, Image URL, Stock, Available, Featured</li>
                    <li>First row must be column headers</li>
                    <li>SKU must be unique for each product</li>
                  </ul>
                </div>
                
                <div className="flex justify-end mt-4">
                  <button
                    onClick={() => setShowImportModal(false)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md"
                  >
                    Close
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Catalog Download Modal */}
      {showCatalogModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">Download Catalog</h3>
              <button
                onClick={() => setShowCatalogModal(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="space-y-4">
              <button
                onClick={() => handleDownloadCatalog()}
                className="w-full p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <h4 className="font-medium mb-1">Full Catalog</h4>
                <p className="text-sm text-gray-500">Download complete catalog with all categories</p>
              </button>
              
              <div className="relative">
                <h4 className="font-medium mb-2">Select Category</h4>
                <div className="space-y-2">
                  {categories.map(category => (
                    <button
                      key={category}
                      onClick={() => handleDownloadCatalog(category)}
                      className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <span className="capitalize">{category}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search products..."
            className="pl-10 pr-4 py-3 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); // Reset to first page when searching
            }}
          />
        </div>
        
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter size={18} className="text-gray-400" />
          </div>
          <select
            className="pl-10 pr-8 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white min-w-[180px]"
            value={categoryFilter}
            onChange={(e) => {
              setCategoryFilter(e.target.value);
              setCurrentPage(1); // Reset to first page when filtering
            }}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <ChevronLeft size={18} className="text-gray-400 transform rotate-270" />
          </div>
        </div>
      </div>

      {/* Add/Edit Product Form */}
      {(showAddForm || editingProductId !== null) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-xl font-bold">
                {editingProductId !== null ? 'Edit Product' : 'Add New Product'}
              </h3>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  handleCancelEdit();
                }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X size={24} />
              </button>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Product Name*
                  </label>
                  <input
                    type="text"
                    value={newProduct.name || ''}
                    onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="Excavator - Mini"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Category*
                  </label>
                  <div className="flex">
                    <select
                      value={newProduct.category || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    SKU*
                  </label>
                  <input
                    type="text"
                    value={newProduct.sku || ''}
                    onChange={(e) => setNewProduct({ ...newProduct, sku: e.target.value.toUpperCase() })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="EXC-001"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Barcode
                  </label>
                  <input
                    type="text"
                    value={newProduct.barcode || ''}
                    onChange={(e) => setNewProduct({ ...newProduct, barcode: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="123456789 (optional)"
                  />
                </div>

                {/* External Vendor section */}
                <div className="md:col-span-2">
                  <div className="border-t border-gray-200 pt-4 mt-2 mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newProduct.isExternalVendorItem || false}
                        onChange={(e) => setNewProduct({ 
                          ...newProduct, 
                          isExternalVendorItem: e.target.checked,
                          vendorId: e.target.checked ? newProduct.vendorId : undefined,
                          vendorSku: e.target.checked ? newProduct.vendorSku : undefined,
                          vendorCost: e.target.checked ? newProduct.vendorCost : undefined,
                          profitMargin: e.target.checked ? newProduct.profitMargin : undefined
                        })}
                        className="mr-2"
                      />
                      <span className="text-gray-700 font-medium">External Vendor Item</span>
                    </label>
                    <p className="text-sm text-gray-500 mt-1">
                      Mark this product as supplied by an external vendor
                    </p>
                  </div>
                </div>

                {newProduct.isExternalVendorItem && (
                  <>
                    <div>
                      <label className="block text-gray-700 mb-2 font-medium">
                        Vendor*
                      </label>
                      <select
                        value={newProduct.vendorId || ''}
                        onChange={(e) => setNewProduct({ ...newProduct, vendorId: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="">Select Vendor</option>
                        {vendors.map(vendor => (
                          <option key={vendor.id} value={vendor.id}>
                            {vendor.name}
                          </option>
                        ))}
                      </select>
                      {loadingVendors && <p className="text-sm text-gray-500 mt-1">Loading vendors...</p>}
                      {vendors.length === 0 && !loadingVendors && (
                        <p className="text-sm text-amber-500 mt-1">
                          No vendors available. Please add vendors in the Vendor Management section.
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 mb-2 font-medium">
                        Vendor SKU
                      </label>
                      <input
                        type="text"
                        value={newProduct.vendorSku || ''}
                        onChange={(e) => setNewProduct({ ...newProduct, vendorSku: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        placeholder="Vendor's product code"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-700 mb-2 font-medium">
                        Vendor Cost (BHD)*
                      </label>
                      <input
                        type="number"
                        value={newProduct.vendorCost || ''}
                        onChange={(e) => setNewProduct({ ...newProduct, vendorCost: parseFloat(e.target.value) })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        min="0"
                        step="0.01"
                        placeholder="Cost from vendor"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-700 mb-2 font-medium">
                        Profit Margin (%)
                      </label>
                      <input
                        type="number"
                        value={newProduct.profitMargin || ''}
                        onChange={(e) => handleProfitMarginChange(parseFloat(e.target.value))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        min="0"
                        max="100"
                        step="0.01"
                      />
                      {newProduct.vendorCost && newProduct.dailyRate && (
                        <p className="text-sm text-gray-500 mt-1">
                          Profit: {formatCurrency(newProduct.dailyRate - (newProduct.vendorCost || 0))} per day
                        </p>
                      )}
                    </div>
                  </>
                )}

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Daily Rate (BHD)*
                  </label>
                  <input
                    type="number"
                    value={newProduct.dailyRate || ''}
                    onChange={(e) => setNewProduct({ ...newProduct, dailyRate: parseFloat(e.target.value) })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Weekly Rate (BHD)
                  </label>
                  <input
                    type="number"
                    value={newProduct.weeklyRate || ''}
                    onChange={(e) => {
                      const value = e.target.value ? parseFloat(e.target.value) : newProduct.dailyRate * 5;
                      setNewProduct({ ...newProduct, weeklyRate: value });
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    min="0"
                    step="0.01"
                    placeholder="Leave empty to calculate automatically"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended: {newProduct.dailyRate ? `BHD ${(newProduct.dailyRate * 5).toFixed(2)}` : 'Set daily rate first'}
                  </p>
                </div>

                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Stock*
                  </label>
                  <input
                    type="number"
                    value={newProduct.stock || 1}
                    onChange={(e) => setNewProduct({ ...newProduct, stock: parseInt(e.target.value) })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    min="1"
                  />
                </div>

                <div className="flex items-center">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newProduct.featured || false}
                      onChange={(e) => setNewProduct({ ...newProduct, featured: e.target.checked })}
                      className="mr-2"
                    />
                    <span className="text-gray-700">Featured Product</span>
                  </label>
                  <div className="ml-2 text-xs text-gray-500">
                    (Featured products appear at the top of the product list)
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-gray-700 mb-2 font-medium">
                    Description
                  </label>
                  <textarea
                    value={newProduct.description || ''}
                    onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Compact excavator perfect for small to medium projects..."
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-gray-700 mb-2 font-medium">
                    Image URL*
                  </label>
                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    <input
                      type="text"
                      value={newProduct.image || ''}
                      onChange={(e) => setNewProduct({ ...newProduct, image: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="https://example.com/image.jpg"
                    />
                    {newProduct.image && (
                      <div className="flex items-center">
                        <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded border border-gray-300">
                          <img 
                            src={newProduct.image} 
                            alt="Preview" 
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = 'https://via.placeholder.com/150?text=Invalid+Image';
                            }}
                          />
                        </div>
                        <span className="ml-2 text-sm text-gray-500">Image Preview</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customQuantity}
                      onChange={(e) => setCustomQuantity(e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-gray-700">Allow customers to select quantity</span>
                  </label>
                  <p className="text-sm text-gray-500 mt-1">
                    If unchecked, customers can only select one unit of this item (default quantity is 1)
                  </p>
                </div>
              </div>

              <div className="flex justify-end items-center mt-6 space-x-3">
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    handleCancelEdit();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={editingProductId !== null ? handleUpdateProduct : handleAddProduct}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center"
                >
                  <Save size={18} className="mr-2" />
                  {editingProductId !== null ? 'Update Product' : 'Add Product'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {paginatedProducts.map(product => (
          <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Product Image with Featured Badge Overlay */}
            <div className="aspect-video w-full overflow-hidden relative">
              <img 
                src={product.image} 
                alt={product.name} 
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://cdn.shopify.com/s/files/1/0533/2089/files/placeholder-images-image_large.png';
                }}
              />
              <div className="absolute top-2 right-2 flex flex-col gap-1">
                {product.featured && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                    Featured
                  </span>
                )}
                {product.isExternalVendorItem && (
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                    External Vendor
                  </span>
                )}
              </div>
            </div>
            
            {/* Product Info */}
            <div className="p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-lg line-clamp-1">{product.name}</h3>
                  <p className="text-sm text-gray-500 capitalize">{product.category}</p>
                </div>
                
                {/* Mobile-visible quick actions */}
                <div className="flex space-x-1 md:hidden">
                  <button
                    onClick={() => handleEditProduct(product)}
                    className="p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100"
                    title="Edit"
                  >
                    <Pencil size={16} />
                  </button>
                  <button
                    onClick={() => handleToggleFeatured(product.id)}
                    className={`p-2 rounded-full ${product.featured ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'} hover:bg-yellow-200`}
                    title={product.featured ? "Remove from featured" : "Add to featured"}
                  >
                    <Star size={16} className={product.featured ? "fill-yellow-500" : ""} />
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product.id)}
                    className="p-2 rounded-full bg-red-50 text-red-600 hover:bg-red-100"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
              
              {/* SKU & Barcode */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p className="text-gray-500">SKU</p>
                  <p className="font-mono truncate">{product.sku}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Barcode</p>
                  <p className="font-mono truncate">{product.barcode || '-'}</p>
                </div>
              </div>
              
              {/* Rates & Stock */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p className="text-gray-500">Daily Rate</p>
                  <p className="font-medium">{formatCurrency(product.dailyRate)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-gray-500">Weekly Rate</p>
                  <p className="font-medium">{formatCurrency(product.weeklyRate || product.dailyRate * 5)}</p>
                </div>
              </div>
              
              {/* Vendor Info (if external) */}
              {product.isExternalVendorItem && (
                <div className="pt-2 border-t border-gray-100">
                  <div className="flex justify-between items-center text-sm">
                    <p className="text-purple-700">Vendor Cost:</p>
                    <p className="font-medium">{formatCurrency(product.vendorCost || 0)}</p>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <p className="text-purple-700">Profit Margin:</p>
                    <p className="font-medium">{product.profitMargin?.toFixed(1) || '0'}%</p>
                  </div>
                  {vendors.find(v => v.id === product.vendorId)?.name && (
                    <div className="flex justify-between items-center text-sm mt-1">
                      <p className="text-purple-700">Vendor:</p>
                      <p className="font-medium">{vendors.find(v => v.id === product.vendorId)?.name}</p>
                    </div>
                  )}
                </div>
              )}
              
              <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                <div className="space-y-1">
                  <p className="text-gray-500 text-sm">Stock</p>
                  <p className="font-medium">{product.stock} units</p>
                </div>
                {/* Desktop-only actions */}
                <div className="hidden md:flex space-x-1">
                  <button
                    onClick={() => handleEditProduct(product)}
                    className="p-2 rounded-full hover:bg-blue-50 text-blue-600"
                    title="Edit"
                  >
                    <Pencil size={18} />
                  </button>
                  <button
                    onClick={() => handleToggleFeatured(product.id)}
                    className={`p-2 rounded-full ${product.featured ? 'bg-yellow-100 text-yellow-600' : 'hover:bg-yellow-50 text-gray-400'} hover:bg-yellow-200`}
                    title={product.featured ? "Remove from featured" : "Add to featured"}
                  >
                    <Star size={18} className={product.featured ? "fill-yellow-500" : ""} />
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product.id)}
                    className="p-2 rounded-full hover:bg-red-50 text-red-600"
                    title="Delete"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 mb-4 space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`p-2 rounded-md ${
              currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronLeft size={20} />
          </button>
          
          <div className="hidden md:flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 rounded-md ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
          
          {/* Mobile-friendly page indicator */}
          <div className="md:hidden flex items-center px-3 py-1">
            <span className="text-sm font-medium">
              Page {currentPage} of {totalPages}
            </span>
          </div>
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`p-2 rounded-md ${
              currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronRight size={20} />
          </button>
        </div>
      )}

      {filteredProducts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {searchTerm || categoryFilter !== 'all' 
              ? 'No products match your search criteria.' 
              : 'No products available. Add your first product!'}
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;