import React, { useState } from 'react';
import { Save, Truck, Percent, Plus, Pencil, Trash2, X, Check, DollarSign } from 'lucide-react';
import { SystemSettings, DeliveryOption } from '../../types';

interface SystemSettingsProps {
  settings: SystemSettings;
  onSettingsUpdate: (settings: SystemSettings) => void;
}

const SystemSettingsComponent: React.FC<SystemSettingsProps> = ({ settings, onSettingsUpdate }) => {
  const [taxRate, setTaxRate] = useState<number>(Number(settings.taxRate));
  const [enableTax, setEnableTax] = useState(settings.enableTax);
  const [deliveryOptions, setDeliveryOptions] = useState<DeliveryOption[]>(settings.deliveryOptions);
  const [editingDeliveryId, setEditingDeliveryId] = useState<string | null>(null);
  const [newDelivery, setNewDelivery] = useState<Partial<DeliveryOption>>({
    name: '',
    description: '',
    fee: 0
  });
  const [showAddDelivery, setShowAddDelivery] = useState(false);

  const handleSaveSettings = () => {
    onSettingsUpdate({
      taxRate,
      enableTax,
      deliveryOptions,
      lastQuoteNumber: settings.lastQuoteNumber || 1000
    });
  };

  const handleAddDelivery = () => {
    if (!newDelivery.name) {
      alert('Please enter a name for the delivery option');
      return;
    }

    const deliveryToAdd: DeliveryOption = {
      id: String(Date.now()),
      name: newDelivery.name || '',
      description: newDelivery.description || '',
      fee: newDelivery.fee || 0
    };

    const updatedOptions = [...deliveryOptions, deliveryToAdd];
    setDeliveryOptions(updatedOptions);
    setShowAddDelivery(false);
    setNewDelivery({
      name: '',
      description: '',
      fee: 0
    });
  };

  const handleEditDelivery = (delivery: DeliveryOption) => {
    setEditingDeliveryId(delivery.id);
    setNewDelivery({ ...delivery });
  };

  const handleUpdateDelivery = () => {
    if (!newDelivery.name) {
      alert('Please enter a name for the delivery option');
      return;
    }

    const updatedOptions = deliveryOptions.map(option => 
      option.id === editingDeliveryId ? { 
        ...option, 
        name: newDelivery.name || option.name,
        description: newDelivery.description || option.description,
        fee: newDelivery.fee !== undefined ? newDelivery.fee : option.fee
      } : option
    );
    
    setDeliveryOptions(updatedOptions);
    setEditingDeliveryId(null);
    setNewDelivery({
      name: '',
      description: '',
      fee: 0
    });
  };

  const handleDeleteDelivery = (id: string) => {
    if (deliveryOptions.length <= 1) {
      alert('You must have at least one delivery option');
      return;
    }
    
    if (window.confirm('Are you sure you want to delete this delivery option?')) {
      const updatedOptions = deliveryOptions.filter(option => option.id !== id);
      setDeliveryOptions(updatedOptions);
    }
  };

  const handleCancelEdit = () => {
    setEditingDeliveryId(null);
    setNewDelivery({
      name: '',
      description: '',
      fee: 0
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">System Settings</h2>
        <button
          onClick={handleSaveSettings}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
        >
          <Save size={18} className="mr-2" />
          Save Settings
        </button>
      </div>

      {/* Tax Settings */}
      <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-6">
        <h3 className="font-bold text-lg mb-4 flex items-center">
          <Percent size={18} className="mr-2 text-blue-600" />
          Tax Settings
        </h3>
        
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={enableTax}
              onChange={(e) => setEnableTax(e.target.checked)}
              className="mr-2"
            />
            <span className="text-gray-700">Enable Tax</span>
          </label>
          <p className="text-sm text-gray-500 mt-1">
            When enabled, tax will be calculated and added to quotes
          </p>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2 font-medium">
            Tax Rate (%)
          </label>
          <div className="flex items-center">
            <input
              type="number"
              value={taxRate}
              onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
              className="w-24 p-2 border border-gray-300 rounded-md"
              min="0"
              max="100"
              step="0.01"
              disabled={!enableTax}
            />
            <span className="ml-2">%</span>
          </div>
        </div>
      </div>

      {/* Delivery Options */}
      <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-lg flex items-center">
            <Truck size={18} className="mr-2 text-blue-600" />
            Delivery Options
          </h3>
          <button
            onClick={() => setShowAddDelivery(true)}
            className="px-3 py-1 bg-blue-600 text-white rounded-md flex items-center text-sm"
          >
            <Plus size={16} className="mr-1" />
            Add Option
          </button>
        </div>

        {/* Add/Edit Delivery Form */}
        {(showAddDelivery || editingDeliveryId !== null) && (
          <div className="bg-white border border-gray-200 rounded-md p-4 mb-4">
            <div className="flex justify-between items-center mb-3">
              <h4 className="font-medium">
                {editingDeliveryId !== null ? 'Edit Delivery Option' : 'Add Delivery Option'}
              </h4>
              <button
                onClick={() => {
                  setShowAddDelivery(false);
                  handleCancelEdit();
                }}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={18} />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 mb-2 text-sm font-medium">
                  Name*
                </label>
                <input
                  type="text"
                  value={newDelivery.name || ''}
                  onChange={(e) => setNewDelivery({ ...newDelivery, name: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Standard Delivery"
                />
              </div>
              
              <div>
                <label className="block text-gray-700 mb-2 text-sm font-medium">
                  Fee (BHD)
                </label>
                <div className="flex items-center">
                  <span className="mr-2">BHD</span>
                  <input
                    type="number"
                    value={newDelivery.fee || 0}
                    onChange={(e) => setNewDelivery({ ...newDelivery, fee: parseFloat(e.target.value) || 0 })}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-gray-700 mb-2 text-sm font-medium">
                  Description
                </label>
                <textarea
                  value={newDelivery.description || ''}
                  onChange={(e) => setNewDelivery({ ...newDelivery, description: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={2}
                  placeholder="Describe the delivery option..."
                />
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setShowAddDelivery(false);
                  handleCancelEdit();
                }}
                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={editingDeliveryId !== null ? handleUpdateDelivery : handleAddDelivery}
                className="px-3 py-1 bg-blue-600 text-white rounded-md"
              >
                {editingDeliveryId !== null ? 'Update' : 'Add'}
              </button>
            </div>
          </div>
        )}

        {/* Delivery Options List */}
        <div className="border rounded-md overflow-hidden bg-white">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fee
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {deliveryOptions.map(option => (
                <tr key={option.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Truck size={18} className="text-blue-600 mr-2" />
                      <span className="font-medium">{option.name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-sm text-gray-500">{option.description}</p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end">
                      <DollarSign size={16} className="text-gray-400 mr-1" />
                      <span className="font-medium">{option.fee.toFixed(2)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex justify-center space-x-2">
                      <button
                        onClick={() => handleEditDelivery(option)}
                        className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
                        title="Edit"
                      >
                        <Pencil size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteDelivery(option.id)}
                        className="p-1 rounded-full hover:bg-gray-100 text-red-600"
                        title="Delete"
                        disabled={deliveryOptions.length <= 1}
                      >
                        <Trash2 size={18} className={deliveryOptions.length <= 1 ? 'opacity-50' : ''} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {deliveryOptions.length === 0 && (
          <div className="text-center py-4 bg-white border border-gray-200 rounded-md">
            <p className="text-gray-500">No delivery options available. Add your first option!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemSettingsComponent;