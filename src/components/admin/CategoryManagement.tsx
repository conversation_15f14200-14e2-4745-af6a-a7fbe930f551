import React, { useState, useEffect } from 'react';
import { Plus, Trash2, X, Save, Edit2, RefreshCw } from 'lucide-react';
import { getCategories, addCategory, deleteCategory } from '../../services/database';
import { Product } from '../../types';

interface CategoryManagementProps {
  categories: string[];
  products: Product[];
  onCategoriesUpdate: (categories: string[]) => void;
  onProductsUpdate: (products: Product[]) => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({ 
  categories,
  products,
  onCategoriesUpdate,
  onProductsUpdate
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingCategory, setEditingCategory] = useState<{original: string, new: string} | null>(null);
  const [localCategories, setLocalCategories] = useState<string[]>(categories);

  // Function to count products in a category
  const getProductCount = (category: string) => {
    return products.filter(product => product.category.toLowerCase() === category.toLowerCase()).length;
  };

  // Effect to refresh the categories from Firebase when the component mounts
  useEffect(() => {
    refreshCategories();
  }, []);

  // Update local categories when the parent prop changes
  useEffect(() => {
    setLocalCategories(categories);
  }, [categories]);

  const refreshCategories = async () => {
    try {
      setIsLoading(true);
      const fetchedCategories = await getCategories();
      setLocalCategories(fetchedCategories);
      onCategoriesUpdate(fetchedCategories);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch categories');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      setIsLoading(true);
      await addCategory(newCategory.trim());
      // Refresh the categories from Firebase after adding to ensure we have the latest
      await refreshCategories();
      setNewCategory('');
      setShowAddForm(false);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to add category');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryName: string) => {
    if (window.confirm(`Are you sure you want to delete the category "${categoryName}"? This will move all products in this category to "Uncategorized".`)) {
      try {
        setIsLoading(true);
        await deleteCategory(categoryName);
        // Refresh the categories after deletion
        await refreshCategories();
        // Update any product references in the local state
        const updatedProducts = products.map(product => 
          product.category === categoryName.toLowerCase() ? 
          {...product, category: 'uncategorized'} : 
          product
        );
        onProductsUpdate(updatedProducts);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to delete category');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const startEditCategory = (category: string) => {
    setEditingCategory({ original: category, new: category });
    setIsEditing(true);
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setIsEditing(false);
  };

  const handleEditCategory = async () => {
    if (!editingCategory) return;
    if (!editingCategory.new.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      setIsLoading(true);
      // Delete the old category
      await deleteCategory(editingCategory.original);
      // Add the new category
      await addCategory(editingCategory.new.trim());
      
      // Update products that use this category
      const updatedProducts = products.map(product => 
        product.category === editingCategory.original.toLowerCase() ? 
        {...product, category: editingCategory.new.toLowerCase()} : 
        product
      );
      onProductsUpdate(updatedProducts);
      
      // Refresh the categories list
      await refreshCategories();
      
      setEditingCategory(null);
      setIsEditing(false);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update category');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <h2 className="text-xl font-bold">Category Management</h2>
          <button
            onClick={refreshCategories}
            className="ml-2 p-1 rounded-full hover:bg-gray-100"
            title="Refresh categories"
            disabled={isLoading}
          >
            <RefreshCw size={18} className={isLoading ? "animate-spin" : ""} />
          </button>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          disabled={isLoading || isEditing}
        >
          <Plus size={18} className="mr-2" />
          Add Category
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Add Category Form */}
      {showAddForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg">Add New Category</h3>
            <button
              onClick={() => {
                setShowAddForm(false);
                setNewCategory('');
                setError(null);
              }}
              className="p-1 rounded-full hover:bg-gray-200"
              disabled={isLoading}
            >
              <X size={20} />
            </button>
          </div>
          <div className="flex gap-4">
            <input
              type="text"
              value={newCategory}
              onChange={(e) => setNewCategory(e.target.value)}
              placeholder="Enter category name"
              className="flex-1 p-2 border border-gray-300 rounded-md"
              disabled={isLoading}
            />
            <button
              onClick={handleAddCategory}
              className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
              disabled={isLoading}
            >
              <Save size={18} className="mr-2" />
              Save
            </button>
          </div>
        </div>
      )}

      {/* Edit Category Form */}
      {isEditing && editingCategory && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg">Edit Category</h3>
            <button
              onClick={cancelEdit}
              className="p-1 rounded-full hover:bg-gray-200"
              disabled={isLoading}
            >
              <X size={20} />
            </button>
          </div>
          <div className="flex gap-4">
            <input
              type="text"
              value={editingCategory.new}
              onChange={(e) => setEditingCategory({...editingCategory, new: e.target.value})}
              placeholder="Enter new category name"
              className="flex-1 p-2 border border-gray-300 rounded-md"
              disabled={isLoading}
            />
            <button
              onClick={handleEditCategory}
              className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
              disabled={isLoading}
            >
              <Save size={18} className="mr-2" />
              Update
            </button>
          </div>
        </div>
      )}

      {/* Categories List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {localCategories.map(category => (
          <div
            key={category}
            className="flex items-center justify-between p-4 bg-gray-50 rounded-md"
          >
            <div className="flex items-center gap-2">
              <span className="capitalize">{category}</span>
              <span className="text-sm text-gray-500 bg-gray-200 px-2 py-0.5 rounded-full">
                {getProductCount(category)} {getProductCount(category) === 1 ? 'product' : 'products'}
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => startEditCategory(category)}
                className="p-1 rounded-full hover:bg-blue-100 text-blue-600"
                title="Edit category"
                disabled={isLoading || isEditing}
              >
                <Edit2 size={18} />
              </button>
              <button
                onClick={() => handleDeleteCategory(category)}
                className="p-1 rounded-full hover:bg-red-100 text-red-600"
                title="Delete category"
                disabled={isLoading || isEditing}
              >
                <Trash2 size={18} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {isLoading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading categories...</p>
        </div>
      )}

      {!isLoading && localCategories.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No categories available. Add your first category!
        </div>
      )}
    </div>
  );
};

export default CategoryManagement;