import React, { useState } from 'react';
import { Search, Check, X, ChevronDown, ChevronUp, Calendar, User, Edit, Save, Download, Plus, Trash2, DollarSign, FileText, CreditCard, Info, Filter } from 'lucide-react';
import { Booking, Payment } from '../../types';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

interface PaymentManagementProps {
  bookings: Booking[];
  onBookingsUpdate: (bookings: Booking[]) => void;
}

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

const PaymentManagement: React.FC<PaymentManagementProps> = ({ bookings, onBookingsUpdate }) => {
  const [expandedBookingId, setExpandedBookingId] = useState<number | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [selectedBookingId, setSelectedBookingId] = useState<number | null>(null);
  const [paymentType, setPaymentType] = useState<'full' | 'partial' | 'advance'>('full');
  const [editingPaymentId, setEditingPaymentId] = useState<number | null>(null);
  const [newPayment, setNewPayment] = useState<Partial<Payment>>({
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    method: 'cash',
    reference: '',
    notes: ''
  });

  // Filter bookings based on search term and payment status
  const filteredBookings = bookings
    .filter(booking => {
      if (statusFilter === 'all') return true;
      const paymentStatus = getPaymentStatus(booking);
      return paymentStatus === statusFilter;
    })
    .filter(booking => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        booking.quoteNumber.toLowerCase().includes(searchLower) ||
        booking.customer.name.toLowerCase().includes(searchLower) ||
        booking.customer.email.toLowerCase().includes(searchLower) ||
        booking.customer.phone.toLowerCase().includes(searchLower)
      );
    });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate payment status
  const getPaymentStatus = (booking: Booking) => {
    const totalAmount = booking.totalAmount || 0;
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);

    if (paidAmount === 0) return 'unpaid';
    if (Math.abs(totalAmount - paidAmount) < 0.001) return 'paid'; // Consider paid if difference is negligible
    return 'partial';
  };

  // Get payment status badge class
  const getPaymentStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'unpaid':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate payment progress percentage
  const calculatePaymentProgress = (booking: Booking) => {
    const totalAmount = booking.totalAmount || 0;
    if (totalAmount === 0) return 0;

    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    return Math.min(100, Math.round((paidAmount / totalAmount) * 100));
  };

  // Handle showing add payment modal
  const handleShowAddPayment = (bookingId: number) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking) return;

    setSelectedBookingId(bookingId);
    setPaymentType('full');

    // Calculate remaining amount
    const totalAmount = booking.totalAmount || 0;
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = totalAmount - paidAmount;

    setNewPayment({
      bookingId: bookingId,
      amount: remainingAmount,
      date: new Date().toISOString().split('T')[0],
      method: 'cash',
      reference: '',
      notes: ''
    });

    setShowAddPaymentModal(true);
  };

  // Handle payment type change
  const handlePaymentTypeChange = (type: 'full' | 'partial' | 'advance') => {
    setPaymentType(type);

    if (!selectedBookingId) return;

    const booking = bookings.find(b => b.id === selectedBookingId);
    if (!booking) return;

    const totalAmount = booking.totalAmount || 0;
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = totalAmount - paidAmount;

    switch (type) {
      case 'full':
        setNewPayment(prev => ({ ...prev, amount: remainingAmount }));
        break;
      case 'partial':
        setNewPayment(prev => ({ ...prev, amount: remainingAmount / 2 }));
        break;
      case 'advance':
        setNewPayment(prev => ({ ...prev, amount: totalAmount * 0.25 }));
        break;
    }
  };

  // Handle adding a payment
  const handleAddPaymentSubmit = () => {
    if (!selectedBookingId || !newPayment.amount || !newPayment.date || !newPayment.method) return;

    const payment: Payment = {
      id: Date.now(),
      bookingId: selectedBookingId,
      amount: newPayment.amount,
      date: newPayment.date,
      method: newPayment.method as 'cash' | 'card' | 'bank_transfer' | 'cheque' | 'other',
      reference: newPayment.reference,
      notes: newPayment.notes
    };

    const updatedBookings = bookings.map(booking => {
      if (booking.id === selectedBookingId) {
        const payments = booking.payments || [];
        return {
          ...booking,
          payments: [...payments, payment]
        };
      }
      return booking;
    });

    onBookingsUpdate(updatedBookings);
    setShowAddPaymentModal(false);
    resetNewPayment();
  };

  // Handle editing a payment
  const handleEditPayment = (bookingId: number, paymentId: number) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;

    const payment = booking.payments.find(p => p.id === paymentId);
    if (!payment) return;

    setSelectedBookingId(bookingId);
    setEditingPaymentId(paymentId);
    setNewPayment({
      bookingId: payment.bookingId,
      amount: payment.amount,
      date: payment.date,
      method: payment.method,
      reference: payment.reference,
      notes: payment.notes
    });

    setShowAddPaymentModal(true);
  };

  // Handle updating a payment
  const handleUpdatePayment = () => {
    if (!selectedBookingId || !editingPaymentId || !newPayment.amount || !newPayment.date || !newPayment.method) return;

    const updatedBookings = bookings.map(booking => {
      if (booking.id === selectedBookingId && booking.payments) {
        const updatedPayments = booking.payments.map(payment => {
          if (payment.id === editingPaymentId) {
            return {
              ...payment,
              amount: newPayment.amount || payment.amount,
              date: newPayment.date || payment.date,
              method: newPayment.method as 'cash' | 'card' | 'bank_transfer' | 'cheque' | 'other',
              reference: newPayment.reference,
              notes: newPayment.notes
            };
          }
          return payment;
        });

        return {
          ...booking,
          payments: updatedPayments
        };
      }
      return booking;
    });

    onBookingsUpdate(updatedBookings);
    setShowAddPaymentModal(false);
    setEditingPaymentId(null);
    resetNewPayment();
  };

  // Handle deleting a payment
  const handleDeletePayment = (bookingId: number, paymentId: number) => {
    if (!window.confirm('Are you sure you want to delete this payment?')) return;

    const updatedBookings = bookings.map(booking => {
      if (booking.id === bookingId && booking.payments) {
        return {
          ...booking,
          payments: booking.payments.filter(payment => payment.id !== paymentId)
        };
      }
      return booking;
    });

    onBookingsUpdate(updatedBookings);
  };

  // Reset new payment form
  const resetNewPayment = () => {
    setNewPayment({
      amount: 0,
      date: new Date().toISOString().split('T')[0],
      method: 'cash',
      reference: '',
      notes: ''
    });
    setSelectedBookingId(null);
    setPaymentType('full');
  };

  // Generate receipt PDF
  const generateReceiptPDF = (booking: Booking, payment: Payment) => {
    try {
      const doc = new jsPDF();

      // Add company logo/header
      doc.setFontSize(20);
      doc.setTextColor(0, 0, 255);
      doc.text('Equipment Rental', 105, 15, { align: 'center' });

      // Add receipt information
      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text('PAYMENT RECEIPT', 105, 30, { align: 'center' });

      doc.setFontSize(12);
      doc.text(`Receipt #: P-${payment.id}`, 14, 45);
      doc.text(`Date: ${formatDate(payment.date)}`, 14, 52);
      doc.text(`Booking #: ${booking.quoteNumber}`, 14, 59);

      // Add customer information
      doc.setFontSize(14);
      doc.text('Customer Information', 14, 72);
      doc.setFontSize(11);
      doc.text(`Name: ${booking.customer.name}`, 14, 80);
      doc.text(`Email: ${booking.customer.email}`, 14, 87);
      doc.text(`Phone: ${booking.customer.phone}`, 14, 94);

      // Add payment details
      doc.setFontSize(14);
      doc.text('Payment Details', 14, 110);
      doc.setFontSize(11);
      doc.text(`Amount: ${formatCurrency(payment.amount)}`, 14, 118);
      doc.text(`Method: ${payment.method.replace('_', ' ').toUpperCase()}`, 14, 125);

      if (payment.reference) {
        doc.text(`Reference: ${payment.reference}`, 14, 132);
      }

      // Add booking summary
      doc.setFontSize(14);
      doc.text('Booking Summary', 14, 145);

      const totalAmount = booking.totalAmount || 0;
      const paidAmount = (booking.payments || []).reduce((sum, p) => sum + p.amount, 0);
      const remainingAmount = totalAmount - paidAmount;

      doc.setFontSize(11);
      doc.text(`Total Amount: ${formatCurrency(totalAmount)}`, 14, 153);
      doc.text(`Amount Paid: ${formatCurrency(paidAmount)}`, 14, 160);
      doc.text(`Remaining Balance: ${formatCurrency(remainingAmount)}`, 14, 167);

      // Add notes if available
      if (payment.notes) {
        doc.setFontSize(14);
        doc.text('Notes', 14, 180);
        doc.setFontSize(11);
        doc.text(payment.notes, 14, 188);
      }

      // Add footer
      doc.setFontSize(10);
      doc.text('Thank you for your business!', 105, 270, { align: 'center' });

      // Save the PDF
      doc.save(`Payment_Receipt_${booking.quoteNumber}_${payment.id}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl font-bold">Payment Management</h2>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search bookings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full sm:w-auto"
            />
          </div>
          <div className="flex items-center">
            <span className="mr-2 text-sm text-gray-600">Filter:</span>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Payments</option>
              <option value="paid">Paid</option>
              <option value="partial">Partially Paid</option>
              <option value="unpaid">Unpaid</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Total Bookings</p>
              <p className="text-2xl font-bold">{bookings.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full text-blue-600">
              <FileText size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Fully Paid</p>
              <p className="text-2xl font-bold">
                {bookings.filter(booking => getPaymentStatus(booking) === 'paid').length}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full text-green-600">
              <Check size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Pending Payments</p>
              <p className="text-2xl font-bold">
                {bookings.filter(booking => getPaymentStatus(booking) !== 'paid').length}
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full text-yellow-600">
              <DollarSign size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Bookings List */}
      <div className="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Booking
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBookings.map(booking => {
                const paymentStatus = getPaymentStatus(booking);
                const totalAmount = booking.totalAmount || 0;
                const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
                const remainingAmount = totalAmount - paidAmount;
                const progressPercentage = calculatePaymentProgress(booking);

                return (
                  <React.Fragment key={booking.id}>
                    <tr className={expandedBookingId === booking.id ? 'bg-blue-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                            <FileText size={20} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{booking.quoteNumber}</div>
                            <div className="text-sm text-gray-500">{formatDate(booking.date)}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{booking.customer.name}</div>
                        <div className="text-sm text-gray-500">{booking.customer.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <div className="text-sm font-medium text-gray-900">{formatCurrency(totalAmount)}</div>
                        {paymentStatus !== 'unpaid' && (
                          <div className="text-sm text-gray-500">
                            Paid: {formatCurrency(paidAmount)}
                          </div>
                        )}
                        {paymentStatus === 'partial' && (
                          <div className="text-sm text-red-600">
                            Remaining: {formatCurrency(remainingAmount)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col items-center">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(paymentStatus)} capitalize`}>
                            {paymentStatus}
                          </span>
                          {paymentStatus !== 'unpaid' && (
                            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                              <div
                                className={`h-2.5 rounded-full ${
                                  progressPercentage === 100 ? 'bg-green-600' : 'bg-yellow-400'
                                }`}
                                style={{ width: `${progressPercentage}%` }}
                              ></div>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <button
                            onClick={() => setExpandedBookingId(expandedBookingId === booking.id ? null : booking.id)}
                            className="p-1 rounded-full hover:bg-gray-100"
                            title={expandedBookingId === booking.id ? "Collapse" : "Expand"}
                          >
                            {expandedBookingId === booking.id ? (
                              <ChevronUp size={18} />
                            ) : (
                              <ChevronDown size={18} />
                            )}
                          </button>
                          <button
                            onClick={() => handleShowAddPayment(booking.id)}
                            className="p-1 rounded-full hover:bg-gray-100 text-blue-600"
                            title="Add Payment"
                          >
                            <Plus size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>

                    {/* Expanded Booking Details */}
                    {expandedBookingId === booking.id && (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Booking Details */}
                            <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                                <FileText size={18} className="mr-2 text-blue-600" />
                                Booking Details
                              </h4>
                              <div className="space-y-3">
                                <div>
                                  <p className="text-sm text-gray-500">Quote Number</p>
                                  <p className="font-medium">{booking.quoteNumber}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Date</p>
                                  <p className="font-medium">{formatDate(booking.date)}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Status</p>
                                  <p className="font-medium capitalize">{booking.status}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Customer</p>
                                  <p className="font-medium">{booking.customer.name}</p>
                                  <p className="text-sm text-gray-500">{booking.customer.email}</p>
                                  <p className="text-sm text-gray-500">{booking.customer.phone}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Total Amount</p>
                                  <p className="font-medium">{formatCurrency(totalAmount)}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Payment Status</p>
                                  <div className="flex items-center">
                                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(paymentStatus)} capitalize mr-2`}>
                                      {paymentStatus}
                                    </span>
                                    <span className="text-sm">
                                      {paymentStatus === 'paid' && 'Fully paid'}
                                      {paymentStatus === 'partial' && `${formatCurrency(paidAmount)} of ${formatCurrency(totalAmount)}`}
                                      {paymentStatus === 'unpaid' && 'No payments recorded'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Payment History */}
                            <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                              <div className="flex justify-between items-center mb-3">
                                <h4 className="font-medium text-gray-900 flex items-center">
                                  <CreditCard size={18} className="mr-2 text-blue-600" />
                                  Payment History
                                </h4>
                                <button
                                  onClick={() => handleShowAddPayment(booking.id)}
                                  className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
                                >
                                  <Plus size={14} className="mr-1" />
                                  Add Payment
                                </button>
                              </div>

                              {(!booking.payments || booking.payments.length === 0) ? (
                                <div className="text-center py-6 bg-gray-50 rounded-md">
                                  <p className="text-gray-500">No payments recorded yet.</p>
                                </div>
                              ) : (
                                <div className="space-y-3 max-h-64 overflow-y-auto">
                                  {booking.payments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).map(payment => (
                                    <div key={payment.id} className="border rounded-md p-3 bg-gray-50">
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <div className="font-medium">{formatCurrency(payment.amount)}</div>
                                          <div className="text-sm text-gray-500 flex items-center">
                                            <Calendar size={14} className="mr-1" />
                                            {formatDate(payment.date)}
                                          </div>
                                          <div className="text-sm text-gray-500 capitalize">
                                            Method: {payment.method.replace('_', ' ')}
                                          </div>
                                          {payment.reference && (
                                            <div className="text-sm text-gray-500">
                                              Ref: {payment.reference}
                                            </div>
                                          )}
                                        </div>
                                        <div className="flex space-x-1">
                                          <button
                                            onClick={() => handleEditPayment(booking.id, payment.id)}
                                            className="p-1 rounded-full hover:bg-gray-200 text-blue-600"
                                            title="Edit Payment"
                                          >
                                            <Edit size={16} />
                                          </button>
                                          <button
                                            onClick={() => handleDeletePayment(booking.id, payment.id)}
                                            className="p-1 rounded-full hover:bg-gray-200 text-red-600"
                                            title="Delete Payment"
                                          >
                                            <Trash2 size={16} />
                                          </button>
                                          <button
                                            onClick={() => generateReceiptPDF(booking, payment)}
                                            className="p-1 rounded-full hover:bg-gray-200 text-green-600"
                                            title="Download Receipt"
                                          >
                                            <Download size={16} />
                                          </button>
                                        </div>
                                      </div>
                                      {payment.notes && (
                                        <div className="mt-2 text-sm text-gray-600 bg-white p-2 rounded border border-gray-200">
                                          {payment.notes}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}

                              {paymentStatus !== 'paid' && (
                                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                  <div className="flex items-center text-yellow-800">
                                    <Info size={16} className="mr-2" />
                                    <span className="font-medium">Payment Required</span>
                                  </div>
                                  <p className="text-sm text-yellow-700 mt-1">
                                    Remaining balance: {formatCurrency(remainingAmount)}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {filteredBookings.length === 0 && (
        <div className="text-center py-8 bg-gray-50 rounded-md mt-4">
          <p className="text-gray-500">No bookings found matching your search criteria.</p>
        </div>
      )}

      {/* Add Payment Modal */}
      {showAddPaymentModal && selectedBookingId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-lg">
                {editingPaymentId ? 'Edit Payment' : 'Record Payment'}
              </h3>
              <button
                onClick={() => {
                  setShowAddPaymentModal(false);
                  setEditingPaymentId(null);
                  resetNewPayment();
                }}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={20} />
              </button>
            </div>

            {!editingPaymentId && (
              <div className="mb-4">
                <label className="block text-gray-700 mb-2 font-medium">
                  Payment Type
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => handlePaymentTypeChange('full')}
                    className={`p-2 border rounded-md text-center ${
                      paymentType === 'full'
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700'
                    }`}
                  >
                    Full Payment
                  </button>
                  <button
                    onClick={() => handlePaymentTypeChange('partial')}
                    className={`p-2 border rounded-md text-center ${
                      paymentType === 'partial'
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700'
                    }`}
                  >
                    Partial
                  </button>
                  <button
                    onClick={() => handlePaymentTypeChange('advance')}
                    className={`p-2 border rounded-md text-center ${
                      paymentType === 'advance'
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700'
                    }`}
                  >
                    Advance
                  </button>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <DollarSign size={18} className="mr-2 text-blue-600" />
                    Amount*
                  </div>
                </label>
                <div className="flex items-center">
                  <span className="mr-2 text-gray-600">BHD</span>
                  <input
                    type="number"
                    value={newPayment.amount || ''}
                    onChange={(e) => setNewPayment(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    step="0.001"
                    min="0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <Calendar size={18} className="mr-2 text-blue-600" />
                    Date*
                  </div>
                </label>
                <input
                  type="date"
                  value={newPayment.date || ''}
                  onChange={(e) => setNewPayment(prev => ({ ...prev, date: e.target.value }))}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <CreditCard size={18} className="mr-2 text-blue-600" />
                    Payment Method*
                  </div>
                </label>
                <select
                  value={newPayment.method || 'cash'}
                  onChange={(e) => setNewPayment(prev => ({ ...prev, method: e.target.value as any }))}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="cash">Cash</option>
                  <option value="card">Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="cheque">Cheque</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <FileText size={18} className="mr-2 text-gray-600" />
                    Reference (Optional)
                  </div>
                </label>
                <input
                  type="text"
                  value={newPayment.reference || ''}
                  onChange={(e) => setNewPayment(prev => ({ ...prev, reference: e.target.value }))}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Transaction ID, Receipt #, etc."
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-medium">
                  <div className="flex items-center">
                    <Info size={18} className="mr-2 text-gray-600" />
                    Notes (Optional)
                  </div>
                </label>
                <textarea
                  value={newPayment.notes || ''}
                  onChange={(e) => setNewPayment(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Additional payment details..."
                />
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowAddPaymentModal(false);
                  setEditingPaymentId(null);
                  resetNewPayment();
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={editingPaymentId ? handleUpdatePayment : handleAddPaymentSubmit}
                disabled={!newPayment.amount || !newPayment.date || !newPayment.method}
                className={`px-4 py-2 rounded-md ${
                  newPayment.amount && newPayment.date && newPayment.method
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {editingPaymentId ? (
                  <span className="flex items-center">
                    <Save size={18} className="mr-2" />
                    Update Payment
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Check size={18} className="mr-2" />
                    Record Payment
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentManagement;