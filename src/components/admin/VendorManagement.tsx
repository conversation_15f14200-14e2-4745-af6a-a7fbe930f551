import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, X, Save, DollarSign, User, Search, AlertCircle, Mail, Phone } from 'lucide-react';
import { Vendor, VendorTransaction, Product, BulkDiscount, VendorDiscount } from '../../types';
import { 
  getVendors, 
  addVendor, 
  updateVendor, 
  deleteVendor, 
  getVendorTransactions,
  addVendorTransaction,
  getProductsByVendor,
  getVendorRevenueData,
  updateProduct
} from '../../services/database';
import VendorReporting from './VendorReporting';

interface VendorManagementProps {
  onUpdate: (vendors: Vendor[]) => void;
}

const VendorManagement: React.FC<VendorManagementProps> = ({ onUpdate }) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingVendorId, setEditingVendorId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendorId, setSelectedVendorId] = useState<string | null>(null);
  const [vendorProducts, setVendorProducts] = useState<Product[]>([]);
  const [vendorTransactions, setVendorTransactions] = useState<VendorTransaction[]>([]);
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  const [revenueData, setRevenueData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [showBulkDiscountForm, setShowBulkDiscountForm] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [newBulkDiscount, setNewBulkDiscount] = useState<BulkDiscount>({
    minQuantity: 1,
    discountPercentage: 0
  });
  
  const [newVendor, setNewVendor] = useState<Partial<Vendor>>({
    name: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
    paymentTerms: 'Net 30',
    active: true,
    taxPercentage: 0
  });

  const [newTransaction, setNewTransaction] = useState<Partial<VendorTransaction>>({
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0],
    type: 'payment',
    status: 'completed',
    reference: ''
  });

  const [showDiscountForm, setShowDiscountForm] = useState(false);
  const [newDiscount, setNewDiscount] = useState<VendorDiscount>({
    type: 'percentage',
    value: 0,
    description: ''
  });

  // Load vendors on component mount
  useEffect(() => {
    loadVendors();
  }, []);

  // Load vendor details when a vendor is selected
  useEffect(() => {
    if (selectedVendorId) {
      loadVendorDetails(selectedVendorId);
    } else {
      setVendorProducts([]);
      setVendorTransactions([]);
      setRevenueData(null);
    }
  }, [selectedVendorId]);

  // Load all vendors
  const loadVendors = async () => {
    setLoading(true);
    try {
      const fetchedVendors = await getVendors();
      setVendors(fetchedVendors);
    } catch (error) {
      console.error('Error loading vendors:', error);
      setError('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  // Load details for a specific vendor
  const loadVendorDetails = async (vendorId: string) => {
    try {
      const [products, transactions] = await Promise.all([
        getProductsByVendor(vendorId),
        getVendorTransactions(vendorId)
      ]);
      
      setVendorProducts(products);
      setVendorTransactions(transactions);
      
      // Get revenue data for the last 30 days
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      
      const revenue = await getVendorRevenueData(vendorId, startDate, endDate);
      setRevenueData(revenue);
    } catch (error) {
      console.error('Error loading vendor details:', error);
      setError('Failed to load vendor details');
    }
  };

  // Filter vendors based on search term
  const filteredVendors = vendors.filter(vendor => 
    vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.contactName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle vendor CRUD operations
  const handleAddVendor = async () => {
    if (!newVendor.name) {
      setError('Vendor name is required');
      return;
    }
    
    try {
      await addVendor(newVendor as Omit<Vendor, 'id' | 'createdAt'>);
      await loadVendors();
      setShowAddForm(false);
      setNewVendor({
        name: '',
        contactName: '',
        email: '',
        phone: '',
        address: '',
        notes: '',
        paymentTerms: 'Net 30',
        active: true,
        taxPercentage: 0
      });
      if (onUpdate) onUpdate(vendors);
      setError(null);
    } catch (error) {
      console.error('Error adding vendor:', error);
      setError('Failed to add vendor');
    }
  };

  const handleUpdateVendor = async () => {
    if (!editingVendorId) return;
    
    try {
      const vendorToUpdate = vendors.find(v => v.id === editingVendorId);
      if (!vendorToUpdate) return;
      
      await updateVendor({
        ...vendorToUpdate,
        ...newVendor
      });
      
      await loadVendors();
      setEditingVendorId(null);
      setNewVendor({
        name: '',
        contactName: '',
        email: '',
        phone: '',
        address: '',
        notes: '',
        paymentTerms: 'Net 30',
        active: true,
        taxPercentage: 0
      });
      if (onUpdate) onUpdate(vendors);
      setError(null);
    } catch (error) {
      console.error('Error updating vendor:', error);
      setError('Failed to update vendor');
    }
  };

  const handleDeleteVendor = async (vendorId: string) => {
    if (!confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      return;
    }
    
    try {
      await deleteVendor(vendorId);
      await loadVendors();
      if (selectedVendorId === vendorId) {
        setSelectedVendorId(null);
      }
      if (onUpdate) onUpdate(vendors);
      setError(null);
    } catch (error) {
      console.error('Error deleting vendor:', error);
      setError('Failed to delete vendor. Make sure there are no associated products.');
    }
  };

  const handleAddTransaction = async () => {
    if (!selectedVendorId || !newTransaction.amount || !newTransaction.description) {
      setError('Please fill in all required fields');
      return;
    }
    
    try {
      await addVendorTransaction({
        ...newTransaction,
        vendorId: selectedVendorId
      } as Omit<VendorTransaction, 'id'>);
      
      await loadVendorDetails(selectedVendorId);
      setShowTransactionForm(false);
      setNewTransaction({
        amount: 0,
        description: '',
        date: new Date().toISOString().split('T')[0],
        type: 'payment',
        status: 'completed',
        reference: ''
      });
      setError(null);
    } catch (error) {
      console.error('Error adding transaction:', error);
      setError('Failed to add transaction');
    }
  };

  const handleEditVendor = (vendor: Vendor) => {
    setEditingVendorId(vendor.id);
    setNewVendor(vendor);
    setShowAddForm(true);
  };

  const handleCancelEdit = () => {
    setEditingVendorId(null);
    setShowAddForm(false);
    setNewVendor({
      name: '',
      contactName: '',
      email: '',
      phone: '',
      address: '',
      notes: '',
      paymentTerms: 'Net 30',
      active: true,
      taxPercentage: 0
    });
    setError(null);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const handleAddBulkDiscount = (productId: string) => {
    setSelectedProductId(productId);
    setNewBulkDiscount({
      minQuantity: 1,
      discountPercentage: 0
    });
    setShowBulkDiscountForm(true);
  };

  const handleAddBulkDiscountSubmit = async () => {
    if (!selectedProductId) return;

    try {
      const product = vendorProducts.find(p => p.id === selectedProductId);
      if (!product) return;

      const updatedProduct = {
        ...product,
        bulkDiscounts: [
          ...(product.bulkDiscounts || []),
          newBulkDiscount
        ].sort((a, b) => a.minQuantity - b.minQuantity)
      };

      await updateProduct(updatedProduct);
      
      // Update local state
      setVendorProducts(vendorProducts.map(p => 
        p.id === selectedProductId ? updatedProduct : p
      ));

      setShowBulkDiscountForm(false);
      setSelectedProductId(null);
      setNewBulkDiscount({
        minQuantity: 1,
        discountPercentage: 0
      });
      setError(null);
    } catch (error) {
      console.error('Error adding bulk discount:', error);
      setError('Failed to add bulk discount');
    }
  };

  const handleRemoveBulkDiscount = async (productId: string, index: number) => {
    try {
      const product = vendorProducts.find(p => p.id === productId);
      if (!product) return;

      const updatedProduct = {
        ...product,
        bulkDiscounts: (product.bulkDiscounts || []).filter((_, i) => i !== index)
      };

      await updateProduct(updatedProduct);
      
      // Update local state
      setVendorProducts(vendorProducts.map(p => 
        p.id === productId ? updatedProduct : p
      ));

      setError(null);
    } catch (error) {
      console.error('Error removing bulk discount:', error);
      setError('Failed to remove bulk discount');
    }
  };

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center">
          <AlertCircle className="mr-2" size={20} />
          {error}
        </div>
      )}

      {/* Search and Add Vendor */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search vendors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <button
          onClick={() => {
            setShowAddForm(true);
            setEditingVendorId(null);
            setNewVendor({
              name: '',
              contactName: '',
              email: '',
              phone: '',
              address: '',
              notes: '',
              paymentTerms: 'Net 30',
              active: true,
              taxPercentage: 0
            });
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700"
        >
          <Plus size={20} className="mr-2" />
          Add Vendor
        </button>
      </div>

      {/* Vendor List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredVendors.map(vendor => (
          <div
            key={vendor.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedVendorId === vendor.id
                ? 'border-blue-500 bg-blue-50'
                : 'hover:border-blue-300'
            }`}
            onClick={() => setSelectedVendorId(vendor.id)}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold text-lg">{vendor.name}</h3>
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditVendor(vendor);
                  }}
                  className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                >
                  <Pencil size={16} />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteVendor(vendor.id);
                  }}
                  className="p-1 text-red-600 hover:bg-red-100 rounded"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
            {vendor.contactName && (
              <p className="text-gray-600 text-sm mb-1">
                <User size={14} className="inline mr-1" />
                {vendor.contactName}
              </p>
            )}
            {vendor.email && (
              <p className="text-gray-600 text-sm mb-1">
                <Mail size={14} className="inline mr-1" />
                {vendor.email}
              </p>
            )}
            {vendor.phone && (
              <p className="text-gray-600 text-sm mb-1">
                <Phone size={14} className="inline mr-1" />
                {vendor.phone}
              </p>
            )}
            <div className="mt-2 flex items-center justify-between text-sm">
              <span className="text-gray-500">Payment Terms:</span>
              <span className="font-medium">{vendor.paymentTerms}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Vendor Form */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white rounded-lg p-6 w-full max-w-md my-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                {editingVendorId ? 'Edit Vendor' : 'Add New Vendor'}
              </h2>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-500"
              >
                <X size={24} />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  value={newVendor.name}
                  onChange={(e) => setNewVendor({ ...newVendor, name: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Name
                </label>
                <input
                  type="text"
                  value={newVendor.contactName}
                  onChange={(e) => setNewVendor({ ...newVendor, contactName: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={newVendor.email}
                  onChange={(e) => setNewVendor({ ...newVendor, email: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={newVendor.phone}
                  onChange={(e) => setNewVendor({ ...newVendor, phone: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <textarea
                  value={newVendor.address}
                  onChange={(e) => setNewVendor({ ...newVendor, address: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Terms
                </label>
                <input
                  type="text"
                  value={newVendor.paymentTerms}
                  onChange={(e) => setNewVendor({ ...newVendor, paymentTerms: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Percentage
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={newVendor.taxPercentage || 0}
                    onChange={(e) => setNewVendor({ ...newVendor, taxPercentage: parseFloat(e.target.value) })}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    max="100"
                    step="0.01"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <span className="text-gray-500">%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Tax percentage to be applied to vendor invoices
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={newVendor.notes}
                  onChange={(e) => setNewVendor({ ...newVendor, notes: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={newVendor.active}
                  onChange={(e) => setNewVendor({ ...newVendor, active: e.target.checked })}
                  className="mr-2"
                />
                <label className="text-sm font-medium text-gray-700">Active</label>
              </div>

              {/* Vendor Discounts Section */}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">Vendor Discounts</h3>
                  <button
                    onClick={() => {
                      setNewDiscount({
                        type: 'percentage',
                        value: 0,
                        description: ''
                      });
                      setShowDiscountForm(true);
                    }}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center"
                  >
                    <Plus size={16} className="mr-1" />
                    Add Discount
                  </button>
                </div>
                
                {newVendor.discounts && newVendor.discounts.length > 0 ? (
                  <div className="space-y-3">
                    {newVendor.discounts.map((discount, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium capitalize">{discount.type} Discount</span>
                          <button
                            onClick={() => {
                              setNewVendor({
                                ...newVendor,
                                discounts: newVendor.discounts?.filter((_, i) => i !== index)
                              });
                            }}
                            className="text-red-500 hover:text-red-600"
                          >
                            <X size={16} />
                          </button>
                        </div>
                        <div className="text-sm text-gray-600">
                          {discount.type === 'percentage' && `${discount.value}% off`}
                          {discount.type === 'fixed' && `${formatCurrency(discount.value)} off`}
                          {discount.type === 'total' && `Discount on orders over ${formatCurrency(discount.minAmount || 0)}`}
                        </div>
                        {discount.description && (
                          <p className="text-sm text-gray-500 mt-1">{discount.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No discounts set</p>
                )}
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3 sticky bottom-0 bg-white py-4 border-t">
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={editingVendorId ? handleUpdateVendor : handleAddVendor}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {editingVendorId ? 'Update' : 'Add'} Vendor
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Selected Vendor Details */}
      {selectedVendorId && (
        <div className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Vendor Details</h2>
            <button
              onClick={() => setShowTransactionForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-green-700"
            >
              <Plus size={20} className="mr-2" />
              Add Transaction
            </button>
          </div>

          {/* Vendor Products */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Associated Products</h3>
            {vendorProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {vendorProducts.map(product => (
                  <div key={product.id} className="border rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <div className="h-12 w-12 rounded overflow-hidden mr-3">
                        <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-500">Vendor Cost:</span>
                        <span className="font-medium ml-1">{formatCurrency(product.vendorCost || 0)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Selling Price:</span>
                        <span className="font-medium ml-1">{formatCurrency(product.dailyRate)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Profit Margin:</span>
                        <span className="font-medium ml-1">{product.profitMargin || 0}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Stock:</span>
                        <span className="font-medium ml-1">{product.stock}</span>
                      </div>
                    </div>
                    {/* Bulk Booking Discount Section */}
                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">Bulk Booking Discount</span>
                        <button
                          onClick={() => handleAddBulkDiscount(product.id)}
                          className="text-blue-600 hover:text-blue-700 text-sm"
                        >
                          Add Discount
                        </button>
                      </div>
                      {product.bulkDiscounts && product.bulkDiscounts.length > 0 ? (
                        <div className="space-y-2">
                          {product.bulkDiscounts.map((discount, index) => (
                            <div key={index} className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">
                                {discount.minQuantity}+ units:
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className="font-medium text-green-600">
                                  {discount.discountPercentage}% off
                                </span>
                                <button
                                  onClick={() => handleRemoveBulkDiscount(product.id, index)}
                                  className="text-red-500 hover:text-red-600"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">No bulk discounts set</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No products associated with this vendor.</p>
            )}
          </div>

          {/* Vendor Transactions */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Transactions</h3>
            {vendorTransactions.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border rounded-lg">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Date</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Type</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Description</th>
                      <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">Amount</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {vendorTransactions.map(transaction => (
                      <tr key={transaction.id} className="border-t">
                        <td className="px-4 py-2 text-sm">
                          {new Date(transaction.date).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-2 text-sm capitalize">{transaction.type}</td>
                        <td className="px-4 py-2 text-sm">{transaction.description}</td>
                        <td className="px-4 py-2 text-sm text-right font-medium">
                          {formatCurrency(transaction.amount)}
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            transaction.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : transaction.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {transaction.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No transactions found.</p>
            )}
          </div>

          {/* Vendor Financial Report */}
          <VendorReporting
            vendor={vendors.find(v => v.id === selectedVendorId)!}
            vendorProducts={vendorProducts}
          />
        </div>
      )}

      {/* Add Transaction Form */}
      {showTransactionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-semibold mb-4">Add Transaction</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type *
                </label>
                <select
                  value={newTransaction.type}
                  onChange={(e) => setNewTransaction({ ...newTransaction, type: e.target.value as 'payment' | 'invoice' })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="payment">Payment</option>
                  <option value="invoice">Invoice</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount *
                </label>
                <input
                  type="number"
                  value={newTransaction.amount}
                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  value={newTransaction.description}
                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  value={newTransaction.date}
                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reference
                </label>
                <input
                  type="text"
                  value={newTransaction.reference}
                  onChange={(e) => setNewTransaction({ ...newTransaction, reference: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={newTransaction.status}
                  onChange={(e) => setNewTransaction({ ...newTransaction, status: e.target.value as 'pending' | 'completed' | 'cancelled' })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowTransactionForm(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleAddTransaction}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Add Transaction
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Bulk Discount Form */}
      {showBulkDiscountForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-semibold mb-4">Add Bulk Discount</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Quantity *
                </label>
                <input
                  type="number"
                  value={newBulkDiscount.minQuantity}
                  onChange={(e) => setNewBulkDiscount({ ...newBulkDiscount, minQuantity: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Discount Percentage *
                </label>
                <input
                  type="number"
                  value={newBulkDiscount.discountPercentage}
                  onChange={(e) => setNewBulkDiscount({ ...newBulkDiscount, discountPercentage: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowBulkDiscountForm(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleAddBulkDiscountSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Add Discount
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Discount Form */}
      {showDiscountForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-semibold mb-4">Add Vendor Discount</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Discount Type *
                </label>
                <select
                  value={newDiscount.type}
                  onChange={(e) => setNewDiscount({ ...newDiscount, type: e.target.value as 'fixed' | 'percentage' | 'total' })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="percentage">Percentage Off</option>
                  <option value="fixed">Fixed Amount Off</option>
                  <option value="total">Total Amount Discount</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {newDiscount.type === 'percentage' ? 'Discount Percentage *' :
                   newDiscount.type === 'fixed' ? 'Discount Amount *' :
                   'Discount Value *'}
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={newDiscount.value}
                    onChange={(e) => setNewDiscount({ ...newDiscount, value: parseFloat(e.target.value) })}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step={newDiscount.type === 'percentage' ? '0.01' : '0.01'}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <span className="text-gray-500">
                      {newDiscount.type === 'percentage' ? '%' : 'BHD'}
                    </span>
                  </div>
                </div>
              </div>

              {newDiscount.type === 'total' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Order Amount *
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={newDiscount.minAmount || 0}
                      onChange={(e) => setNewDiscount({ ...newDiscount, minAmount: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-500">BHD</span>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={newDiscount.description}
                  onChange={(e) => setNewDiscount({ ...newDiscount, description: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={2}
                  placeholder="e.g., Special discount for bulk orders"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowDiscountForm(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setNewVendor({
                    ...newVendor,
                    discounts: [...(newVendor.discounts || []), newDiscount]
                  });
                  setShowDiscountForm(false);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Add Discount
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VendorManagement; 