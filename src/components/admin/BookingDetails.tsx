import React from 'react';
import { format, parseISO } from 'date-fns';
import { X } from 'lucide-react';
import { Booking } from '../../types';

interface BookingDetailsProps {
  booking: Booking;
  onClose: () => void;
}

const BookingDetails: React.FC<BookingDetailsProps> = ({ booking, onClose }) => {
  const formatRentalDates = () => {
    if (!booking.rentalPeriod.dates || booking.rentalPeriod.dates.length === 0) {
      return 'No dates selected';
    }

    const dates = booking.rentalPeriod.dates.map((date: string) => parseISO(date));
    const formattedDates = dates.map((date: Date) => format(date, 'd MMM yyyy'));
    
    // Group dates by month and year
    const groupedDates = dates.reduce((acc: Record<string, number[]>, date: Date) => {
      const key = format(date, 'MMM yyyy');
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(date.getDate());
      return acc;
    }, {});

    // Format each group
    return Object.entries(groupedDates)
      .map(([monthYear, days]) => {
        const sortedDays = days.sort((a: number, b: number) => a - b);
        return `${sortedDays.join(', ')} ${monthYear}`;
      })
      .join(', ');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Booking Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={24} />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* ... existing customer details ... */}

          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium mb-2">Rental Period</h3>
            <div className="space-y-2">
              <p className="text-gray-600">
                <span className="font-medium">Selected Dates:</span> {formatRentalDates()}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Total Days:</span> {booking.rentalPeriod.days}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Rental Type:</span> {booking.rentalPeriod.rentalType}
              </p>
            </div>
          </div>

          {/* ... rest of the component ... */}
        </div>
      </div>
    </div>
  );
};

export default BookingDetails; 