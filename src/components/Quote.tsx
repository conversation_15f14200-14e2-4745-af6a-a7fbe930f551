import React, { useState, useRef } from 'react';
import { FileText, Printer, Mail, ArrowLeft, Check, Download, RefreshCw, Truck, ShoppingCart } from 'lucide-react';
import { Product, ContactInfo, RentalPeriodInfo, Coupon, DeliveryOption, SystemSettings, Booking } from '../types';
import { generatePDF } from '../services/pdfGenerator';
import { formatDate, formatCurrencyTotal } from '../utils/formatters';
import { format, parseISO } from 'date-fns';

interface QuoteProps {
  quoteNumber: string;
  selectedProducts: Product[];
  contactInfo: ContactInfo;
  rentalPeriod: RentalPeriodInfo;
  appliedCoupon: Coupon | null;
  deliveryOption: DeliveryOption | null;
  systemSettings: SystemSettings;
  onNewReservation: () => void;
  onBack: () => void;
  onFinalize: () => void;
}

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: {
      startY?: number;
      head?: string[][];
      body?: string[][];
      foot?: string[][];
      theme?: string;
      styles?: {
        fontSize?: number;
        cellPadding?: number;
        cellWidth?: string;
        headStyles?: {
          fillColor?: number[];
          textColor?: number;
          fontStyle?: string;
        };
        footStyles?: {
          fillColor?: number[];
          textColor?: number;
          fontStyle?: string;
        };
      };
    }) => jsPDF;
  }
}

const Quote: React.FC<QuoteProps> = ({
  quoteNumber,
  selectedProducts,
  contactInfo,
  rentalPeriod,
  appliedCoupon,
  deliveryOption,
  systemSettings,
  onNewReservation,
  onBack,
  onFinalize
}) => {

  const [emailSent, setEmailSent] = useState(false);
  const [isFinalized, setIsFinalized] = useState(false);
  const [pdfGenerating, setPdfGenerating] = useState(false);
  const [hasFinalized, setHasFinalized] = useState(false);
  const [currentQuoteNumber, setCurrentQuoteNumber] = useState(quoteNumber);
  const [showActions, setShowActions] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const quoteRef = useRef<HTMLDivElement>(null);

  // Calculate subtotal based on rental type and custom days
  const subtotal = selectedProducts.reduce((sum, product) => {
    // Get the days for this product (either custom or global)
    const productDays = product.customDays || rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate != null ? product.temporaryDailyRate : product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate != null ? product.temporaryWeeklyRate : product.weeklyRate;

    if (rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      // Calculate weeks (rounded up)
      const weeks = Math.ceil(productDays / 7);
      return sum + (weeklyRate * weeks * product.quantity);
    } else {
      // Daily rate
      return sum + (dailyRate * productDays * product.quantity);
    }
  }, 0);



  // Calculate delivery fee
  const deliveryFee = deliveryOption ? deliveryOption.fee : 0;

  // Calculate discount if coupon is applied
  const discount = appliedCoupon ?
    (appliedCoupon.discountType === 'percentage' ?
      (subtotal * appliedCoupon.discountValue / 100) :
      appliedCoupon.discountValue) : 0;

  // Calculate tax (if enabled)
  const taxRate = systemSettings.enableTax ? systemSettings.taxRate / 100 : 0;
  const taxableAmount = subtotal + deliveryFee - discount;
  const tax = taxableAmount * taxRate;

  // Calculate total
  const total = taxableAmount + tax;

  // Format currency for rates (0 decimals)
  const formatCurrencyRate = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format currency for total (3 decimals)
  const formatCurrencyTotal = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format currency for tax (3 decimals)
  const formatCurrencyTax = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle email quote
  const handleEmailQuote = () => {
    // In a real app, this would send the quote to the customer's email
    setEmailSent(true);
    setTimeout(() => setEmailSent(false), 3000);
  };

  // Handle finalize booking
  const handleFinalize = async () => {
    // Prevent multiple finalizations
    if (hasFinalized || isProcessing) return;

    try {
      setIsProcessing(true);

      // Call the finalize function from parent component
      await onFinalize();

      // Update state to reflect booking is finalized
      setIsFinalized(true);
      setHasFinalized(true);
      setShowActions(true);
    } catch (error) {
      console.error("Error finalizing booking:", error);
      alert(`Error creating booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Calculate item total based on rental type and custom days
  const calculateItemTotal = (product: Product) => {
    // Get the days for this product (either custom or global)
    const productDays = product.customDays || rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate !== undefined ? product.temporaryDailyRate : product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate !== undefined ? product.temporaryWeeklyRate : product.weeklyRate;

    if (rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      const weeks = Math.ceil(productDays / 7);
      return weeklyRate * weeks * product.quantity;
    } else {
      return dailyRate * productDays * product.quantity;
    }
  };

  // Handle PDF generation
  const handleGeneratePDF = async (e?: React.MouseEvent) => {
    setPdfGenerating(true);

    try {
      const booking: Booking = {
        id: quoteNumber,
        quoteNumber,
        date: new Date().toISOString(),
        customer: contactInfo,
        products: selectedProducts,
        rentalPeriod,
        coupon: appliedCoupon || null,
        status: 'pending',
        delivery: deliveryOption ? {
          option: deliveryOption,
          fee: deliveryFee
        } : undefined,
        subtotal,
        deliveryFee,
        discount,
        tax,
        total,
        paidAmount: 0,
        remainingAmount: total
      };

      // Check if it's a mobile device and detect iOS specifically
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

      if (isMobile && e) {
        // Show options modal for mobile
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';

        const modalContent = document.createElement('div');
        modalContent.className = 'bg-white rounded-lg shadow-lg w-full max-w-xs p-4';

        const title = document.createElement('h3');
        title.className = 'text-lg font-bold mb-4 text-center';
        title.textContent = 'Share or Download PDF';

        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'flex flex-col space-y-3';

        // iOS Share button (only shown on iOS devices)
        let iosShareButton;
        if (isIOS) {
          iosShareButton = document.createElement('button');
          iosShareButton.className = 'flex items-center justify-center p-3 bg-blue-500 text-white rounded-md';
          iosShareButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg><span class="ml-2">iOS Share</span>';
        }

        // Regular WhatsApp button
        const whatsappButton = document.createElement('button');
        whatsappButton.className = 'flex items-center justify-center p-3 bg-green-500 text-white rounded-md';
        whatsappButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/></svg><span class="ml-2">WhatsApp + Download PDF</span>';

        // WhatsApp Business button
        const whatsappBusinessButton = document.createElement('button');
        whatsappBusinessButton.className = 'flex items-center justify-center p-3 bg-green-600 text-white rounded-md';
        whatsappBusinessButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/></svg><span class="ml-2">WhatsApp Business + PDF (Client)</span>';

        // Download button
        const downloadButton = document.createElement('button');
        downloadButton.className = 'flex items-center justify-center p-3 bg-blue-600 text-white rounded-md';
        downloadButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg><span class="ml-2">Download PDF</span>';

        // Cancel button
        const cancelButton = document.createElement('button');
        cancelButton.className = 'flex items-center justify-center p-3 bg-gray-200 text-gray-800 rounded-md';
        cancelButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg><span class="ml-2">Cancel</span>';

        // Add event listeners
        if (isIOS && iosShareButton) {
          iosShareButton.addEventListener('click', async () => {
            try {
              // Generate PDF as data URL
              const pdfDataUrl = await generatePDF({
                ...booking,
                settings: systemSettings
              }, false) as string;

              // Create a blob from the data URL
              const dataUrlParts = pdfDataUrl.split(',');
              const mimeMatch = dataUrlParts[0].match(/:(.*?);/);
              const mimeType = mimeMatch && mimeMatch[1] ? mimeMatch[1] : 'application/pdf';
              const decodedData = atob(dataUrlParts[1]);
              const uInt8Array = new Uint8Array(decodedData.length);

              for (let i = 0; i < decodedData.length; ++i) {
                uInt8Array[i] = decodedData.charCodeAt(i);
              }

              const blob = new Blob([uInt8Array], { type: mimeType });

              // Create a File object
              const file = new File([blob], `TW_${quoteNumber}.pdf`, { type: mimeType });

              // Use the Web Share API
              if (navigator.share) {
                await navigator.share({
                  title: `Quote #${quoteNumber} for ${contactInfo.name}`,
                  text: `Quote #${quoteNumber} for ${contactInfo.name}\nTotal: BHD ${total.toFixed(3)}`,
                  files: [file]
                });
              } else {
                // Fallback for browsers that don't support file sharing
                const link = document.createElement('a');
                link.href = pdfDataUrl;
                link.download = `TW_${quoteNumber}.pdf`;
                link.click();

                alert('Your browser does not support the Share API. The PDF has been downloaded instead.');
              }

              // Remove the modal
              document.body.removeChild(modal);
              setPdfGenerating(false);
            } catch (error) {
              console.error('Error using Share API:', error);

              // Fallback to download if sharing fails
              try {
                await generatePDF({
                  ...booking,
                  settings: systemSettings
                });

                // Remove the modal
                document.body.removeChild(modal);
                setPdfGenerating(false);
              } catch (downloadError) {
                console.error('Error downloading PDF:', downloadError);
                setPdfGenerating(false);
              }
            }
          });
        }

        whatsappButton.addEventListener('click', async () => {
          try {
            // Generate PDF as data URL
            const pdfDataUrl = await generatePDF({
              ...booking,
              settings: systemSettings
            }, false) as string;

            // Create a regular WhatsApp share link with the booking details
            const message = `Quote #${quoteNumber} for ${contactInfo.name}\nTotal: BHD ${total.toFixed(3)}\n\nThe PDF has been downloaded to your device. Please send it as a separate attachment after this message.`;

            // For regular WhatsApp, always open without a specific recipient
            // This allows the user to select any contact to share with
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

            // Open WhatsApp in a new tab
            window.open(whatsappUrl, '_blank');

            // Also download the PDF
            const link = document.createElement('a');
            link.href = pdfDataUrl;
            link.download = `TW_${quoteNumber}.pdf`;
            link.click();

            // Remove the modal
            document.body.removeChild(modal);
            setPdfGenerating(false);
          } catch (error) {
            console.error('Error sharing via WhatsApp:', error);
            setPdfGenerating(false);
          }
        });

        whatsappBusinessButton.addEventListener('click', async () => {
          try {
            // Generate PDF as data URL
            const pdfDataUrl = await generatePDF({
              ...booking,
              settings: systemSettings
            }, false) as string;

            // Create a WhatsApp Business share link with the booking details
            const message = `Quote #${quoteNumber} for ${contactInfo.name}\nTotal: BHD ${total.toFixed(3)}\n\nThe PDF has been downloaded to your device. Please send it as a separate attachment after this message.`;

            // Use client's phone number if available, otherwise open without a specific recipient
            let whatsappUrl = '';
            if (contactInfo.phone && contactInfo.phone.trim() !== '') {
              // Format the phone number (remove spaces, dashes, etc.)
              let phoneNumber = contactInfo.phone.replace(/[\s-\(\)]/g, '');

              // Add country code if not present
              if (!phoneNumber.startsWith('+')) {
                // Assume Bahrain (+973) if no country code
                if (!phoneNumber.startsWith('973')) {
                  phoneNumber = '973' + phoneNumber;
                }
                phoneNumber = '+' + phoneNumber;
              }

              whatsappUrl = `https://api.whatsapp.com/send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;
            } else {
              whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(message)}`;
            }

            // Open WhatsApp Business in a new tab
            window.open(whatsappUrl, '_blank');

            // Also download the PDF
            const link = document.createElement('a');
            link.href = pdfDataUrl;
            link.download = `TW_${quoteNumber}.pdf`;
            link.click();

            // Remove the modal
            document.body.removeChild(modal);
            setPdfGenerating(false);
          } catch (error) {
            console.error('Error sharing via WhatsApp Business:', error);
            setPdfGenerating(false);
          }
        });

        downloadButton.addEventListener('click', async () => {
          try {
            // Generate and download PDF
            await generatePDF({
              ...booking,
              settings: systemSettings
            });

            // Remove the modal
            document.body.removeChild(modal);
            setPdfGenerating(false);
          } catch (error) {
            console.error('Error downloading PDF:', error);
            setPdfGenerating(false);
          }
        });

        cancelButton.addEventListener('click', () => {
          document.body.removeChild(modal);
          setPdfGenerating(false);
        });

        // Assemble and show the modal
        if (isIOS && iosShareButton) {
          buttonsContainer.appendChild(iosShareButton);
        }
        buttonsContainer.appendChild(whatsappButton);
        buttonsContainer.appendChild(whatsappBusinessButton);
        buttonsContainer.appendChild(downloadButton);
        buttonsContainer.appendChild(cancelButton);

        modalContent.appendChild(title);
        modalContent.appendChild(buttonsContainer);

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
      } else {
        // Desktop: just generate and download the PDF
        await generatePDF({
          ...booking,
          settings: systemSettings
        });
        setPdfGenerating(false);
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      setPdfGenerating(false);
    }
  };

  const formatRentalDates = (dates: string[]) => {
    if (!dates || dates.length === 0) return 'No dates selected';

    const parsedDates = dates.map(date => parseISO(date));
    const groupedDates = parsedDates.reduce((acc: Record<string, number[]>, date: Date) => {
      const key = format(date, 'MMM yyyy');
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(date.getDate());
      return acc;
    }, {});

    return Object.entries(groupedDates)
      .map(([monthYear, days]) => {
        const sortedDays = days.sort((a, b) => a - b);
        return `${sortedDays.join(', ')} ${monthYear}`;
      })
      .join(', ');
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <FileText size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div className="flex-1">
            <span className="font-medium">
              {isFinalized ? `Quote #${quoteNumber}` : 'Quote Preview'}
            </span>
            <div className="text-blue-600 font-bold">
              Total: {formatCurrencyTotal(total)}
            </div>
            {isFinalized && showActions && (
              <div className="flex mt-2 space-x-2">
                <button
                  onClick={() => window.print()}
                  className="p-2 bg-white rounded-md hover:bg-gray-100 border border-gray-300"
                  title="Print Quote"
                >
                  <Printer size={18} />
                </button>
                <button
                  onClick={handleEmailQuote}
                  className={`p-2 rounded-md border ${
                    emailSent
                      ? 'bg-green-100 border-green-300 text-green-600'
                      : 'bg-white hover:bg-gray-100 border-gray-300'
                  }`}
                  title="Email Quote"
                >
                  {emailSent ? <Check size={18} /> : <Mail size={18} />}
                </button>
                <button
                  onClick={(e) => handleGeneratePDF(e)}
                  className="p-2 rounded-md border bg-white hover:bg-gray-100 border-gray-300"
                  title="Download PDF"
                >
                  <Download size={18} />
                </button>
              </div>
            )}
          </div>
        </div>
        {!isFinalized ? (
          <button
            onClick={handleFinalize}
            disabled={isProcessing}
            className={`px-4 py-2 rounded-md font-medium w-full sm:w-auto ${
              isProcessing
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700'
            } text-white transition-colors`}
          >
            {isProcessing ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Confirm Booking'
            )}
          </button>
        ) : (
          <button
            onClick={onNewReservation}
            className="px-4 py-2 rounded-md font-medium w-full sm:w-auto bg-blue-600 text-white hover:bg-blue-700 transition-colors"
          >
            New Reservation
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div ref={quoteRef} className="pb-20">
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
          disabled={isFinalized}
        >
          <ArrowLeft size={20} />
        </button>
        <h2 className="text-2xl font-bold">Rental Quote</h2>
      </div>

      <div className="bg-blue-50 p-4 rounded-md mb-6">
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center">
              <FileText size={20} className="text-blue-600 mr-2" />
              <h3 className="font-bold text-lg">
                {isFinalized ? `Quote #${quoteNumber}` : 'Quote Preview'}
              </h3>
            </div>
            <p className="text-gray-600 text-sm mt-1">Generated on {new Date().toLocaleDateString()}</p>
          </div>
          {isFinalized && (
            <div className="flex space-x-2">
              <button
                onClick={() => window.print()}
                className="p-2 bg-white rounded-md hover:bg-gray-100 border border-gray-300"
                title="Print Quote"
              >
                <Printer size={18} />
              </button>
              <button
                onClick={handleEmailQuote}
                className={`p-2 rounded-md border ${
                  emailSent
                    ? 'bg-green-100 border-green-300 text-green-600'
                    : 'bg-white hover:bg-gray-100 border-gray-300'
                }`}
                title="Email Quote"
              >
                {emailSent ? <Check size={18} /> : <Mail size={18} />}
              </button>
              <button
                onClick={handleGeneratePDF}
                className={`p-2 rounded-md border ${
                  pdfGenerating
                    ? 'bg-blue-100 border-blue-300 text-blue-600 cursor-wait'
                    : 'bg-white hover:bg-gray-100 border-gray-300'
                }`}
                title="Download PDF"
                disabled={pdfGenerating}
              >
                <Download size={18} />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Customer Information */}
      <div className="mb-6">
        <h3 className="font-bold text-gray-700 mb-2">Customer Information</h3>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          <p className="font-medium">{contactInfo.name}</p>
          <p className="text-gray-600">{contactInfo.email}</p>
          <p className="text-gray-600">{contactInfo.phone}</p>
          <p className="text-gray-600">{contactInfo.address}</p>
        </div>
      </div>

      {/* Rental Period */}
      <div className="mb-6">
        <h3 className="font-bold text-gray-700 mb-2">Rental Period</h3>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          <div className="flex items-center mb-2">
            <RefreshCw size={16} className="text-blue-600 mr-2" />
            <span className="font-medium capitalize">{rentalPeriod.rentalType} Rental</span>
          </div>
          <div className="grid grid-cols-1 gap-4 mt-3">
            <div>
              <p className="text-sm text-gray-500">Selected Dates</p>
              <p className="font-medium">{formatRentalDates(rentalPeriod.dates)}</p>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-100">
            <p className="text-sm text-gray-500">Total Days</p>
            <p className="font-medium">
              {rentalPeriod.days} {rentalPeriod.days === 1 ? 'day' : 'days'}
              {rentalPeriod.rentalType === 'weekly' && rentalPeriod.days >= 7 && (
                <span className="text-gray-500 ml-2">
                  ({Math.ceil(rentalPeriod.days / 7)} {Math.ceil(rentalPeriod.days / 7) === 1 ? 'week' : 'weeks'})
                </span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Delivery Method */}
      {deliveryOption && (
        <div className="mb-6">
          <h3 className="font-bold text-gray-700 mb-2">Delivery Method</h3>
          <div className="bg-white border border-gray-200 rounded-md p-4">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center">
                  <Truck size={16} className="text-blue-600 mr-2" />
                  <p className="font-medium">{deliveryOption.name}</p>
                </div>
                <p className="text-gray-600 mt-1">{deliveryOption.description}</p>
              </div>
              <div>
                {deliveryOption.fee > 0 ? (
                  <span className="font-bold">{formatCurrencyRate(deliveryOption.fee)}</span>
                ) : (
                  <span className="text-green-600 font-medium">Free</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Selected Equipment */}
      <div className="mb-6">
        <h3 className="font-bold text-gray-700 mb-2">Selected Equipment</h3>
        <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    {rentalPeriod.rentalType === 'weekly' ? 'Weekly Rate' : 'Daily Rate'}
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Quantity</th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">
                    Duration
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Total</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {selectedProducts.map(product => {
                  const productDays = product.customDays || rentalPeriod.days;

                  return (
                  <tr key={product.id}>
                    <td className="px-4 py-3">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden mr-3">
                          <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                        </div>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-xs text-gray-500 capitalize">{product.category}</p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-right">
                      {rentalPeriod.rentalType === 'weekly' && product.weeklyRate
                        ? formatCurrencyRate(product.temporaryWeeklyRate || product.weeklyRate)
                        : formatCurrencyRate(product.temporaryDailyRate || product.dailyRate)}
                    </td>
                    <td className="px-4 py-3 text-center">{product.quantity}</td>
                    <td className="px-4 py-3 text-center">
                      {productDays} {productDays === 1 ? 'day' : 'days'}
                      {rentalPeriod.rentalType === 'weekly' && productDays >= 7 && (
                        <span className="text-gray-500 ml-1">
                          ({Math.ceil(productDays / 7)} {Math.ceil(productDays / 7) === 1 ? 'week' : 'weeks'})
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-right font-medium">
                      {formatCurrencyRate(calculateItemTotal(product))}
                    </td>
                  </tr>
                )})}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Quote Summary */}
      <div className="mb-6">
        <h3 className="font-bold text-gray-700 mb-2">Quote Summary</h3>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal</span>
              <span>{formatCurrencyRate(subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Delivery Fee</span>
              <span>{formatCurrencyRate(deliveryFee)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount {appliedCoupon && `(${appliedCoupon.code})`}</span>
                <span>-{formatCurrencyRate(discount)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">VAT ({systemSettings.enableTax ? `${systemSettings.taxRate}%` : '0%'})</span>
              <span>{formatCurrencyTax(tax)}</span>
            </div>
            <div className="border-t border-gray-200 pt-2 mt-2">
              <div className="flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{formatCurrencyTotal(total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="mb-8">
        <h3 className="font-bold text-gray-700 mb-2">Terms and Conditions</h3>
        <div className="bg-white border border-gray-200 rounded-md p-4 text-sm text-gray-600">
          <p className="mb-2">This quote is valid for 7 days from the date of issue.</p>
          <p className="mb-2">A 50% deposit is required to confirm your reservation.</p>
          <p className="mb-2">Cancellations must be made at least 48 hours in advance for a full refund.</p>
          <p className="mb-2">Equipment must be returned in the same condition as received.</p>
          <p>Additional charges may apply for damaged or late equipment returns.</p>
        </div>
      </div>

      {/* Actions */}
      {!isFinalized ? (
        <div className="flex justify-between mb-20">
          <button
            onClick={onBack}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Edit Reservation
          </button>
          <button
            onClick={onNewReservation}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            New Reservation
          </button>
        </div>
      ) : (
        <div className="w-full mb-20">
          <div className="bg-green-100 border border-green-300 text-green-700 px-4 py-3 rounded-md mb-4">
            <div className="flex items-center">
              <Check size={18} className="mr-2" />
              <span className="font-medium">Booking confirmed! Your reservation has been submitted.</span>
            </div>
            <p className="mt-1 text-sm">A confirmation email has been sent to {contactInfo.email}</p>
          </div>
        </div>
      )}

      {/* Email Sent Notification */}
      {emailSent && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-300 text-green-700 px-4 py-3 rounded-md shadow-md flex items-center">
          <Check size={18} className="mr-2" />
          Quote sent to {contactInfo.email}
        </div>
      )}

      {/* Fixed Cart Summary */}
      <CartSummary />
    </div>
  );
};

export default Quote;