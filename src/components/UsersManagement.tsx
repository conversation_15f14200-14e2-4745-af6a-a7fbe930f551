import React, { useState, useEffect } from 'react';
import { User, UserRole } from '../types';
import { getUsers, updateUserLastLogin } from '../services/database';
import { createUser, updateUserProfile, deleteUserAccount } from '../services/userManagement';
import { Plus, Pencil, Trash2, X, Check, Shield, ShieldOff, User as UserIcon, AtSign } from 'lucide-react';

interface UsersManagementProps {
  initialUsers: User[];
  onUserUpdate: (users: User[]) => void;
  currentUser: User;
}

const UsersManagement: React.FC<UsersManagementProps> = ({
  initialUsers,
  onUserUpdate,
  currentUser
}) => {
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(initialUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<UserRole>('manager');
  const [isActive, setIsActive] = useState(true);
  const [formError, setFormError] = useState('');

  useEffect(() => {
    setFilteredUsers(
      users.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.username.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [users, searchTerm]);

  const resetForm = () => {
    setName('');
    setEmail('');
    setUsername('');
    setPassword('');
    setRole('manager');
    setIsActive(true);
    setFormError('');
  };

  const handleAddClick = () => {
    resetForm();
    setIsAddingUser(true);
    setSelectedUser(null);
  };

  const handleEditClick = (user: User) => {
    setIsAddingUser(false);
    setSelectedUser(user);
    setName(user.name);
    setEmail(user.email);
    setUsername(user.username);
    setPassword(''); // Don't show existing password
    setRole(user.role);
    setIsActive(user.isActive);
    setFormError('');
  };

  const handleCancelEdit = () => {
    setSelectedUser(null);
    resetForm();
  };

  const validateForm = (): boolean => {
    if (!name.trim()) {
      setFormError('Name is required');
      return false;
    }

    if (!email.trim()) {
      setFormError('Email is required');
      return false;
    }

    if (!username.trim()) {
      setFormError('Username is required');
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setFormError('Please enter a valid email address');
      return false;
    }

    // Password validation for new users
    if (isAddingUser && (!password || password.length < 6)) {
      setFormError('Password must be at least 6 characters');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Create new user
      if (isAddingUser) {
        // Show confirmation dialog
        const confirmed = window.confirm(
          'Creating a new user will sign you out and redirect to the login page. ' +
          'Make sure to remember your admin credentials. Continue?'
        );

        if (!confirmed) {
          return;
        }

        // Create user in Firebase Authentication and Firestore
        await createUser(
          email,
          password,
          {
            name,
            username,
            role
          }
        );

        // The createUser function will redirect to the login page
      }
      // Update existing user
      else if (selectedUser) {
        // Update user in Firestore
        await updateUserProfile(selectedUser.id, {
          name,
          email,
          username,
          role,
          isActive
        });

        // Update local state
        const updatedUsers = users.map(user =>
          user.id === selectedUser.id ? { ...user, name, email, username, role, isActive } : user
        );

        setUsers(updatedUsers);
        onUserUpdate(updatedUsers);

        // Reset form
        setSelectedUser(null);
        resetForm();
      }
    } catch (error) {
      console.error('Error saving user:', error);
      setFormError(error instanceof Error ? error.message : 'An error occurred');
    }
  };

  const handleDeleteClick = (user: User) => {
    setSelectedUser(user);
    setShowDeleteConfirmation(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;

    try {
      // Cannot delete yourself
      if (selectedUser.id === currentUser.id) {
        setFormError("You cannot delete your own account");
        return;
      }

      await deleteUserAccount(selectedUser.id);

      // Update local state
      const updatedUsers = users.filter(user => user.id !== selectedUser.id);
      setUsers(updatedUsers);
      onUserUpdate(updatedUsers);

      // Close modal and reset selection
      setShowDeleteConfirmation(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
      setFormError(error instanceof Error ? error.message : 'An error occurred');
    }
  };

  const handleToggleActive = async (user: User) => {
    // Cannot deactivate yourself
    if (user.id === currentUser.id) {
      setFormError("You cannot deactivate your own account");
      return;
    }

    try {
      const updatedUser = {
        ...user,
        isActive: !user.isActive
      };

      await updateUserProfile(user.id, { isActive: !user.isActive });

      // Update local state
      const updatedUsers = users.map(u =>
        u.id === user.id ? updatedUser : u
      );

      setUsers(updatedUsers);
      onUserUpdate(updatedUsers);
    } catch (error) {
      console.error('Error toggling user active status:', error);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">User Management</h2>
        <button
          onClick={handleAddClick}
          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
        >
          <Plus size={18} className="mr-1" /> Add User
        </button>
      </div>

      <div className="mb-4">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search users..."
          className="w-full p-2 border border-gray-300 rounded-md"
        />
      </div>

      {formError && (
        <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md">
          {formError}
        </div>
      )}

      {/* User Edit/Add Form */}
      {(selectedUser || isAddingUser) && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-4">
          <h3 className="font-medium mb-3">
            {isAddingUser ? 'Add New User' : 'Edit User'}
          </h3>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password {!isAddingUser && '(Leave blank to keep unchanged)'}</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required={isAddingUser}
                  placeholder={isAddingUser ? '' : '••••••••'}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                <select
                  value={role}
                  onChange={(e) => setRole(e.target.value as UserRole)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                </select>
              </div>
            </div>
            <div className="mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="mr-2"
                />
                <span>Active</span>
              </label>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {isAddingUser ? 'Add User' : 'Update User'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-md overflow-hidden border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Username
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsers.map(user => (
              <tr key={user.id} className={user.id === currentUser.id ? 'bg-blue-50' : ''}>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    <UserIcon size={16} className="mr-2 text-gray-500" />
                    <span className="font-medium">{user.name}</span>
                    {user.id === currentUser.id && (
                      <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">You</span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    <AtSign size={16} className="mr-2 text-gray-500" />
                    <span>{user.username}</span>
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">{user.email}</td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    {user.role === 'admin' ? (
                      <Shield size={16} className="mr-1 text-blue-600" />
                    ) : (
                      <UserIcon size={16} className="mr-1 text-gray-600" />
                    )}
                    <span className="capitalize">{user.role}</span>
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  {user.isActive ? (
                    <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  ) : (
                    <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                      Inactive
                    </span>
                  )}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-center">
                  <div className="flex space-x-2 justify-center">
                    <button
                      onClick={() => handleToggleActive(user)}
                      className={`p-1 rounded-full ${
                        user.isActive
                          ? 'text-green-600 hover:bg-green-100'
                          : 'text-red-600 hover:bg-red-100'
                      }`}
                      title={user.isActive ? 'Deactivate' : 'Activate'}
                      disabled={user.id === currentUser.id}
                    >
                      {user.isActive ? <ShieldOff size={16} /> : <Shield size={16} />}
                    </button>
                    <button
                      onClick={() => handleEditClick(user)}
                      className="p-1 rounded-full text-blue-600 hover:bg-blue-100"
                      title="Edit"
                    >
                      <Pencil size={16} />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(user)}
                      className="p-1 rounded-full text-red-600 hover:bg-red-100"
                      title="Delete"
                      disabled={user.id === currentUser.id}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            {filteredUsers.length === 0 && (
              <tr>
                <td colSpan={6} className="px-4 py-3 text-center text-gray-500">
                  No users found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-3">Confirm Delete</h3>
            <p className="text-gray-600 mb-4">
              Are you sure you want to delete the user <strong>{selectedUser.name}</strong>? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsersManagement;