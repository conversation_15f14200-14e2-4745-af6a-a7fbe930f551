import React, { useState, useEffect } from 'react';
import { Search, Filter, ShoppingCart, Plus, Minus, Grid, List, Star, ChevronLeft, ChevronRight, DollarSign, X, Check } from 'lucide-react';
import { Product } from '../types';

interface ProductListProps {
  products: Product[];
  onProductSelect: (products: Product[]) => void;
}

const ProductList: React.FC<ProductListProps> = ({ products, onProductSelect }) => {
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingPrice, setEditingPrice] = useState<string | null>(null);
  const [temporaryPrice, setTemporaryPrice] = useState<string>('');
  const itemsPerPage = 10;
  
  const categories = ['all', ...Array.from(new Set(products.map(product => product.category)))];
  
  // Sort products to show featured items first
  const sortedProducts = [...products].sort((a, b) => {
    if (a.featured && !b.featured) return -1;
    if (!a.featured && b.featured) return 1;
    return 0;
  });
  
  // Get filtered products
  const filteredProducts = sortedProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    return matchesSearch && matchesCategory && product.available;
  });
  
  // Get paginated products
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // Calculate total pages
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };
  
  const handleProductToggle = (product: Product) => {
    const existingProduct = selectedProducts.find(p => p.id === product.id);

    if (existingProduct) {
      // Remove product if it exists
      setSelectedProducts(selectedProducts.filter(p => p.id !== product.id));
    } else {
      // Add product with quantity 1
      setSelectedProducts([...selectedProducts, {...product, quantity: 1}]);
    }
  };
  
  const handleQuantityChange = (productId: string, change: number) => {
    setSelectedProducts(prev => prev.map(product => {
      if (product.id === productId) {
        const newQuantity = Math.max(1, Math.min(product.quantity + change, product.stock));
        return { ...product, quantity: newQuantity };
      }
      return product;
    }));
  };

  const handleTemporaryPriceChange = (productId: string, price: string) => {
    const numericPrice = parseFloat(price);
    if (!isNaN(numericPrice)) {
      setSelectedProducts(prev => prev.map(product => {
        if (product.id === productId) {
          return { ...product, temporaryDailyRate: numericPrice };
        }
        return product;
      }));
    }
  };

  const handleClearTemporaryPrice = (productId: string) => {
    setSelectedProducts(prev => prev.map(product => {
      if (product.id === productId) {
        return { ...product, temporaryDailyRate: undefined };
      }
      return product;
    }));
  };
  
  const handleContinue = () => {
    if (selectedProducts.length > 0) {
      // Scroll to top before navigating
      window.scrollTo(0, 0);
      onProductSelect(selectedProducts);
    }
  };

  // Format currency for rates (0 decimals)
  const formatCurrencyRate = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format currency for total (3 decimals)
  const formatCurrencyTotal = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Calculate total price
  const calculateTotal = () => {
    return selectedProducts.reduce((sum, product) => {
      const rate = product.temporaryDailyRate || product.dailyRate;
      return sum + (rate * product.quantity);
    }, 0);
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <ShoppingCart size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div>
            <span className="font-medium">
              {selectedProducts.length} {selectedProducts.length === 1 ? 'item' : 'items'} selected
              {selectedProducts.length > 0 && ` (${selectedProducts.reduce((sum, p) => sum + p.quantity, 0)} total units)`}
            </span>
            {selectedProducts.length > 0 && (
              <div className="text-blue-600 font-bold">
                Total: {formatCurrencyTotal(calculateTotal())}
              </div>
            )}
          </div>
        </div>
        <button
          onClick={handleContinue}
          disabled={selectedProducts.length === 0}
          className={`px-4 py-2 rounded-md font-medium w-full sm:w-auto ${
            selectedProducts.length > 0
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          } transition-colors`}
        >
          Continue
        </button>
      </div>
    </div>
  );
  
  return (
    <div className="pb-20">
      <h2 className="text-2xl font-bold mb-6">Select Equipment</h2>
      
      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search equipment..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex space-x-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex border border-gray-300 rounded-md overflow-hidden">
            <button 
              className={`p-2 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'bg-white text-gray-600'}`}
              onClick={() => setViewMode('grid')}
              aria-label="Grid View"
            >
              <Grid size={18} />
            </button>
            <button 
              className={`p-2 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'bg-white text-gray-600'}`}
              onClick={() => setViewMode('list')}
              aria-label="List View"
            >
              <List size={18} />
            </button>
          </div>
        </div>
      </div>
      
      {/* Products Grid/List */}
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {paginatedProducts.map(product => {
            const isSelected = selectedProducts.some(p => p.id === product.id);
            const selectedProduct = selectedProducts.find(p => p.id === product.id);
            
            return (
              <div 
                key={product.id} 
                className={`border rounded-lg overflow-hidden ${isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'}`}
              >
                <div className="h-48 overflow-hidden relative">
                  <img 
                    src={product.image} 
                    alt={product.name} 
                    className="w-full h-full object-cover"
                  />
                  {product.featured && (
                    <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
                      <Star size={12} className="mr-1" />
                      Featured
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-lg">{product.name}</h3>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full capitalize">
                      {product.category}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
                  <div className="flex justify-between items-center mb-3">
                    <div>
                      {selectedProduct?.temporaryDailyRate ? (
                        <div>
                          <span className="font-bold text-lg line-through text-gray-400">
                            {formatCurrencyRate(product.dailyRate)}/day
                          </span>
                          <span className="font-bold text-lg text-green-600 ml-2">
                            {formatCurrencyRate(selectedProduct.temporaryDailyRate)}/day
                          </span>
                        </div>
                      ) : (
                        <span className="font-bold text-lg">{formatCurrencyRate(product.dailyRate)}/day</span>
                      )}
                      {product.weeklyRate && (
                        <span className="text-sm text-gray-600 ml-2">{formatCurrencyRate(product.weeklyRate)}/week</span>
                      )}
                    </div>
                  </div>
                  
                  {isSelected ? (
                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Quantity:</span>
                        <div className="flex items-center">
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              handleQuantityChange(product.id, -1);
                            }}
                            className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                            disabled={selectedProduct?.quantity === 1}
                          >
                            <Minus size={16} />
                          </button>
                          <span className="mx-3 font-medium">{selectedProduct?.quantity}</span>
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              handleQuantityChange(product.id, 1);
                            }}
                            className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                            disabled={selectedProduct?.quantity === product.stock}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                      </div>

                      {/* Temporary Price Input */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Temporary Price:</span>
                        <div className="flex items-center">
                          {editingPrice === product.id ? (
                            <div className="flex items-center">
                              <div className="relative">
                                <DollarSign size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                  type="number"
                                  value={temporaryPrice}
                                  onChange={(e) => setTemporaryPrice(e.target.value)}
                                  className="pl-8 pr-2 py-1 w-24 border border-gray-300 rounded-md text-sm"
                                  placeholder="0.00"
                                  min="0"
                                  
                                  step="0.01"
                                />
                              </div>
                              <button
                                onClick={() => {
                                  handleTemporaryPriceChange(product.id, temporaryPrice);
                                  setEditingPrice(null);
                                  setTemporaryPrice('');
                                }}
                                className="ml-2 p-1 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
                              >
                                <Check size={16} />
                              </button>
                              <button
                                onClick={() => {
                                  setEditingPrice(null);
                                  setTemporaryPrice('');
                                }}
                                className="ml-1 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                              >
                                <X size={16} />
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <button
                                onClick={() => {
                                  setEditingPrice(product.id);
                                  setTemporaryPrice(selectedProduct?.temporaryDailyRate?.toString() || '');
                                }}
                                className="p-1 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-600"
                              >
                                <DollarSign size={16} />
                              </button>
                              {selectedProduct?.temporaryDailyRate && (
                                <button
                                  onClick={() => handleClearTemporaryPrice(product.id)}
                                  className="ml-1 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                                >
                                  <X size={16} />
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      <button
                        onClick={() => handleProductToggle(product)}
                        className="w-full py-2 bg-red-100 text-red-600 rounded-md font-medium hover:bg-red-200 transition-colors"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => handleProductToggle(product)}
                      className="w-full py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 transition-colors"
                    >
                      Select
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
      
      {/* List View - Optimized for mobile */}
      {viewMode === 'list' && (
        <div className="border rounded-md overflow-hidden">
          <div className="overflow-x-visible">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                    Rates
                  </th>
                  <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {paginatedProducts.map(product => {
                  const isSelected = selectedProducts.some(p => p.id === product.id);
                  const selectedProduct = selectedProducts.find(p => p.id === product.id);
                  
                  return (
                    <tr key={`list-${product.id}`} className={isSelected ? 'bg-blue-50' : ''}>
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden mr-3 relative">
                            <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                            {product.featured && (
                              <div className="absolute top-0 right-0 bg-yellow-500 w-4 h-4 flex items-center justify-center rounded-full">
                                <Star size={8} className="text-white" />
                              </div>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{product.name}</p>
                            <p className="text-xs text-gray-500 max-w-md hidden md:block">{product.description}</p>
                            <div className="flex items-center mt-1">
                              {selectedProduct?.temporaryDailyRate ? (
                                <div className="flex items-center">
                                  <span className="text-sm font-medium line-through text-gray-400">
                                    {formatCurrencyRate(product.dailyRate)}/day
                                  </span>
                                  <span className="text-sm font-medium text-green-600 ml-2">
                                    {formatCurrencyRate(selectedProduct.temporaryDailyRate)}/day
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm font-medium md:hidden">
                                  {formatCurrencyRate(product.dailyRate)}/day
                                </span>
                              )}
                              <span className="px-2 ml-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 capitalize">
                                {product.category}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium hidden md:table-cell">
                        {selectedProduct?.temporaryDailyRate ? (
                          <div>
                            <div className="line-through text-gray-400">{formatCurrencyRate(product.dailyRate)}/day</div>
                            <div className="text-green-600">{formatCurrencyRate(selectedProduct.temporaryDailyRate)}/day</div>
                          </div>
                        ) : (
                          <div>
                            <div>{formatCurrencyRate(product.dailyRate)}/day</div>
                            {product.weeklyRate && (
                              <div className="text-gray-500">{formatCurrencyRate(product.weeklyRate)}/week</div>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        {isSelected ? (
                          <div className="flex flex-col items-center">
                            <div className="flex items-center justify-center mb-2">
                              <button 
                                onClick={() => handleQuantityChange(product.id, -1)}
                                className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                                disabled={selectedProduct?.quantity === 1}
                              >
                                <Minus size={16} />
                              </button>
                              <span className="mx-3 font-medium">{selectedProduct?.quantity}</span>
                              <button 
                                onClick={() => handleQuantityChange(product.id, 1)}
                                className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                                disabled={selectedProduct?.quantity === product.stock}
                              >
                                <Plus size={16} />
                              </button>
                            </div>

                            {/* Temporary Price Controls */}
                            <div className="flex items-center justify-center mb-2">
                              {editingPrice === product.id ? (
                                <div className="flex items-center">
                                  <div className="relative">
                                    <DollarSign size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                    <input
                                      type="number"
                                      value={temporaryPrice}
                                      onChange={(e) => setTemporaryPrice(e.target.value)}
                                      className="pl-8 pr-2 py-1 w-24 border border-gray-300 rounded-md text-sm"
                                      placeholder="0.00"
                                      min="0"
                                      step="0.01"
                                    />
                                  </div>
                                  <button
                                    onClick={() => {
                                      handleTemporaryPriceChange(product.id, temporaryPrice);
                                      setEditingPrice(null);
                                      setTemporaryPrice('');
                                    }}
                                    className="ml-2 p-1 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
                                  >
                                    <Check size={16} />
                                  </button>
                                  <button
                                    onClick={() => {
                                      setEditingPrice(null);
                                      setTemporaryPrice('');
                                    }}
                                    className="ml-1 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                                  >
                                    <X size={16} />
                                  </button>
                                </div>
                              ) : (
                                <div className="flex items-center">
                                  <button
                                    onClick={() => {
                                      setEditingPrice(product.id);
                                      setTemporaryPrice(selectedProduct?.temporaryDailyRate?.toString() || '');
                                    }}
                                    className="p-1 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-600"
                                  >
                                    <DollarSign size={16} />
                                  </button>
                                  {selectedProduct?.temporaryDailyRate && (
                                    <button
                                      onClick={() => handleClearTemporaryPrice(product.id)}
                                      className="ml-1 p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                                    >
                                      <X size={16} />
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>

                            <button
                              onClick={() => handleProductToggle(product)}
                              className="px-3 py-1 rounded text-sm font-medium bg-red-100 text-red-600 hover:bg-red-200"
                            >
                              Remove
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleProductToggle(product)}
                            className="px-3 py-1 rounded text-sm font-medium bg-blue-600 text-white hover:bg-blue-700"
                          >
                            Select
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`p-2 rounded-md ${
              currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronLeft size={20} />
          </button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 rounded-md ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`p-2 rounded-md ${
              currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronRight size={20} />
          </button>
        </div>
      )}
      
      {filteredProducts.length === 0 && (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500">No equipment found matching your criteria.</p>
        </div>
      )}
      
      {/* Fixed Cart Summary */}
      <CartSummary />
    </div>
  );
};

export default ProductList;