import React, { useState, useEffect } from 'react';
import { ArrowLeft, ShoppingCart, User, Calendar, Tag, Truck, Trash2, Plus, Minus, RefreshCw, Clock, Check, X, Percent } from 'lucide-react';
import QuoteDiscountModal from './modals/QuoteDiscountModal';
import { Product, ContactInfo, RentalPeriodInfo, Coupon, DeliveryOption, SystemSettings } from '../types';
import { format, parseISO, isValid } from 'date-fns';

interface ReviewBookingProps {
  selectedProducts: Product[];
  contactInfo: ContactInfo;
  rentalPeriod: RentalPeriodInfo;
  couponCode: string;
  appliedCoupon: Coupon | null;
  deliveryOption: DeliveryOption | null;
  systemSettings: SystemSettings;
  onCouponCodeChange: (code: string) => void;
  onCouponApply: (code: string, customCoupon?: Coupon) => boolean;
  onProductsUpdate: (products: Product[]) => void;
  onSubmit: () => void;
  onBack: () => void;
}

const ReviewBooking: React.FC<ReviewBookingProps> = ({
  selectedProducts,
  contactInfo,
  rentalPeriod,
  couponCode,
  appliedCoupon,
  deliveryOption,
  systemSettings,
  onCouponCodeChange,
  onCouponApply,
  onProductsUpdate,
  onSubmit,
  onBack
}) => {
  const [couponError, setCouponError] = useState('');
  const [couponSuccess, setCouponSuccess] = useState('');
  const [editingProductId, setEditingProductId] = useState<string | null>(null);
  const [customDays, setCustomDays] = useState<number>(0);
  const [showDiscountModal, setShowDiscountModal] = useState(false);

  // Recalculate totals whenever products, discounts, or delivery changes
  useEffect(() => {
    calculateTotals();
  }, [selectedProducts, appliedCoupon, deliveryOption]);

  const calculateTotals = () => {
    // Calculate subtotal based on rental type and custom days
    const newSubtotal = selectedProducts.reduce((sum, product) => {
      const productDays = product.customDays || rentalPeriod.days;

      if (rentalPeriod.rentalType === 'weekly' && product.weeklyRate) {
        const weeks = Math.ceil(productDays / 7);
        return sum + (product.weeklyRate * weeks * product.quantity);
      } else {
        return sum + (product.dailyRate * productDays * product.quantity);
      }
    }, 0);

    // Calculate delivery fee
    const newDeliveryFee = deliveryOption ? deliveryOption.fee : 0;

    // Calculate discount
    const newDiscount = appliedCoupon ?
      (appliedCoupon.discountType === 'percentage' ?
        (newSubtotal * appliedCoupon.discountValue / 100) :
        appliedCoupon.discountValue) : 0;

    // Calculate tax
    const newTaxableAmount = newSubtotal + newDeliveryFee - newDiscount;
    const newTax = newTaxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);

    // Update state
    setSubtotal(newSubtotal);
    setDeliveryFee(newDeliveryFee);
    setDiscount(newDiscount);
    setTax(newTax);
    setTotal(newTaxableAmount + newTax);
  };

  // Add state for all calculated values
  const [subtotal, setSubtotal] = useState(0);
  const [deliveryFee, setDeliveryFee] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [total, setTotal] = useState(0);

  // Format currency for rates (0 decimals)
  const formatCurrencyRate = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format currency for total (3 decimals)
  const formatCurrencyTotal = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format currency for tax (3 decimals)
  const formatCurrencyTax = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleCouponApply = () => {
    if (!couponCode.trim()) {
      setCouponError('Please enter a coupon code');
      setCouponSuccess('');
      return;
    }

    const success = onCouponApply(couponCode);
    if (success) {
      setCouponSuccess('Coupon applied successfully!');
      setCouponError('');
    } else {
      setCouponError('Invalid or expired coupon code');
      setCouponSuccess('');
    }
  };

  const handleQuantityChange = (productId: string, change: number) => {
    const updatedProducts = selectedProducts.map(product => {
      if (product.id === productId) {
        const newQuantity = Math.max(1, Math.min(product.stock, (product.quantity || 1) + change));
        return {...product, quantity: newQuantity};
      }
      return product;
    });
    onProductsUpdate(updatedProducts);
    calculateTotals();
  };

  const handleRemoveProduct = (productId: string) => {
    const updatedProducts = selectedProducts.filter(product => product.id !== productId);
    onProductsUpdate(updatedProducts);
    calculateTotals();
  };

  // Calculate item total based on rental type and custom days
  const calculateItemTotal = (product: Product) => {
    // Get the days for this product (either custom or global)
    const productDays = product.customDays || rentalPeriod.days;
    const dailyRate = product.temporaryDailyRate || product.dailyRate;
    const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;

    if (rentalPeriod.rentalType === 'weekly' && weeklyRate) {
      const weeks = Math.ceil(productDays / 7);
      return weeklyRate * weeks * product.quantity;
    } else {
      return dailyRate * productDays * product.quantity;
    }
  };

  const handleSubmit = () => {
    window.scrollTo(0, 0);
    onSubmit();
  };

  const handleEditDays = (productId: string) => {
    const product = selectedProducts.find(p => p.id === productId);
    if (product) {
      setCustomDays(product.customDays || rentalPeriod.days);
      setEditingProductId(productId);
    }
  };

  const handleSaveCustomDays = () => {
    if (editingProductId) {
      const updatedProducts = selectedProducts.map(product => {
        if (product.id === editingProductId) {
          return {
            ...product,
            customDays: customDays !== rentalPeriod.days ? customDays : undefined
          };
        }
        return product;
      });
      onProductsUpdate(updatedProducts);
      setEditingProductId(null);
    }
  };

  const handleCancelEditDays = () => {
    setEditingProductId(null);
  };

  const formatRentalDates = (dates: string[]) => {
    if (!dates || dates.length === 0) return 'No dates selected';

    const parsedDates = dates.map(d => parseISO(d)).filter(d => isValid(d));
    const groupedDates = parsedDates.reduce((acc: Record<string, number[]>, date: Date) => {
      const key = format(date, 'MMM yyyy');
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(date.getDate());
      return acc;
    }, {});

    return Object.entries(groupedDates)
      .map(([monthYear, days]) => {
        const sortedDays = days.sort((a, b) => a - b);
        return `${sortedDays.join(', ')} ${monthYear}`;
      })
      .join(', ');
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <ShoppingCart size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div>
            <span className="font-medium">
              {selectedProducts.length} {selectedProducts.length === 1 ? 'item' : 'items'} selected
            </span>
            <div className="text-blue-600 font-bold">
              Total: {formatCurrencyTotal(total)}
            </div>
          </div>
        </div>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 rounded-md font-medium w-full sm:w-auto bg-blue-600 text-white hover:bg-blue-700 transition-colors"
        >
          Generate Quote
        </button>
      </div>
    </div>
  );

  return (
    <div className="pb-20">
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </button>
        <h2 className="text-2xl font-bold">Review Your Booking</h2>
      </div>

      {/* Selected Products */}
      <div className="mb-6">
        <div className="flex items-center mb-3">
          <ShoppingCart size={20} className="text-blue-600 mr-2" />
          <h3 className="font-bold text-lg">Selected Equipment</h3>
        </div>
        <div className="bg-white border border-gray-200 rounded-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    {rentalPeriod.rentalType === 'weekly' ? 'Weekly Rate' : 'Daily Rate'}
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Quantity</th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">
                    Rental Period
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Subtotal</th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {selectedProducts.map(product => (
                  <tr key={product.id}>
                    <td className="px-4 py-3">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden mr-3">
                          <img src={product.image} alt={product.name} className="h-full w-full object-cover" />
                        </div>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-xs text-gray-500 capitalize">{product.category}</p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-right">
                      {rentalPeriod.rentalType === 'weekly' && product.weeklyRate
                        ? `${formatCurrencyRate(product.weeklyRate)}/week`
                        : `${formatCurrencyRate(product.dailyRate)}/day`}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center">
                        <button
                          onClick={(e) => { e.stopPropagation(); handleQuantityChange(product.id, -1); }}
                          className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                          disabled={product.quantity <= 1}
                        >
                          <Minus size={16} />
                        </button>
                        <span className="mx-3 font-medium">{product.quantity}</span>
                        <button
                          onClick={(e) => { e.stopPropagation(); handleQuantityChange(product.id, 1); }}
                          className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                          disabled={product.quantity >= product.stock}
                        >
                          <Plus size={16} />
                        </button>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center">
                      {editingProductId === product.id ? (
                        <div className="flex items-center justify-center space-x-2">
                          <input
                            type="number"
                            value={customDays}
                            onChange={(e) => setCustomDays(Math.max(1, parseInt(e.target.value) || 1))}
                            className="w-16 p-1 border border-gray-300 rounded text-center"
                            min="1"
                          />
                          <div className="flex">
                            <button
                              onClick={(e) => { e.stopPropagation(); handleSaveCustomDays(); }}
                              className="p-1 rounded-full text-green-600 hover:bg-green-100"
                              title="Save"
                            >
                              <Check size={16} />
                            </button>
                            <button
                              onClick={(e) => { e.stopPropagation(); handleCancelEditDays(); }}
                              className="p-1 rounded-full text-red-600 hover:bg-red-100"
                              title="Cancel"
                            >
                              <X size={16} />
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center">
                          <span className="font-medium">
                            {product.customDays || rentalPeriod.days} {(product.customDays || rentalPeriod.days) === 1 ? 'day' : 'days'}
                          </span>
                          <button
                            onClick={(e) => { e.stopPropagation(); handleEditDays(product.id); }}
                            className="ml-2 p-1 rounded-full text-blue-600 hover:bg-blue-100"
                            title="Edit Days"
                          >
                            <Clock size={16} />
                          </button>
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-3 text-right font-medium">
                      {formatCurrencyRate(calculateItemTotal(product))}
                    </td>
                    <td className="px-4 py-3 text-center">
                      <button
                        onClick={(e) => { e.stopPropagation(); handleRemoveProduct(product.id); }}
                        className="p-1 rounded-full text-red-600 hover:bg-red-100"
                        title="Remove"
                      >
                        <Trash2 size={18} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <div className="flex items-center mb-3">
            <User size={20} className="text-blue-600 mr-2" />
            <h3 className="font-bold text-lg">Customer Information</h3>
          </div>
          <div className="bg-white border border-gray-200 rounded-md p-4">
            <p className="font-medium">{contactInfo.name}</p>
            <p className="text-gray-600">{contactInfo.email}</p>
            <p className="text-gray-600">{contactInfo.phone}</p>
            <p className="text-gray-600">{contactInfo.address}</p>
          </div>
        </div>

        {/* Rental Period Section */}
        <div>
          <div className="flex items-center mb-3">
            <Calendar size={20} className="text-blue-600 mr-2" />
            <h3 className="font-bold text-lg">Rental Period</h3>
          </div>
          <div className="bg-white border border-gray-200 rounded-md p-4">
            <div className="flex items-center mb-2">
              <RefreshCw size={16} className="text-blue-600 mr-2" />
              <span className="font-medium capitalize">{rentalPeriod.rentalType} Rental</span>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-3">
              <div>
                <p className="text-sm text-gray-500">Selected Dates</p>
                <p className="font-medium">{formatRentalDates(rentalPeriod.dates)}</p>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-gray-100">
              <p className="text-sm text-gray-500">Total Days</p>
              <p className="font-medium">
                {rentalPeriod.days} {rentalPeriod.days === 1 ? 'day' : 'days'}
                {rentalPeriod.rentalType === 'weekly' && rentalPeriod.days >= 7 && (
                  <span className="text-gray-500 ml-2">
                    ({Math.ceil(rentalPeriod.days / 7)} {Math.ceil(rentalPeriod.days / 7) === 1 ? 'week' : 'weeks'})
                  </span>
                )}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Note: You can customize the rental period for each item individually
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Delivery Option */}
      <div className="mb-6">
        <div className="flex items-center mb-3">
          <Truck size={20} className="text-blue-600 mr-2" />
          <h3 className="font-bold text-lg">Delivery Method</h3>
        </div>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          {deliveryOption ? (
            <div>
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium text-lg">{deliveryOption.name}</p>
                  <p className="text-gray-600">{deliveryOption.description}</p>
                </div>
                <div>
                  {deliveryOption.fee > 0 ? (
                    <span className="font-bold text-lg">{formatCurrencyRate(deliveryOption.fee)}</span>
                  ) : (
                    <span className="text-green-600 font-medium">Free</span>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No delivery option selected</p>
          )}
        </div>
      </div>

      {/* Discount Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <Tag size={20} className="text-blue-600 mr-2" />
            <h3 className="font-bold text-lg">Discount</h3>
          </div>
          <button
            onClick={() => setShowDiscountModal(true)}
            className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm flex items-center"
          >
            <Percent size={16} className="mr-1" /> Add Discount
          </button>
        </div>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          <div className="flex flex-col sm:flex-row gap-3 mb-4">
            <input
              type="text"
              value={couponCode}
              onChange={(e) => onCouponCodeChange(e.target.value)}
              placeholder="Enter coupon code"
              className="flex-1 p-2 border border-gray-300 rounded-md"
              disabled={!!appliedCoupon}
            />
            <button
              onClick={handleCouponApply}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors whitespace-nowrap"
              disabled={!!appliedCoupon}
            >
              Apply Coupon
            </button>
          </div>
          {couponError && (
            <p className="text-red-500 text-sm mt-2">{couponError}</p>
          )}
          {couponSuccess && (
            <p className="text-green-600 text-sm mt-2">{couponSuccess}</p>
          )}
          {appliedCoupon && (
            <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-green-800">{appliedCoupon.code}</p>
                  <p className="text-sm text-green-600">
                    {appliedCoupon.discountType === 'percentage'
                      ? `${appliedCoupon.discountValue}% off`
                      : `${formatCurrencyRate(appliedCoupon.discountValue)} off`}
                  </p>
                </div>
                <button
                  onClick={() => {
                    onCouponApply('');
                    onCouponCodeChange('');
                    setCouponSuccess('');
                    setCouponError('');
                  }}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Order Summary */}
      <div className="mb-6">
        <h3 className="font-bold text-lg mb-3">Order Summary</h3>
        <div className="bg-white border border-gray-200 rounded-md p-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal</span>
              <span>{formatCurrencyRate(subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Delivery Fee</span>
              <span>{formatCurrencyRate(deliveryFee)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount</span>
                <span>-{formatCurrencyRate(discount)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">VAT ({systemSettings.enableTax ? `${systemSettings.taxRate}%` : '0%'})</span>
              <span>{formatCurrencyTax(tax)}</span>
            </div>
            <div className="border-t border-gray-200 pt-2 mt-2">
              <div className="flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{formatCurrencyTotal(total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Cart Summary */}
      <CartSummary />

      {/* Discount Modal */}
      {showDiscountModal && (
        <QuoteDiscountModal
          show={showDiscountModal}
          onClose={() => setShowDiscountModal(false)}
          onSave={(coupon) => {
            // Apply the discount
            onCouponApply('');
            onCouponCodeChange('');
            setCouponSuccess('Manual discount applied successfully!');
            setCouponError('');
            // Use the existing coupon apply mechanism
            onCouponApply(coupon.code, coupon);
            setShowDiscountModal(false);
          }}
          subtotal={subtotal}
          deliveryFee={deliveryFee}
          systemSettings={systemSettings}
          formatCurrency={formatCurrencyTotal}
        />
      )}
    </div>
  );
};

export default ReviewBooking;