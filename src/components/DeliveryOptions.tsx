import React, { useState } from 'react';
import { ArrowLeft, Truck, MapPin, Clock, DollarSign } from 'lucide-react';
import { DeliveryOption } from '../types';

interface DeliveryOptionsProps {
  deliveryOptions: DeliveryOption[];
  onDeliverySelect: (option: DeliveryOption | null) => void;
  onBack: () => void;
  onSubmit: () => void;
}

const DeliveryOptions: React.FC<DeliveryOptionsProps> = ({ 
  deliveryOptions, 
  onDeliverySelect, 
  onBack,
  onSubmit
}) => {
  const [selectedOption, setSelectedOption] = useState<DeliveryOption | null>(deliveryOptions[0] || null);

  const handleOptionSelect = (option: DeliveryOption) => {
    setSelectedOption(option);
    onDeliverySelect(option);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onDeliverySelect(selectedOption);
    window.scrollTo(0, 0);
    onSubmit();
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Fixed cart at bottom
  const CartSummary = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-4 z-10">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center">
          <Truck size={20} className="text-blue-600 mr-2 flex-shrink-0" />
          <div>
            <span className="font-medium">
              Delivery Option: {selectedOption?.name || 'None selected'}
            </span>
            {selectedOption && (
              <div className="text-blue-600 font-bold">
                {selectedOption.fee > 0 ? formatCurrency(selectedOption.fee) : 'Free'}
              </div>
            )}
          </div>
        </div>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 rounded-md font-medium w-full sm:w-auto bg-blue-600 text-white hover:bg-blue-700 transition-colors"
        >
          Next
        </button>
      </div>
    </div>
  );

  return (
    <div className="pb-20">
      <div className="flex items-center mb-6">
        <button 
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </button>
        <h2 className="text-2xl font-bold">Delivery Options</h2>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <div className="flex items-center mb-2">
              <Truck size={18} className="mr-2 text-blue-600" />
              <h3 className="font-medium">Choose Delivery Method</h3>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Select how you would like to receive your equipment
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {deliveryOptions.map(option => (
              <div 
                key={option.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedOption?.id === option.id 
                    ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50' 
                    : 'border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => handleOptionSelect(option)}
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-bold text-lg">{option.name}</h3>
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedOption?.id === option.id ? 'border-blue-500' : 'border-gray-300'
                  }`}>
                    {selectedOption?.id === option.id && (
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    )}
                  </div>
                </div>
                
                <p className="text-gray-600 mb-3">{option.description}</p>
                
                <div className="flex items-center justify-between mt-auto">
                  {option.fee > 0 ? (
                    <span className="font-bold text-lg">{formatCurrency(option.fee)}</span>
                  ) : (
                    <span className="text-green-600 font-medium">Free</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium mb-2">Delivery Information</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start">
                <MapPin size={16} className="mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>Self pickup is available at our main office: 123 Equipment Lane, Anytown, ST 12345</span>
              </li>
              <li className="flex items-start">
                <Clock size={16} className="mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>Delivery times are between 8am-5pm on the start date of your rental period</span>
              </li>
              <li className="flex items-start">
                <Truck size={16} className="mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>Our team will contact you to confirm the exact delivery time</span>
              </li>
              <li className="flex items-start">
                <DollarSign size={16} className="mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>Delivery fees are non-refundable once the equipment has been dispatched</span>
              </li>
            </ul>
          </div>
        </div>
      </form>
      
      {/* Fixed Cart Summary */}
      <CartSummary />
    </div>
  );
};

export default DeliveryOptions;