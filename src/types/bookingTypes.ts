// Simplified product type for bookings to reduce database read/write operations
export interface BookingProduct {
  id: string;
  name: string;
  category: string;
  sku: string;
  dailyRate: number;
  weeklyRate?: number;
  description: string;
  quantity: number;
  // Keep these fields for UI compatibility but with minimal data
  image: string;
  stock: number;
  available: boolean;
  customDays?: number;
  customPrice?: number;
  temporaryDailyRate?: number;
  temporaryWeeklyRate?: number;
}

// Function to convert a full Product to a BookingProduct
export const toBookingProduct = (product: any): BookingProduct => {
  // Create a clean object with no undefined values
  const cleanProduct: BookingProduct = {
    id: product.id || '',
    name: product.name || '',
    category: product.category || '',
    sku: product.sku || '',
    dailyRate: product.dailyRate || 0,
    description: product.description || '',
    quantity: product.quantity || 1,
    // Include minimal UI compatibility fields
    image: product.image || 'https://placehold.co/400x300/2563eb/ffffff?text=Product',
    stock: product.stock || 99,
    available: product.available !== undefined ? product.available : true
  };

  // Only add optional fields if they exist and are not undefined
  if (product.weeklyRate !== undefined) cleanProduct.weeklyRate = product.weeklyRate;
  if (product.customDays !== undefined) cleanProduct.customDays = product.customDays;
  if (product.customPrice !== undefined) cleanProduct.customPrice = product.customPrice;
  if (product.temporaryDailyRate !== undefined) cleanProduct.temporaryDailyRate = product.temporaryDailyRate;
  if (product.temporaryWeeklyRate !== undefined) cleanProduct.temporaryWeeklyRate = product.temporaryWeeklyRate;

  return cleanProduct;
};
