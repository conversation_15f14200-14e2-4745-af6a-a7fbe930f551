import { testConnection } from '../config/database';
import createTables from './createSchema';
import { addUser, updateSystemSettings, addCoupon } from '../services/database';
import { SystemSettings } from '../types';

const initializeDatabase = async () => {
  try {
    console.log('🚀 Starting database initialization...');
    
    // Test database connection
    console.log('📡 Testing database connection...');
    const isConnected = await testConnection();
    
    if (!isConnected) {
      throw new Error('Failed to connect to database. Please check your database configuration.');
    }
    
    console.log('✅ Database connection successful!');
    
    // Create tables
    console.log('📋 Creating database schema...');
    await createTables();
    console.log('✅ Database schema created successfully!');
    
    // Seed default data
    console.log('🌱 Seeding default data...');
    
    // Create default admin user
    try {
      await addUser({
        email: '<EMAIL>',
        username: 'admin',
        name: 'System Administrator',
        role: 'admin',
        createdAt: new Date().toISOString(),
        isActive: true
      });
      console.log('✅ Default admin user created (username: admin, password: defaultpassword)');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  Admin user already exists, skipping...');
      } else {
        throw error;
      }
    }
    
    // Create default system settings
    const defaultSettings: SystemSettings = {
      taxRate: 10,
      enableTax: true,
      lastQuoteNumber: 1000,
      deliveryOptions: [
        {
          id: 'pickup',
          name: 'Customer Pickup',
          description: 'Customer will pick up items from our location',
          fee: 0
        },
        {
          id: 'local_delivery',
          name: 'Local Delivery',
          description: 'Delivery within 10km radius',
          fee: 25
        },
        {
          id: 'extended_delivery',
          name: 'Extended Delivery',
          description: 'Delivery beyond 10km radius',
          fee: 50
        }
      ]
    };
    
    await updateSystemSettings(defaultSettings);
    console.log('✅ Default system settings created');
    
    // Create sample coupon
    try {
      await addCoupon({
        code: 'WELCOME10',
        discountType: 'percentage',
        discountValue: 10,
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
        active: true
      });
      console.log('✅ Sample coupon created (WELCOME10 - 10% off)');
    } catch (error) {
      console.log('ℹ️  Sample coupon might already exist, skipping...');
    }
    
    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('1. Start your application with: npm run dev');
    console.log('2. Login with username: admin, password: defaultpassword');
    console.log('3. Add your products, clients, and start taking bookings!');
    console.log('');
    console.log('🔐 Security Note: Please change the default admin password after first login');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
};

// Run initialization if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeDatabase()
    .then(() => {
      console.log('Database initialization completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database initialization failed:', error);
      process.exit(1);
    });
}

export default initializeDatabase;
