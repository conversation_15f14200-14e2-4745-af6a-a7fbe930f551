import { pool } from '../config/database';

const createTables = async () => {
  try {
    console.log('Creating database schema...');

    // Products table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS products (
        id VARCHAR(255) PRIMARY KEY,
        name VA<PERSON>HAR(255) NOT NULL,
        category VARCHAR(255) NOT NULL,
        sku VARCHAR(255) UNIQUE NOT NULL,
        barcode VARCHAR(255),
        daily_rate DECIMAL(10,2) NOT NULL,
        weekly_rate DECIMAL(10,2),
        image TEXT,
        description TEXT,
        available BOOLEAN DEFAULT true,
        quantity INT DEFAULT 0,
        stock INT DEFAULT 0,
        featured BOOLEAN DEFAULT false,
        custom_days INT,
        custom_price DECIMAL(10,2),
        temporary_daily_rate DECIMAL(10,2),
        temporary_weekly_rate DECIMAL(10,2),
        is_external_vendor_item BOOLEAN DEFAULT false,
        vendor_id VARCHAR(255),
        vendor_sku VARCHAR(255),
        vendor_cost DECIMAL(10,2),
        profit_margin DECIMAL(5,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Categories table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS categories (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Clients table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS clients (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        total_bookings INT DEFAULT 0,
        total_spent DECIMAL(10,2) DEFAULT 0,
        pending_payment DECIMAL(10,2) DEFAULT 0,
        last_booking TIMESTAMP,
        notes TEXT
      )
    `);

    // Bookings table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS bookings (
        id VARCHAR(255) PRIMARY KEY,
        quote_number VARCHAR(255) UNIQUE NOT NULL,
        date TIMESTAMP NOT NULL,
        customer_name VARCHAR(255) NOT NULL,
        customer_email VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(50),
        customer_address TEXT,
        rental_dates JSON NOT NULL,
        rental_days INT NOT NULL,
        rental_type ENUM('daily', 'weekly') NOT NULL,
        status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
        subtotal DECIMAL(10,2) NOT NULL,
        delivery_fee DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        total DECIMAL(10,2) NOT NULL,
        paid_amount DECIMAL(10,2) DEFAULT 0,
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        delivery_option_id VARCHAR(255),
        coupon_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Booking products table (for many-to-many relationship)
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS booking_products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id VARCHAR(255) NOT NULL,
        product_id VARCHAR(255) NOT NULL,
        quantity INT NOT NULL,
        daily_rate DECIMAL(10,2) NOT NULL,
        weekly_rate DECIMAL(10,2),
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
      )
    `);

    // Coupons table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS coupons (
        id VARCHAR(255) PRIMARY KEY,
        code VARCHAR(100) UNIQUE NOT NULL,
        discount_type ENUM('percentage', 'fixed') NOT NULL,
        discount_value DECIMAL(10,2) NOT NULL,
        expiry_date DATE,
        active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Users table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(255) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(100) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'manager') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP,
        is_active BOOLEAN DEFAULT true
      )
    `);

    // Vendors table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS vendors (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        contact_name VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        notes TEXT,
        payment_terms VARCHAR(255),
        active BOOLEAN DEFAULT true,
        tax_percentage DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Vendor transactions table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS vendor_transactions (
        id VARCHAR(255) PRIMARY KEY,
        vendor_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        date TIMESTAMP NOT NULL,
        type ENUM('payment', 'invoice') NOT NULL,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        booking_id VARCHAR(255),
        reference VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE CASCADE
      )
    `);

    // Client activities table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS client_activities (
        id VARCHAR(255) PRIMARY KEY,
        client_id VARCHAR(255) NOT NULL,
        date TIMESTAMP NOT NULL,
        type ENUM('booking', 'quote', 'payment', 'note', 'merge') NOT NULL,
        description TEXT NOT NULL,
        amount DECIMAL(10,2),
        booking_id VARCHAR(255),
        quote_number VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
      )
    `);

    // System settings table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id VARCHAR(50) PRIMARY KEY,
        tax_rate DECIMAL(5,2) DEFAULT 0,
        enable_tax BOOLEAN DEFAULT false,
        last_quote_number INT DEFAULT 0,
        settings_data JSON,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Delivery options table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS delivery_options (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        fee DECIMAL(10,2) NOT NULL,
        active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Payments table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS payments (
        id VARCHAR(255) PRIMARY KEY,
        booking_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        transaction_id VARCHAR(255),
        date TIMESTAMP NOT NULL,
        method ENUM('cash', 'card', 'bank_transfer', 'cheque', 'other') NOT NULL,
        reference VARCHAR(255),
        notes TEXT,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
      )
    `);

    console.log('Database schema created successfully!');
  } catch (error) {
    console.error('Error creating database schema:', error);
    throw error;
  }
};

export default createTables;
