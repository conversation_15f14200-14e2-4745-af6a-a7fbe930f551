import { Product, RentalPeriodInfo } from '../types';
import { BookingProduct } from '../types/bookingTypes';

// Function to calculate the total for a product
export const calculateItemTotal = (
  product: Product | BookingProduct, 
  rentalPeriod?: RentalPeriodInfo
): number => {
  // If no rental period is provided, default to 1 day
  const days = product.customDays || (rentalPeriod ? rentalPeriod.days : 1);
  
  // Use custom price if available
  if (product.customPrice !== undefined) {
    return product.customPrice * (product.quantity || 1);
  }
  
  // Use temporary rates if available
  if (product.temporaryDailyRate !== undefined && rentalPeriod?.rentalType === 'daily') {
    return product.temporaryDailyRate * days * (product.quantity || 1);
  }
  
  if (product.temporaryWeeklyRate !== undefined && rentalPeriod?.rentalType === 'weekly') {
    return product.temporaryWeeklyRate * days * (product.quantity || 1);
  }
  
  // Use standard rates
  const rate = rentalPeriod?.rentalType === 'weekly' 
    ? (product.weeklyRate || product.dailyRate) 
    : product.dailyRate;
  
  return rate * days * (product.quantity || 1);
};

// Function to calculate the total amount for all products
export const calculateTotalAmount = (
  products: (Product | BookingProduct)[], 
  rentalPeriod?: RentalPeriodInfo
): number => {
  return products.reduce((sum, product) => sum + calculateItemTotal(product, rentalPeriod), 0);
};
