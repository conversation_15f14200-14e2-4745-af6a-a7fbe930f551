import { format, parseISO } from 'date-fns';

export const getOrdinalSuffix = (n: number): string => {
  const j = n % 10;
  const k = n % 100;
  if (j === 1 && k !== 11) return 'st';
  if (j === 2 && k !== 12) return 'nd';
  if (j === 3 && k !== 13) return 'rd';
  return 'th';
};

export const formatDateWithOrdinal = (date: string | Date): string => {
  const parsedDate = typeof date === 'string' ? parseISO(date) : date;
  const day = parsedDate.getDate();
  const month = format(parsedDate, 'MMM');
  const year = format(parsedDate, 'yyyy');
  return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
};

export const formatRentalDates = (dates: string[]): string => {
  if (!dates || dates.length === 0) return 'No dates selected';
  
  const parsedDates = dates.map(date => parseISO(date));
  const groupedDates = parsedDates.reduce((acc: Record<string, number[]>, date: Date) => {
    const key = format(date, 'MMM yyyy');
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(date.getDate());
    return acc;
  }, {});

  return Object.entries(groupedDates)
    .map(([monthYear, days]) => {
      const sortedDays = days.sort((a, b) => a - b);
      const formattedDays = sortedDays.map(day => `${day}${getOrdinalSuffix(day)}`);
      return `${formattedDays.join(', ')} ${monthYear}`;
    })
    .join(', ');
}; 