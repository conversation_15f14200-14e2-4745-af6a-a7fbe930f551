// Service Worker Registration Utility

export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | undefined> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered successfully', registration.scope);
      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  } else {
    console.log('Service Workers are not supported in this browser');
  }
  return undefined;
};

export const unregisterServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker unregistered:', result);
    }
  }
};

// Helper to request notification permission
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (!('Notification' in window)) {
    console.log('This browser does not support notifications');
    return 'denied';
  }
  
  if (Notification.permission === 'granted') {
    return 'granted';
  }
  
  if (Notification.permission !== 'denied') {
    const permission = await Notification.requestPermission();
    return permission;
  }
  
  return Notification.permission;
};

// Check if push is supported in the browser
export const isPushSupported = (): boolean => {
  return 'serviceWorker' in navigator && 
         'PushManager' in window;
};

// Subscribe to push notifications
export const subscribeToPushNotifications = async (): Promise<PushSubscription | null> => {
  if (!isPushSupported()) {
    console.log('Push notifications are not supported in this browser');
    return null;
  }
  
  try {
    const registration = await navigator.serviceWorker.ready;
    
    // Get the server's public key
    // In a real app, this would come from your server
    const publicVapidKey = import.meta.env.VITE_PUBLIC_VAPID_KEY || '';
    
    if (!publicVapidKey) {
      console.warn('No VAPID key available, cannot subscribe to push notifications');
      return null;
    }
    
    // Subscribe the user
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,  // Always show notifications
      applicationServerKey: urlBase64ToUint8Array(publicVapidKey)
    });
    
    console.log('Push notification subscription successful:', subscription);
    
    // TODO: Send the subscription to your server
    // await saveSubscription(subscription);
    
    return subscription;
  } catch (error) {
    console.error('Failed to subscribe to push notifications:', error);
    return null;
  }
};

// Helper function to convert base64 to Uint8Array
// Required for applicationServerKey
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  
  return outputArray;
} 