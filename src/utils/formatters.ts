// Format date
export const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format currency for total amounts
export const formatCurrencyTotal = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'BHD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}; 