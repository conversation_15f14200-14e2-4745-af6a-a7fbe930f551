import mysql from 'mysql2/promise';

const config = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'teamwork_app',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

const seedSampleData = async () => {
  let connection;
  
  try {
    console.log('🌱 Starting data seeding...');
    
    // Create connection
    connection = await mysql.createConnection(config);
    console.log('✅ Database connection successful!');
    
    // Sample products
    const products = [
      {
        name: 'Professional Camera',
        category: 'Photography',
        sku: 'CAM001',
        barcode: '123456789001',
        dailyRate: 50.00,
        weeklyRate: 300.00,
        description: 'High-quality DSLR camera for professional photography',
        available: true,
        quantity: 5,
        stock: 5,
        featured: true
      },
      {
        name: 'LED Light Panel',
        category: 'Lighting',
        sku: 'LED001',
        barcode: '123456789002',
        dailyRate: 25.00,
        weeklyRate: 150.00,
        description: 'Professional LED light panel for video and photography',
        available: true,
        quantity: 10,
        stock: 10,
        featured: false
      },
      {
        name: 'Tripod Stand',
        category: 'Accessories',
        sku: 'TRI001',
        barcode: '123456789003',
        dailyRate: 15.00,
        weeklyRate: 90.00,
        description: 'Sturdy tripod stand for cameras and lights',
        available: true,
        quantity: 8,
        stock: 8,
        featured: false
      },
      {
        name: 'Wireless Microphone',
        category: 'Audio',
        sku: 'MIC001',
        barcode: '123456789004',
        dailyRate: 30.00,
        weeklyRate: 180.00,
        description: 'Professional wireless microphone system',
        available: true,
        quantity: 6,
        stock: 6,
        featured: true
      },
      {
        name: 'Video Projector',
        category: 'Display',
        sku: 'PROJ001',
        barcode: '123456789005',
        dailyRate: 75.00,
        weeklyRate: 450.00,
        description: 'High-resolution video projector for presentations',
        available: true,
        quantity: 3,
        stock: 3,
        featured: true
      }
    ];

    // Insert products
    for (const product of products) {
      const id = generateId();
      await connection.execute(`
        INSERT INTO products (
          id, name, category, sku, barcode, daily_rate, weekly_rate, image, description,
          available, quantity, stock, featured, custom_days, custom_price,
          temporary_daily_rate, temporary_weekly_rate, is_external_vendor_item,
          vendor_id, vendor_sku, vendor_cost, profit_margin
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, product.name, product.category, product.sku, product.barcode,
        product.dailyRate, product.weeklyRate, null, product.description,
        product.available, product.quantity, product.stock, product.featured,
        null, null, null, null, false, null, null, null, null
      ]);
      console.log(`✅ Added product: ${product.name}`);
    }

    // Sample clients
    const clients = [
      {
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0101',
        address: '123 Main St, City, State 12345',
        totalBookings: 0,
        totalSpent: 0,
        pendingPayment: 0,
        notes: 'Regular customer, prefers weekend rentals'
      },
      {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0102',
        address: '456 Oak Ave, City, State 12345',
        totalBookings: 0,
        totalSpent: 0,
        pendingPayment: 0,
        notes: 'Event planner, often books multiple items'
      },
      {
        name: 'Mike Davis',
        email: '<EMAIL>',
        phone: '******-0103',
        address: '789 Pine St, City, State 12345',
        totalBookings: 0,
        totalSpent: 0,
        pendingPayment: 0,
        notes: 'Photography business owner'
      }
    ];

    // Insert clients
    for (const client of clients) {
      const id = client.email.toLowerCase().trim();
      await connection.execute(`
        INSERT INTO clients (id, name, email, phone, address, total_bookings, total_spent, pending_payment, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, client.name, client.email, client.phone, client.address,
        client.totalBookings, client.totalSpent, client.pendingPayment, client.notes
      ]);
      console.log(`✅ Added client: ${client.name}`);
    }

    console.log('');
    console.log('🎉 Sample data seeded successfully!');
    console.log('');
    console.log('📝 Sample data includes:');
    console.log(`   - ${products.length} products across different categories`);
    console.log(`   - ${clients.length} sample clients`);
    console.log('   - 1 admin user (username: admin, password: defaultpassword)');
    console.log('   - 1 sample coupon (WELCOME10 - 10% off)');
    console.log('');
    console.log('🚀 Your application is ready to use!');
    console.log('   Frontend: http://localhost:5173');
    console.log('   Backend API: http://localhost:3001');

  } catch (error) {
    console.error('❌ Data seeding failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run seeding
seedSampleData()
  .then(() => {
    console.log('Data seeding completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Data seeding failed:', error);
    process.exit(1);
  });
