import admin from 'firebase-admin';
import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Firebase Admin SDK configuration
// You'll need to download your Firebase service account key
let serviceAccount;
try {
  const keyPath = path.join(__dirname, 'firebase-service-account-key.json');
  const keyContent = fs.readFileSync(keyPath, 'utf8');

  if (!keyContent.trim()) {
    console.error('❌ Firebase service account key file is empty!');
    console.log('📋 Please follow these steps:');
    console.log('1. Go to https://console.firebase.google.com/');
    console.log('2. Select your project: teamwork-c16e4');
    console.log('3. Go to Project Settings → Service Accounts');
    console.log('4. Click "Generate new private key"');
    console.log('5. Download the JSON file');
    console.log('6. Replace the empty file at: scripts/firebase-service-account-key.json');
    process.exit(1);
  }

  serviceAccount = JSON.parse(keyContent);
} catch (error) {
  console.error('❌ Error reading Firebase service account key:', error.message);
  console.log('📋 Make sure the file exists at: scripts/firebase-service-account-key.json');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // Add your Firebase project URL here
  databaseURL: "https://teamwork-c16e4-default-rtdb.firebaseio.com"
});

const db = admin.firestore();

// MySQL connection configuration
const mysqlConfig = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'teamwork_app'
};

// Helper function to generate MySQL-compatible ID
function generateMySQLId() {
  return Date.now().toString() + '_' + Math.random().toString(36).substring(2, 11);
}

// Helper function to convert Firebase timestamp to MySQL datetime
function convertTimestamp(timestamp) {
  if (!timestamp) return null;

  try {
    let date;

    if (timestamp._seconds) {
      date = new Date(timestamp._seconds * 1000);
    } else if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'number') {
      date = new Date(timestamp);
    } else {
      // If we can't parse it, return current time
      date = new Date();
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return new Date().toISOString().slice(0, 19).replace('T', ' ');
    }

    return date.toISOString().slice(0, 19).replace('T', ' ');
  } catch (error) {
    console.warn('Warning: Invalid timestamp, using current time:', timestamp);
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  }
}

async function migrateProducts() {
  console.log('🔄 Migrating products...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const productsSnapshot = await db.collection('products').get();
    
    let migratedCount = 0;
    
    for (const doc of productsSnapshot.docs) {
      const data = doc.data();
      
      const query = `
        INSERT INTO products (
          id, name, category, sku, barcode, daily_rate, weekly_rate, image, description,
          available, quantity, stock, featured, custom_days, custom_price,
          temporary_daily_rate, temporary_weekly_rate, is_external_vendor_item,
          vendor_id, vendor_sku, vendor_cost, profit_margin, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        category = VALUES(category),
        daily_rate = VALUES(daily_rate),
        updated_at = NOW()
      `;
      
      const values = [
        doc.id,
        data.name || '',
        data.category || 'Uncategorized',
        data.sku || generateMySQLId(),
        data.barcode || null,
        data.dailyRate || 0,
        data.weeklyRate || null,
        data.image || null,
        data.description || null,
        data.available !== false,
        data.quantity || 0,
        data.stock || 0,
        data.featured || false,
        data.customDays || null,
        data.customPrice || null,
        data.temporaryDailyRate || null,
        data.temporaryWeeklyRate || null,
        data.isExternalVendorItem || false,
        data.vendorId || null,
        data.vendorSku || null,
        data.vendorCost || null,
        data.profitMargin || null,
        convertTimestamp(data.createdAt) || new Date().toISOString().slice(0, 19).replace('T', ' '),
        convertTimestamp(data.updatedAt) || new Date().toISOString().slice(0, 19).replace('T', ' ')
      ];
      
      await connection.execute(query, values);
      migratedCount++;
    }
    
    await connection.end();
    console.log(`✅ Migrated ${migratedCount} products`);
    
  } catch (error) {
    console.error('❌ Error migrating products:', error);
  }
}

async function migrateClients() {
  console.log('🔄 Migrating clients...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const clientsSnapshot = await db.collection('clients').get();
    
    let migratedCount = 0;
    
    for (const doc of clientsSnapshot.docs) {
      const data = doc.data();
      
      const query = `
        INSERT INTO clients (
          id, name, email, phone, address, date_added, total_bookings, total_spent,
          pending_payment, last_booking, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        phone = VALUES(phone),
        address = VALUES(address),
        total_bookings = VALUES(total_bookings),
        total_spent = VALUES(total_spent),
        pending_payment = VALUES(pending_payment),
        last_booking = VALUES(last_booking),
        notes = VALUES(notes)
      `;
      
      const values = [
        data.email || doc.id, // Use email as ID
        data.name || '',
        data.email || '',
        data.phone || null,
        data.address || null,
        convertTimestamp(data.dateAdded) || new Date().toISOString().slice(0, 19).replace('T', ' '),
        data.totalBookings || 0,
        data.totalSpent || 0,
        data.pendingPayment || 0,
        convertTimestamp(data.lastBooking) || null,
        data.notes || null
      ];
      
      await connection.execute(query, values);
      migratedCount++;
    }
    
    await connection.end();
    console.log(`✅ Migrated ${migratedCount} clients`);
    
  } catch (error) {
    console.error('❌ Error migrating clients:', error);
  }
}

async function migrateBookings() {
  console.log('🔄 Migrating bookings...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const bookingsSnapshot = await db.collection('bookings').get();
    
    let migratedCount = 0;
    
    for (const doc of bookingsSnapshot.docs) {
      const data = doc.data();
      
      // Insert booking
      const bookingQuery = `
        INSERT INTO bookings (
          id, quote_number, date, customer_name, customer_email, customer_phone, customer_address,
          rental_dates, rental_days, rental_type, status, subtotal, delivery_fee, discount,
          tax, total, paid_amount, remaining_amount, delivery_option_id, coupon_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        paid_amount = VALUES(paid_amount),
        remaining_amount = VALUES(remaining_amount),
        updated_at = NOW()
      `;
      
      const bookingValues = [
        doc.id,
        data.quoteNumber || doc.id,
        convertTimestamp(data.date) || new Date().toISOString().slice(0, 19).replace('T', ' '),
        data.customer?.name || '',
        data.customer?.email || '',
        data.customer?.phone || null,
        data.customer?.address || null,
        JSON.stringify(data.rentalPeriod?.dates || []),
        data.rentalPeriod?.days || 1,
        data.rentalPeriod?.rentalType || 'daily',
        data.status || 'pending',
        data.subtotal || 0,
        data.deliveryFee || 0,
        data.discount || 0,
        data.tax || 0,
        data.total || 0,
        data.paidAmount || 0,
        data.remainingAmount || 0,
        data.delivery?.option?.id || null,
        data.coupon?.id || null,
        convertTimestamp(data.createdAt) || new Date().toISOString().slice(0, 19).replace('T', ' '),
        convertTimestamp(data.updatedAt) || new Date().toISOString().slice(0, 19).replace('T', ' ')
      ];
      
      await connection.execute(bookingQuery, bookingValues);
      
      // Insert booking products
      if (data.products && Array.isArray(data.products)) {
        for (const product of data.products) {
          try {
            const productQuery = `
              INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
              VALUES (?, ?, ?, ?, ?, ?)
              ON DUPLICATE KEY UPDATE
              quantity = VALUES(quantity),
              daily_rate = VALUES(daily_rate),
              weekly_rate = VALUES(weekly_rate),
              total_price = VALUES(total_price)
            `;

            const productValues = [
              doc.id,
              product.id,
              product.quantity || 1,
              product.dailyRate || 0,
              product.weeklyRate || null,
              product.totalPrice || 0
            ];

            await connection.execute(productQuery, productValues);
          } catch (productError) {
            console.warn(`Warning: Skipping product ${product.id} for booking ${doc.id} - product may not exist`);
          }
        }
      }
      
      migratedCount++;
    }
    
    await connection.end();
    console.log(`✅ Migrated ${migratedCount} bookings`);
    
  } catch (error) {
    console.error('❌ Error migrating bookings:', error);
  }
}

async function migrateCoupons() {
  console.log('🔄 Migrating coupons...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const couponsSnapshot = await db.collection('coupons').get();
    
    let migratedCount = 0;
    
    for (const doc of couponsSnapshot.docs) {
      const data = doc.data();
      
      const query = `
        INSERT INTO coupons (id, code, discount_type, discount_value, expiry_date, active, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        discount_value = VALUES(discount_value),
        expiry_date = VALUES(expiry_date),
        active = VALUES(active)
      `;
      
      const values = [
        doc.id,
        data.code || '',
        data.discountType || 'percentage',
        data.discountValue || 0,
        data.expiryDate ? new Date(data.expiryDate).toISOString().slice(0, 10) : null,
        data.active !== false,
        convertTimestamp(data.createdAt) || new Date().toISOString().slice(0, 19).replace('T', ' ')
      ];
      
      await connection.execute(query, values);
      migratedCount++;
    }
    
    await connection.end();
    console.log(`✅ Migrated ${migratedCount} coupons`);
    
  } catch (error) {
    console.error('❌ Error migrating coupons:', error);
  }
}

async function migrateUsers() {
  console.log('🔄 Migrating users...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const usersSnapshot = await db.collection('users').get();
    
    let migratedCount = 0;
    
    for (const doc of usersSnapshot.docs) {
      const data = doc.data();
      
      const query = `
        INSERT INTO users (id, email, username, name, password, role, created_at, last_login, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        role = VALUES(role),
        last_login = VALUES(last_login),
        is_active = VALUES(is_active)
      `;
      
      const values = [
        doc.id,
        data.email || '',
        data.username || data.email || '',
        data.name || '',
        data.password || 'defaultpassword', // You may want to reset passwords
        data.role || 'admin',
        convertTimestamp(data.createdAt) || new Date().toISOString().slice(0, 19).replace('T', ' '),
        convertTimestamp(data.lastLogin) || null,
        data.isActive !== false
      ];
      
      await connection.execute(query, values);
      migratedCount++;
    }
    
    await connection.end();
    console.log(`✅ Migrated ${migratedCount} users`);
    
  } catch (error) {
    console.error('❌ Error migrating users:', error);
  }
}

async function migrateSystemSettings() {
  console.log('🔄 Migrating system settings...');
  
  try {
    const connection = await mysql.createConnection(mysqlConfig);
    const settingsSnapshot = await db.collection('systemSettings').get();
    
    for (const doc of settingsSnapshot.docs) {
      const data = doc.data();
      
      const query = `
        INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        tax_rate = VALUES(tax_rate),
        enable_tax = VALUES(enable_tax),
        last_quote_number = VALUES(last_quote_number),
        settings_data = VALUES(settings_data),
        updated_at = NOW()
      `;
      
      const values = [
        doc.id || 'general',
        data.taxRate || 0,
        data.enableTax || false,
        data.lastQuoteNumber || 1000,
        JSON.stringify(data),
        new Date().toISOString().slice(0, 19).replace('T', ' ')
      ];
      
      await connection.execute(query, values);
    }
    
    await connection.end();
    console.log(`✅ Migrated system settings`);
    
  } catch (error) {
    console.error('❌ Error migrating system settings:', error);
  }
}

// Main migration function
async function runMigration() {
  console.log('🚀 Starting Firebase to MySQL migration...\n');
  
  try {
    await migrateUsers();
    await migrateProducts();
    await migrateClients();
    await migrateCoupons();
    await migrateBookings();
    await migrateSystemSettings();
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('📊 All your Firebase data has been imported to MySQL.');
    console.log('🔐 Note: User passwords have been reset to "defaultpassword" for security.');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the migration
runMigration();
