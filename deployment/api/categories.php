<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Categories API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    // Get categories from categories table
    $query = "SELECT * FROM categories ORDER BY name";
    $categories = $database->getMany($query);

    // Format categories to match frontend expectations
    $formattedCategories = array_map(function($category) {
        return [
            'id' => $category['id'],
            'name' => $category['name'],
            'description' => $category['description'] ?? ''
        ];
    }, $categories);

    sendResponse($formattedCategories);
}

function handlePost($database) {
    $data = getRequestData();

    validateRequired($data, ['name']);

    $categoryName = sanitizeInput($data['name']);

    // Check if category already exists
    $checkQuery = "SELECT COUNT(*) as count FROM categories WHERE name = ?";
    $result = $database->getOne($checkQuery, [$categoryName]);

    if ($result['count'] > 0) {
        sendError('Category already exists');
    }

    // Create new category
    $id = uniqid('cat_', true);
    $query = "INSERT INTO categories (id, name, description) VALUES (?, ?, ?)";
    $database->executeQuery($query, [$id, $categoryName, $data['description'] ?? '']);
    
    sendSuccess('Category created successfully', ['id' => $id, 'name' => $categoryName]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['oldName', 'newName']);
    
    $oldName = sanitizeInput($data['oldName']);
    $newName = sanitizeInput($data['newName']);
    
    // Update all products with this category
    $query = "UPDATE products SET category = ? WHERE category = ?";
    $rowsAffected = $database->update($query, [$newName, $oldName]);
    
    if ($rowsAffected === 0) {
        sendError('Category not found', 404);
    }
    
    sendSuccess('Category updated successfully');
}

function handleDelete($database) {
    $categoryName = $_GET['name'] ?? null;
    
    if (!$categoryName) {
        sendError('Category name is required');
    }
    
    $categoryName = sanitizeInput($categoryName);
    
    // Update all products in this category to 'Uncategorized'
    $query = "UPDATE products SET category = 'Uncategorized' WHERE category = ?";
    $rowsAffected = $database->update($query, [$categoryName]);
    
    if ($rowsAffected === 0) {
        sendError('Category not found', 404);
    }
    
    sendSuccess('Category deleted successfully');
}
?>
