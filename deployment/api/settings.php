<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
        case 'PUT':
            handleUpdate($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Settings API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    // Get all settings from system_settings table
    $query = "SELECT setting_key, setting_value FROM system_settings";
    $settingsRows = $database->getMany($query);

    // Convert to key-value array
    $settings = [];
    foreach ($settingsRows as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Get the highest quote number from bookings
    $quoteQuery = "SELECT MAX(CAST(SUBSTRING(quote_number, 3) AS UNSIGNED)) as max_quote FROM bookings WHERE quote_number LIKE 'Q-%'";
    $quoteResult = $database->getOne($quoteQuery);
    $lastQuoteNumber = $quoteResult ? (int)$quoteResult['max_quote'] : 1000;

    // Default delivery options
    $deliveryOptions = [
        ['id' => 'pickup', 'name' => 'Pickup', 'fee' => 0],
        ['id' => 'delivery', 'name' => 'Delivery', 'fee' => (float)($settings['default_delivery_fee'] ?? 0)]
    ];

    $response = [
        'taxRate' => (float)($settings['tax_rate'] ?? 0),
        'enableTax' => (bool)($settings['tax_rate'] ?? 0 > 0),
        'deliveryOptions' => $deliveryOptions,
        'lastQuoteNumber' => $lastQuoteNumber
    ];

    sendResponse($response);
    return;
    
    $settingsData = $settings['settings_data'] ? json_decode($settings['settings_data'], true) : [];
    
    $response = [
        'taxRate' => (float)$settings['tax_rate'],
        'enableTax' => (bool)$settings['enable_tax'],
        'lastQuoteNumber' => (int)$settings['last_quote_number'],
        'deliveryOptions' => $settingsData['deliveryOptions'] ?? []
    ];
    
    sendResponse($response);
}

function handleUpdate($database) {
    $data = getRequestData();

    // Update individual settings in the key-value structure
    if (isset($data['taxRate'])) {
        $query = "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                  ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $database->executeQuery($query, ['tax_rate', $data['taxRate']]);
    }

    if (isset($data['enableTax'])) {
        $query = "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                  ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $database->executeQuery($query, ['enable_tax', $data['enableTax'] ? '1' : '0']);
    }

    if (isset($data['lastQuoteNumber'])) {
        $query = "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                  ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $database->executeQuery($query, ['last_quote_number', $data['lastQuoteNumber']]);
    }

    sendSuccess('Settings updated successfully');
}
?>
